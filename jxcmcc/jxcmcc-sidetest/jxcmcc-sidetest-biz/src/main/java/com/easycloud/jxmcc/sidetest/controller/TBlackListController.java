/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.sidetest.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.sidetest.entity.TBlackList;
import com.easycloud.jxmcc.sidetest.service.TBlackListService;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 
 *
 * <AUTHOR> code generator
 * @date 2020-11-09 10:55:40
 */
@RestController
@AllArgsConstructor
@RequestMapping("/tblacklist" )
@Api(value = "tblacklist", tags = "管理")
public class TBlackListController {

    private final  TBlackListService tBlackListService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tBlackList
     * @return
     */
    @SneakyThrows
    @SysLog("分页查询")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page" )
    public R getTBlackListPage(Page page, TBlackList tBlackList) {
        return R.ok(tBlackListService.page(page, Wrappers.query(tBlackList)));
    }


    /**
     * 通过id查询
     * @param id id
     * @return R
     */
    @SysLog("通过id查询")
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {

        return R.ok(tBlackListService.getById(id));
    }
    @RequestMapping(value = "/exportTblackList")
    public void queryReceiptList(HttpServletRequest request, HttpServletResponse response, TBlackList tBlackList) {
        List<TBlackList> list = tBlackListService.list(Wrappers.query(tBlackList));
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("list",list);
        //导出Excel
        try{
            TemplateExportParams params = new TemplateExportParams("templates/poi/tblack.xls");
            Workbook workbook= ExcelExportUtil.exportExcel(params,map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 新增
     * @param tBlackList 
     * @return R
     */
    @ApiOperation(value = "新增", notes = "新增")
    @SysLog("新增" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('sidetest_tblacklist_add')" )
    public R save(@RequestBody TBlackList tBlackList) {
        tBlackList.setCreatedBy(SecurityUtils.getUser().getUsername());
        return R.ok(tBlackListService.save(tBlackList));
    }

    /**
     * 修改
     * @param tBlackList 
     * @return R
     */
    @ApiOperation(value = "修改", notes = "修改")
    @SysLog("修改" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('sidetest_tblacklist_edit')" )
    public R updateById(@RequestBody TBlackList tBlackList) {
        return R.ok(tBlackListService.updateById(tBlackList));
    }

    /**
     * 通过id删除
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除", notes = "通过id删除")
    @SysLog("通过id删除" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('sidetest_tblacklist_del')" )
    public R removeById(@PathVariable Long id) {
        return R.ok(tBlackListService.removeById(id));
    }

}
