/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.sidetest.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.constant.SecurityConstants;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.log.util.DemandStateCode;
import com.easycloud.jxcmcc.common.security.annotation.Inner;
import com.easycloud.jxmcc.sidetest.entity.StReModel;
import com.easycloud.jxmcc.sidetest.service.StReModelService;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;


/**
 * 模型管理
 *
 * <AUTHOR>
 * @date 2020-09-02 11:19:14
 */
@RestController
@AllArgsConstructor
@RequestMapping("/stremodel" )
@Api(value = "stremodel", tags = "模型管理管理")
public class StReModelController {

    private final  StReModelService stReModelService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param stReModel 模型管理
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog(value = "模型管理分页查询", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.SELECT)
    @GetMapping("/page" )
    public R getStReModelPage(Page page, StReModel stReModel) {
        stReModel.setEnableFlag(1);
        QueryWrapper<StReModel> query = Wrappers.query(stReModel);
        query.lambda().orderByAsc(StReModel::getOrderNum);
        return R.ok(stReModelService.page(page, query));
    }


    /**
     * 通过id查询模型管理
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog(value = "通过id查询模型管理", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.SELECT)
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(stReModelService.getById(id));
    }

    /**
     * 新增模型管理
     * @param stReModel 模型管理
     * @return R
     */
    @ApiOperation(value = "新增模型管理", notes = "新增模型管理")
    @SysLog(value = "新增模型管理", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.INSERT)
    @PostMapping
    @PreAuthorize("@pms.hasPermission('sidetest_stremodel_add')" )
    public R save(@RequestBody StReModel stReModel) {
        return R.ok(stReModelService.saveModel(stReModel));
    }

    /**
     * 修改模型管理
     * @param stReModel 模型管理
     * @return R
     */
    @ApiOperation(value = "修改模型管理", notes = "修改模型管理")
    @SysLog(value = "修改模型管理", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.UPDATE)
    @PutMapping
    @PreAuthorize("@pms.hasPermission('sidetest_stremodel_edit')" )
    public R updateById(@RequestBody StReModel stReModel) {

        stReModel.setDepFlag(2);
        return R.ok(stReModelService.updateById(stReModel));
    }

    /**
     * 通过id删除模型管理
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除模型管理", notes = "通过id删除模型管理")
    @SysLog(value = "通过id删除模型管理", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.DELETE)
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('sidetest_stremodel_del')" )
    public R removeById(@PathVariable Long id) {
        return R.ok(stReModelService.removeModel(id));
    }


    /**
     * 通过id部署模型
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id部署模型", notes = "通过id部署模型")
    @SysLog(value = "通过部署模型", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.BUSINESS)
    @GetMapping("/deploy/{id}" )
    public R deployById(@PathVariable Long id) {
        return R.ok(stReModelService.deployModel(id));
    }



    /**
     * 列表
     * @param stReModel 列表
     * @param stReModel 模型管理
     * @return
     */
    @SysLog(value = "列表", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.SELECT)
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/list" )
    public R getStReModelPage(StReModel stReModel) {
        stReModel.setEnableFlag(1);
        return R.ok(stReModelService.list(Wrappers.query(stReModel)));
    }

     /**
     * 列表
     * @param pModelId 父级模型id
     * @return
     */
    @ApiOperation(value = "列表", notes = "列表")
    @SysLog(value = "列表", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.SELECT)
    @GetMapping("/list/{pModelId}" )
    public R getStReModelPage(@PathVariable(required = false) Integer pModelId) {

        StReModel stReModel =new StReModel();
        stReModel.setEnableFlag(1);
        stReModel.setParModelId(pModelId);
        return R.ok(stReModelService.list(Wrappers.query(stReModel)));
    }

    /**
     * 通过模型id查询模型运行实例
     * @param modelId modelId
     * @return R
     */
    @Inner()
    @SysLog(value = "通过模型id查询模型运行实例", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.BUSINESS)
    @ApiOperation(value = "通过模型id运行", notes = "通过模型id运行")
    @GetMapping("/run/{modelId}" )
    public R runModelByModelId(@PathVariable("modelId" ) Long modelId,
						 @RequestHeader(SecurityConstants.FROM) String from) {
        return R.ok(stReModelService.runModel(modelId));
    }

}
