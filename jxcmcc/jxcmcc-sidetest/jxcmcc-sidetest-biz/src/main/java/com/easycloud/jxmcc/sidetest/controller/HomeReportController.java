/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.sidetest.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.security.annotation.Inner;
import com.easycloud.jxmcc.sidetest.entity.HomeProductDevelopment;
import com.easycloud.jxmcc.sidetest.service.HomeAccountReportService;
import com.easycloud.jxmcc.sidetest.vo.homebalance.HomeBalanceReportQueryVo;
import com.easycloud.jxmcc.sidetest.vo.homebalance.HomeBalanceReportResultItemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 四库一家报表
 *
 * <AUTHOR>
 * @date 2021-08-31 16:57:40
 */
@RestController
@AllArgsConstructor
@RequestMapping("/homereport" )
@Api(value = "homereport", tags = "四库一家报表")
public class HomeReportController {

    private final HomeAccountReportService reportService;

    /**
     * 报表查询
     * @return
     */
    @ApiOperation(value = "报表查询", notes = "报表查询")
    @SysLog(value = "四库一家报表查询", demandId = "KFXQ-SGS20210820081602")
    @GetMapping("/queryReport" )
    public R getHomeAccountingBalancePage(HomeBalanceReportQueryVo queryVo) {
        return R.ok(reportService.queryReport(queryVo));
    }

    /**
    * @Description: 报表持久化
    * @Param: HomeBalanceReportQueryVo
    * @return: void
    * @Author: lilaishan
    * @Date: 2021/11/29
    */
    @ApiOperation(value = "报表持久化", notes = "报表持久化")
    @SysLog(value = "四库一家报表持久化", demandId = "KFXQ-SGS20210820081602")
    @GetMapping("/dataPersistencev" )
    public R dataPersistencev(HomeBalanceReportQueryVo queryVo) {
        reportService.dataPersistence(queryVo);
        return R.ok();
    }



       /**
     * 导出家宽模型数据
     * @param request
     * @param response
     * @param queryVo
     */
    @RequestMapping(value = "/exportReport")
    @SysLog(value = "导出家宽模型数据", demandId = "KFXQ-SGS20210820081602")
    public void exportApply(HttpServletRequest request,
                            HttpServletResponse response,
                            HomeBalanceReportQueryVo queryVo) {

        List<HomeBalanceReportResultItemVo> reportResultItemVoList = reportService.
                queryReport(queryVo);

        String filePrefix = "exportHome";
        if (StringUtil.isNotBlank(queryVo.getOrgCode())) {
            filePrefix += "-" + queryVo.getOrgCode();
        }

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("data", reportResultItemVoList);
        //导出Excel
        try {
            TemplateExportParams params = new TemplateExportParams("templates/poi/home/"+filePrefix+".xlsx", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 家宽客户发展报表查询
     *
     * @return
     */
    @ApiOperation(value = "家宽客户发展报表查询", notes = "家宽客户发展报表查询")
    @SysLog(value = "家宽客户发展报表查询", demandId = "KFXQ-SGS20210820081602")
    @GetMapping("/queryHomeProductDevelopment")
    public R queryHomeProductDevelopment(HomeBalanceReportQueryVo queryVo) {
        return R.ok(reportService.queryHomeProductDevelopment(queryVo));
    }

    @ApiOperation(value = "家宽客户发展报表导出", notes = "家宽客户发展报表导出")
    @SysLog(value = "家宽客户发展报表导出", demandId = "KFXQ-SGS20210820081602")
    @GetMapping("/exportProductDevelopment")
    public void exportProductDevelopment(HttpServletResponse response,HomeBalanceReportQueryVo queryVo){
        reportService.exportProductDevelopment(response,queryVo);
    }

    @ApiOperation(value = "家庭产品效益标杆查询", notes = "家庭产品效益标杆查询")
    @SysLog(value = "家庭产品效益标杆查询", demandId = "KFXQ-SGS20210820081602")
    @GetMapping("/queryHomeBenchmark")
    public R queryHomeBenchmark(HomeBalanceReportQueryVo queryVo){
        return R.ok(reportService.getHomeProductDevelopmentBenefitsList(queryVo));
    }

    @ApiOperation(value = "导出家庭产品效益标杆", notes = "导出家庭产品效益标杆")
    @SysLog(value = "导出家庭产品效益标杆", demandId = "KFXQ-SGS20210820081602")
    @GetMapping("/exportBenchmarkReport")
    public void exportBenchmarkReport(HttpServletResponse response,HomeBalanceReportQueryVo queryVo){
        reportService.exportBenchmarkReport(response,queryVo);
    }

}
