/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.sidetest.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.sidetest.entity.StRuModel;
import com.easycloud.jxmcc.sidetest.service.StRuModelService;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;


/**
 * 模型运行实例
 *
 * <AUTHOR>
 * @date 2020-09-02 11:19:14
 */
@RestController
@AllArgsConstructor
@RequestMapping("/strumodel" )
@Api(value = "strumodel", tags = "模型运行实例管理")
public class StRuModelController {

    private final  StRuModelService stRuModelService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param stRuModel 模型运行实例
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog(value = "模型运行实例分页查询", demandId = "KFXQ-SGS20210518170127")
    @GetMapping("/page" )
    public R getStRuModelPage(Page page, StRuModel stRuModel) {
        stRuModel.setEnableFlag(1);
        QueryWrapper<StRuModel> query = Wrappers.query(stRuModel);
        query.lambda().orderByAsc(StRuModel::getOrderNum);
        return R.ok(stRuModelService.page(page, query));
    }


    /**
     * 通过id查询模型运行实例
     * @param id id
     * @return R
     */
    @SysLog(value = "通过id查询模型运行实例", demandId = "KFXQ-SGS20210518170127")
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(stRuModelService.getById(id));
    }

    /**
     * 新增模型运行实例
     * @param stRuModel 模型运行实例
     * @return R
     */
    @ApiOperation(value = "新增模型运行实例", notes = "新增模型运行实例")
    @SysLog(value = "新增模型运行实例", demandId = "KFXQ-SGS20210518170127")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('sidetest_strumodel_add')" )
    public R save(@RequestBody StRuModel stRuModel) {
        return R.ok(stRuModelService.save(stRuModel));
    }

    /**
     * 修改模型运行实例
     * @param stRuModel 模型运行实例
     * @return R
     */
    @ApiOperation(value = "修改模型运行实例", notes = "修改模型运行实例")
    @SysLog(value = "修改模型运行实例", demandId = "KFXQ-SGS20210518170127")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('sidetest_strumodel_edit')" )
    public R updateById(@RequestBody StRuModel stRuModel) {
        return R.ok(stRuModelService.updateById(stRuModel));
    }

    /**
     * 通过id删除模型运行实例
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除模型运行实例", notes = "通过id删除模型运行实例")
    @SysLog(value = "通过id删除模型运行实例", demandId = "KFXQ-SGS20210518170127")
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('sidetest_strumodel_del')" )
    public R removeById(@PathVariable Long id) {
        return R.ok(stRuModelService.removeById(id));
    }



      /**
     * 通过id查询模型运行实例
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过Id运行", notes = "通过id运行")
    @SysLog(value = "通过id查询模型运行实例", demandId = "KFXQ-SGS20210518170127")
    @GetMapping("/run/{id}" )
    public R runById(@PathVariable("id" ) Long id) {
        return R.ok(stRuModelService.runModel(id));
    }






}
