package com.easycloud.jxmcc.sidetest.controller;


import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.word.WordExportUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccTemplateExcelConstants;
import com.easycloud.jxcmcc.common.core.poi.execl.view.JxcmccTemplateExcelView;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.sidetest.service.ICashierInfoService;
import com.easycloud.jxmcc.sidetest.vo.CashierInfoVo;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 出纳自动审单报表
 * @Author: fangcy
 * @Date:   2020-04-27
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "出纳自动审单报表")
@RestController
@RequestMapping("cashierInfo")
public class CashierInfoController {

    @Autowired
    private ICashierInfoService cashierInfoService;

    /**
     * 查询出纳审单报表(地市)
     * @return
     */
    @SysLog(value = "查询出纳审单报表(地市)", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表-查询出纳审单报表(地市)", notes = "出纳自动审单报表-查询出纳审单报表(地市)")
    @GetMapping(value = "/queryCashierReportByOrgCode")
    public R queryCashierReportByOrgCode(CashierInfoVo cashierInfoVo) {
        List<Map<String, Object>> resultList = cashierInfoService.queryCashierReportByOrgCode(cashierInfoVo);
        return R.ok(resultList);
    }

    /**
     * 查询出纳审单报表(报账单类型)
     * @return
     */
    @SysLog(value = "查询出纳审单报表(报账单类型)", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表-查询出纳审单报表(报账单类型)", notes = "出纳自动审单报表-查询出纳审单报表(报账单类型)")
    @GetMapping(value = "/queryCashierReportByClaimType")
    public R queryCashierReportByClaimType(CashierInfoVo cashierInfoVo) {
        List<Map<String, Object>> resultList = cashierInfoService.queryCashierReportByClaimType(cashierInfoVo);
        return R.ok(resultList);
    }

    /**
     * 查询出纳审单报表(地市)
     * @version 2.0
     * @return
     */
    @SysLog(value = "查询出纳审单报表(地市)", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表-查询出纳审单报表(地市)", notes = "出纳自动审单报表-查询出纳审单报表(地市)")
    @GetMapping(value = "/queryCashierWithByOrgCode")
    public R queryCashierWithByOrgCode(CashierInfoVo cashierInfoVo) {
        List<Map<String, Object>> resultList = cashierInfoService.queryCashierWithByOrgCode(cashierInfoVo);
        return R.ok(resultList);
    }

    /**
     * 查询出纳审单报表(报账单类型)
     * @version 2.0
     * @return
     */
    @SysLog(value = "查询出纳审单报表(报账单类型)", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表-查询出纳审单报表(报账单类型)", notes = "出纳自动审单报表-查询出纳审单报表(报账单类型)")
    @GetMapping(value = "/queryCashierWithByClaimType")
    public R queryCashierWithByClaimType(CashierInfoVo cashierInfoVo) {
        List<Map<String, Object>> resultList = cashierInfoService.queryCashierWithByClaimType(cashierInfoVo);
        return R.ok(resultList);
    }

    /**
     * 供应商支付排行表
     * @return
     */
    @SysLog(value = "供应商支付排行表", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表-供应商支付排行表", notes = "出纳自动审单报表-供应商支付排行表")
    @GetMapping(value = "/querySupplierPaymentRanking")
    public R querySupplierPaymentRanking(CashierInfoVo cashierInfoVo) {
        List<CashierInfoVo> resultList = cashierInfoService.querySupplierPaymentRanking(cashierInfoVo);
        return R.ok(resultList);
    }

    /**
     * 供应商支付明细
     * @return
     */
    @SysLog(value = "供应商支付明细", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表-供应商支付明细", notes = "出纳自动审单报表-供应商支付明细")
    @GetMapping(value = "/querySupplierPaymentDetails")
    public R querySupplierPaymentDetails(Page page,CashierInfoVo cashierInfoVo) {
        Page<CashierInfoVo> resultList = cashierInfoService.querySupplierPaymentDetails(page,cashierInfoVo);
        return R.ok(resultList);
    }

    /**
     * 支付明细表
     * @return
     */
    @SysLog(value = "出纳自动审单报表-支付明细表", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表-支付明细表", notes = "出纳自动审单报表-支付明细表")
    @GetMapping(value = "/queryPaymentDetails")
    public R queryPaymentDetails(Page page,CashierInfoVo cashierInfoVo) {
        Page<CashierInfoVo> resultList = cashierInfoService.queryPaymentDetails(page,cashierInfoVo);
        return R.ok(resultList);
    }

    /**
     * 支付失败明细表
     * @return
     */
    @SysLog(value = "出纳自动审单报表-支付失败明细表", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表-支付失败明细表", notes = "出纳自动审单报表-支付失败明细表")
    @GetMapping(value = "/queryPaymentFailureDetails")
    public R queryPaymentFailureDetails(Page page,CashierInfoVo cashierInfoVo) {
        Page<CashierInfoVo> resultList = cashierInfoService.queryPaymentFailureDetails(page,cashierInfoVo);
        return R.ok(resultList);
    }

    /**
     * 月度支付信息趋势(全省)
     * @return
     */
    @SysLog(value = "出纳自动审单报表-月度支付信息趋势(全省)", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表-月度支付信息趋势(全省)", notes = "出纳自动审单报表-月度支付信息趋势(全省)")
    @GetMapping(value = "/queryAmountPaidSuccessFullyByMonthAndProvince")
    public R queryAmountPaidSuccessFullyByMonthAndProvince(CashierInfoVo cashierInfoVo) {
        List<Map<String, Object>> resultList = cashierInfoService.queryAmountPaidSuccessFullyByMonthAndProvince(cashierInfoVo);
        return R.ok(resultList);
    }

    /**
     * 月度支付信息趋势(地市)
     * @return
     */
    @SysLog(value = "出纳自动审单报表-月度支付信息趋势(地市)", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表-月度支付信息趋势(地市)", notes = "出纳自动审单报表-月度支付信息趋势(地市)")
    @GetMapping(value = "/queryAmountPaidSuccessFullyByMonthAndCity")
    public R queryAmountPaidSuccessFullyByMonthAndCity(CashierInfoVo cashierInfoVo) {
        List<Map<String, Object>> resultList = cashierInfoService.queryAmountPaidSuccessFullyByMonthAndCity(cashierInfoVo);
        return R.ok(resultList);
    }

    /**
     * 年度支付信息趋势图(全省)
     * @return
     */
    @SysLog(value = "出纳自动审单报表-年度支付信息趋势图(全省)", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表-年度支付信息趋势图(全省)", notes = "出纳自动审单报表-年度支付信息趋势图(全省)")
    @GetMapping(value = "/queryAmountPaidSuccessFullyByYear")
    public R queryAmountPaidSuccessFullyByYear(CashierInfoVo cashierInfoVo) {
        List<Map<String, Object>> resultList = cashierInfoService.queryAmountPaidSuccessFullyByYear(cashierInfoVo);
        return R.ok(resultList);
    }

    /**
     * 出纳支付失败信息(分公司)
     * @return
     */
    @SysLog(value = "出纳自动审单报表报表-出纳支付失败信息(分公司)", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表报表-出纳支付失败信息(分公司)", notes = "出纳自动审单报表报表-出纳支付失败信息(分公司)")
    @GetMapping(value = "/queryErrorWithOrgCode")
    public R queryErrorWithOrgCode(
            @RequestParam(name = "beginDate", required = false) String beginDate,
            @RequestParam(name = "endDate", required = false) String endDate) {

        List<Map<String, Object>> resultList = cashierInfoService.queryErrorWithOrgCode(beginDate,endDate);
        return R.ok(resultList);
    }

    /**
     * 出纳支付失败信息(报账单类型)
     * @return
     */
    @SysLog(value = "出纳自动审单报表报表-出纳支付失败信息(报账单类型)", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表报表-出纳支付失败信息(报账单类型)", notes = "出纳自动审单报表报表-出纳支付失败信息(报账单类型)")
    @GetMapping(value = "/queryErrorWithClaimType")
    public R queryErrorWithClaimType(
            @RequestParam(name = "beginDate", required = false) String beginDate,
            @RequestParam(name = "endDate", required = false) String endDate) {

        List<Map<String, Object>> resultList = cashierInfoService.queryErrorWithClaimType(beginDate,endDate);
        return R.ok(resultList);
    }




    /**
     * 出纳支付失败信息(分公司) v2.0
     * @return
     */
    @SysLog(value = "出纳自动审单报表报表-出纳支付失败信息", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表报表-出纳支付失败信息", notes = "出纳自动审单报表报表-出纳支付失败信息")
    @GetMapping(value = "/queryErrorReportByOrgCode")
    public R queryErrorReportByOrgCode(
            @RequestParam(name = "beginDate", required = false) String beginDate,
            @RequestParam(name = "endDate", required = false) String endDate) {
        beginDate = StringUtils.isNotBlank(beginDate)?beginDate+" 00:00:00":"2020-01-01";
        endDate = StringUtils.isNotBlank(endDate)?endDate+" 23:59:59":"2020-01-01";
        List<Map<String, Object>> resultList = cashierInfoService.queryErrorReportByOrgCode(beginDate,endDate);
        return R.ok(resultList);
    }

    /**
     * 出纳支付失败信息(报账单类型)v2.0
     * @return
     */
    @SysLog(value = "出纳自动审单报表报表-出纳支付失败信息", demandId = "KFXQ-SGS20210526095656")
    @ApiOperation(value = "出纳自动审单报表报表-出纳支付失败信息", notes = "出纳自动审单报表报表-出纳支付失败信息")
    @GetMapping(value = "/queryErrorReportByClaimtType")
    public R queryErrorReportByClaimType(
            @RequestParam(name = "beginDate", required = false) String beginDate,
            @RequestParam(name = "endDate", required = false) String endDate) {
        beginDate = StringUtils.isNotBlank(beginDate)?beginDate+" 00:00:00":"2020-01-01";
        endDate = StringUtils.isNotBlank(endDate)?endDate+" 23:59:59":"2020-01-01";
        List<Map<String, Object>> resultList = cashierInfoService.queryErrorReportByClaimtType(beginDate,endDate);
        return R.ok(resultList);
    }



    /**
     * 导出excel
     *
     * @param request
     */
    @SysLog(value = "导出excel", demandId = "KFXQ-SGS20210526095656")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request,@RequestParam(name = "beginDate", required = false) String beginDate,@RequestParam(name = "endDate", required = false) String endDate) {

        List<Map<String, Object>> list = cashierInfoService.queryCashierReportXls(beginDate,endDate);
        Map<String,Object> map = new HashMap<String, Object>(null);
        map.put("list", list);
        //导出Excel
        ModelAndView mv = new ModelAndView(new JxcmccTemplateExcelView());
        //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(JxcmccTemplateExcelConstants.FILE_NAME, "测试报表");
        mv.addObject(JxcmccTemplateExcelConstants.PARAMS, new TemplateExportParams("templates/xls/cashierReport.xlsx",true) );
        mv.addObject(JxcmccTemplateExcelConstants.MAP_DATA, map);
        return mv;
    }
}

