package com.easycloud.jxmcc.sidetest.exception;

import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * <AUTHOR> se
 * @description 导入表格异常类
 * @create 2020-12-14 18:10
 **/
public class ExcelImportException extends RuntimeException {

    private List<T> failList;

    public List<T> getFailList() {
        return failList;
    }

    public ExcelImportException() {
        super();
    }

    public ExcelImportException(List failList) {
        super();
        this.failList = failList;
    }
    public  ExcelImportException(String message,List failList) {
        super(message);
        this.failList = failList;
    }
    public  ExcelImportException(String message,List failList, Throwable cause) {
        super(message,cause);
        this.failList = failList;
    }
}
