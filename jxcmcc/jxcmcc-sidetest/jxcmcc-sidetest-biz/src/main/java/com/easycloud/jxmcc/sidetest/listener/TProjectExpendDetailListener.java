package com.easycloud.jxmcc.sidetest.listener;

import cn.hutool.extra.spring.SpringUtil;
import com.easycloud.jxmcc.sidetest.entity.TProjectExpendDeatil;
import com.easycloud.jxmcc.sidetest.service.TProjectExpendDeatilService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;

/**
 * 项目明细报表源数据
 */
@Slf4j
public class TProjectExpendDetailListener implements Callable<Map<String, String>> {


    /**
     * 线程信号源
     */
    private Semaphore semaphore;

    private CountDownLatch countDownLatch;

    private List<TProjectExpendDeatil> csvReaders;

    private String username;

    private String company;

    public TProjectExpendDetailListener(Semaphore semaphore,
                                   CountDownLatch countDownLatch,
                                   String username,
                                   String company,
                                   List<TProjectExpendDeatil> csvReaders
    ) {
        this.semaphore = semaphore;
        this.countDownLatch = countDownLatch;
        this.username = username;
        this.company = company;
        this.csvReaders = csvReaders;
    }

    @Override
    public Map<String, String> call() throws Exception {
        Map<String, String> map = new HashMap<>();
        try {
            log.info("线程ID：<{}>开始运行", Thread.currentThread().getId());
            semaphore.acquire();
            log.info("消耗了一个信号量，剩余信号量为:{}", semaphore.availablePermits());
            countDownLatch.countDown();
            SpringUtil.getBean(TProjectExpendDeatilService.class).addBathList(this.csvReaders,this.username,this.company);
        } catch (Exception e) {
            throw e;
        } finally {
            semaphore.release();
        }
        map.put("status","true");
        map.put("msg","解析成功");
        map.put("count",this.csvReaders.size()+"");
        return map;
    }
}
