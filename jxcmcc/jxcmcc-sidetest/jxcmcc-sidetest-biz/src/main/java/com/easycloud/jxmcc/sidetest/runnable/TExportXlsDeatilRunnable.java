package com.easycloud.jxmcc.sidetest.runnable;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxmcc.sidetest.entity.TExportXlsDeatil;
import com.easycloud.jxmcc.sidetest.util.CsvHelper;
import com.easycloud.jxmcc.sidetest.vo.TExportXlsDeatilVo;
import lombok.SneakyThrows;

import java.io.File;
import java.util.List;

public class TExportXlsDeatilRunnable implements Runnable {

  private ServiceImpl service;

  private TExportXlsDeatilVo item;

  private TExportXlsDeatil tExportXlsDeatil;

  private String path;

  private List<File> fileList;

  public TExportXlsDeatilRunnable(
      ServiceImpl service,
      TExportXlsDeatilVo item,
      TExportXlsDeatil tExportXlsDeatil,
      String path,
      List<File> fileList) {
    this.service = service;
    this.item = item;
    this.tExportXlsDeatil = tExportXlsDeatil;
    this.path = path;
    this.fileList = fileList;
  }

  @SneakyThrows
  @Override
  public void run() {
    List list = service.list(item.getQueryWrapper());
    String fileName =
        ObjectUtil.isNotNull(item.getDate())
            ? item.getDate() + ".csv"
            : tExportXlsDeatil.getExportTypeName() + "报表.csv";
    String filePath = path + fileName;
    fileList.add(new File(filePath));
    CsvHelper.writeBeanToCsvFile(filePath, list);
  }
}
