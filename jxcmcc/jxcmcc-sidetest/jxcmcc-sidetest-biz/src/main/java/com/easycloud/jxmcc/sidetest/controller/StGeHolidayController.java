/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.sidetest.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.sidetest.entity.StGeHoliday;
import com.easycloud.jxmcc.sidetest.service.StGeHolidayService;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;


/**
 * 旁侧节假日信息
 *
 * <AUTHOR>
 * @date 2020-10-22 14:27:05
 */
@RestController
@AllArgsConstructor
@RequestMapping("/stgeholiday" )
@Api(value = "stgeholiday", tags = "旁侧节假日信息管理")
public class StGeHolidayController {

    private final  StGeHolidayService stGeHolidayService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param stGeHoliday 旁侧节假日信息
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog(value = "旁侧节假日信息分页查询", demandId = "KFXQ-SGS20210518170127")
    @GetMapping("/page" )
    public R getStGeHolidayPage(Page page, StGeHoliday stGeHoliday) {
        QueryWrapper<StGeHoliday> query = Wrappers.query(stGeHoliday);
        query.lambda().orderByDesc(StGeHoliday::getDates);
        return R.ok(stGeHolidayService.page(page, query));
    }

    /**
     * 通过id查询旁侧节假日信息
     * @param id id
     * @return R
     */
    @SysLog(value = "通过id查询旁侧节假日信息", demandId = "KFXQ-SGS20210518170127")
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(stGeHolidayService.getById(id));
    }

    /**
     * 新增旁侧节假日信息
     * @param stGeHoliday 旁侧节假日信息
     * @return R
     */
    @ApiOperation(value = "新增旁侧节假日信息", notes = "新增旁侧节假日信息")
    @SysLog(value = "新增旁侧节假日信息", demandId = "KFXQ-SGS20210518170127")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('sidetest_stgeholiday_add')" )
    public R save(@RequestBody StGeHoliday stGeHoliday) {

        stGeHoliday.setCreateTime(DateUtil.date());
        return R.ok(

                stGeHolidayService.save(stGeHoliday));
    }

    /**
     * 修改旁侧节假日信息
     * @param stGeHoliday 旁侧节假日信息
     * @return R
     */
    @ApiOperation(value = "修改旁侧节假日信息", notes = "修改旁侧节假日信息")
    @SysLog(value = "修改旁侧节假日信息", demandId = "KFXQ-SGS20210518170127")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('sidetest_stgeholiday_edit')" )
    public R updateById(@RequestBody StGeHoliday stGeHoliday) {
        return R.ok(stGeHolidayService.updateById(stGeHoliday));
    }

    /**
     * 通过id删除旁侧节假日信息
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除旁侧节假日信息", notes = "通过id删除旁侧节假日信息")
    @SysLog(value = "通过id删除旁侧节假日信息", demandId = "KFXQ-SGS20210518170127")
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('sidetest_stgeholiday_del')" )
    public R removeById(@PathVariable Long id) {
        return R.ok(stGeHolidayService.removeById(id));
    }

}
