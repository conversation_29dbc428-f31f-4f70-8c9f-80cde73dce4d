/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.sidetest.controller;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.security.annotation.Inner;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxcmcc.sidetest.api.vo.FileIssueStParam;
import com.easycloud.jxmcc.sidetest.entity.StHiData;
import com.easycloud.jxmcc.sidetest.service.StHiDataService;
import com.easycloud.jxmcc.sidetest.vo.StHiDataVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.*;


/**
 * 旁侧检测到的错误数据
 *
 * <AUTHOR>
 * @date 2020-09-02 11:19:14
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sthidata" )
@Api(value = "sthidata", tags = "旁侧检测到的错误数据管理")
public class StHiDataController {

    private final  StHiDataService stHiDataService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param stHiDataVo 旁侧检测到的错误数据
     * @return
     */
    @SysLog(value = "旁侧检测到的错误数据分页查询", demandId = "KFXQ-SGS20210518170127")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page" )
    public R getStHiDataPage(Page page, StHiDataVo stHiDataVo) {


        return R.ok(stHiDataService.queryPageList(page,stHiDataVo));
    }
    /**
     * 旁侧检测到的错误数据信息导出
     */
    @SysLog(value = "旁侧检测到的错误数据信息导出", demandId = "KFXQ-SGS20210518170127")
    @RequestMapping(value = "/exportStHiDataList")
    public void queryReceiptList(HttpServletRequest request, HttpServletResponse response,StHiDataVo stHiDataVo) throws ParseException {
        //登陆人验证
        JxcmccUser sysUser = SecurityUtils.getUser();
        if(StrUtil.isBlank(stHiDataVo.getOrgCode()) && !StringUtils.equals("303710",sysUser.getOrgCode()))
        {
            stHiDataVo.setOrgCode(sysUser.getOrgCode());
        }
        List<StHiDataVo> list = stHiDataService.exportStHiDataList(stHiDataVo);
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("list",list);
        //导出Excel
        try{
            TemplateExportParams params = new TemplateExportParams("templates/poi/sthidata.xls");
            Workbook workbook= ExcelExportUtil.exportExcel(params,map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 通过id查询旁侧检测到的错误数据
     * @param id id
     * @return R
     */
    @SysLog(value = "通过id查询旁侧检测到的错误数据", demandId = "KFXQ-SGS20210518170127")
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(stHiDataService.getById(id));
    }

    /**
     * 新增旁侧检测到的错误数据
     * @param stHiData 旁侧检测到的错误数据
     * @return R
     */
    @ApiOperation(value = "新增旁侧检测到的错误数据", notes = "新增旁侧检测到的错误数据")
    @SysLog(value = "新增旁侧检测到的错误数据", demandId = "KFXQ-SGS20210518170127")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('sidetest_sthidata_add')" )
    public R save(@RequestBody StHiData stHiData) {
        return R.ok(stHiDataService.save(stHiData));
    }

    /**
     * 修改旁侧检测到的错误数据
     * @param stHiData 旁侧检测到的错误数据
     * @return R
     */
    @ApiOperation(value = "修改旁侧检测到的错误数据", notes = "修改旁侧检测到的错误数据")
    @SysLog(value = "修改旁侧检测到的错误数据", demandId = "KFXQ-SGS20210518170127")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('sidetest_sthidata_edit')" )
    public R updateById(@RequestBody StHiData stHiData) {
        return R.ok(stHiDataService.updateById(stHiData));
    }

    /**
     * 通过id删除旁侧检测到的错误数据
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除旁侧检测到的错误数据", notes = "通过id删除旁侧检测到的错误数据")
    @SysLog(value = "通过id删除旁侧检测到的错误数据", demandId = "KFXQ-SGS20210518170127")
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('sidetest_sthidata_del')" )
    public R removeById(@PathVariable Long id) {
        return R.ok(stHiDataService.removeById(id));
    }


    /**
     * 生成差旅检测模型数据
     *
     * @param param 查询参数
     * @return R
     */
    @ApiOperation(value = "生成差旅检测模型数据", notes = "生成差旅检测模型数据")
    @SysLog(value = "生成差旅检测模型数据", demandId = "KFXQ-SGS20210518170127")
    @Inner(value = false)
    @PostMapping("/genTravelCheckDataFile")
    public R<Boolean> genTravelCheckDataFile(@RequestBody  FileIssueStParam param) {
        return stHiDataService.genTravelCheckDataFile(param);
    }

}
