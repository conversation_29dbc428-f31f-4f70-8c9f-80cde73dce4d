/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.sidetest.controller.currentaccount;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.log.util.DemandStateCode;
import com.easycloud.jxmcc.sidetest.entity.currentaccount.*;
import com.easycloud.jxmcc.sidetest.service.currentaccount.CurrentacCountCurrencyService;
import com.easycloud.jxmcc.sidetest.service.currentaccount.TCurrentAccountClearingService;
import com.easycloud.jxmcc.sidetest.service.currentaccount.TDepartmentCurrentAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 部门内部往来清单
 *
 * <AUTHOR>
 * @date 2021-09-09 09:18:30
 */
@RestController
@AllArgsConstructor
@RequestMapping("/tdepartmentcurrentaccount" )
@Api(value = "tdepartmentcurrentaccount", tags = "部门内部往来清单")
public class TDepartmentCurrentAccountController {

    protected final Logger logger = LoggerFactory.getLogger(TDepartmentCurrentAccountController.class);

    private final TDepartmentCurrentAccountService tDepartmentCurrentAccountService;

    /**
     * 分页查询应付
     * @param page 分页对象
     * @param tCurrentAccountClearing 内部往来清单
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog(value = "部门内部往来清单分页查询", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.SELECT)
    @GetMapping("/page" )
    public R getTDepartmentCurrentAccountPage(Page page, TCurrentAccountClearing tCurrentAccountClearing ,String step) {
        IPage pages = new Page<TCurrentAccountClearing>(page.getCurrent(), page.getSize());

        return R.ok(tDepartmentCurrentAccountService.queryCopewithPage(pages,tCurrentAccountClearing,step));
    }


    /**
     * 分页查询应收
     * @param page 分页对象
     * @param tCurrentAccountReceivable 应收内部往来清单
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog(value = "应收内部往来清单分页查询", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.SELECT)
    @GetMapping("/receivablePage" )
    public R getReceivablePage(Page page, TCurrentAccountReceivable tCurrentAccountReceivable,String step) {
        IPage pages = new Page<TCurrentAccountReceivable>(page.getCurrent(), page.getSize());

        return R.ok(tDepartmentCurrentAccountService.queryReceivablePage(pages,tCurrentAccountReceivable,step));
    }


    /**
     * 分页查询 预付款
     * @param page 分页对象
     * @param tCurrentAccountAdvancecharge 预付款内部往来清单
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog(value = "预付款内部往来清单分页查询", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.SELECT)
    @GetMapping("/advancechargePage" )
    public R getAdvancechargePage(Page page, TCurrentAccountAdvancecharge tCurrentAccountAdvancecharge,String step) {
        IPage pages = new Page<TCurrentAccountAdvancecharge>(page.getCurrent(), page.getSize());

        return R.ok(tDepartmentCurrentAccountService.queryAdvancechargePage(pages,tCurrentAccountAdvancecharge,step));
    }


    /**
     * 通过id查询内部往来清单
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog(value = "通过id查询内部往来清单", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.SELECT)
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(tDepartmentCurrentAccountService.getById(id));
    }

    /**
     * 新增内部往来清单
     * @param tCurrentAccountClearing 内部往来清单
     * @return R
     */
    @ApiOperation(value = "新增内部往来清单", notes = "新增内部往来清单")
    @SysLog(value = "新增内部往来清单", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.INSERT)
    @PostMapping
    @PreAuthorize("@pms.hasPermission('sidetest_tdepartmentcurrentaccount_add')" )
    public R save(@RequestBody TCurrentAccountClearing tCurrentAccountClearing) {
        return R.ok(tDepartmentCurrentAccountService.save(tCurrentAccountClearing));
    }

    /**
     * 修改内部往来清单
     * @param tCurrentAccountClearing 内部往来清单
     * @return R
     */
    @ApiOperation(value = "修改内部往来清单", notes = "修改内部往来清单")
    @SysLog(value = "修改内部往来清单", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.UPDATE)
    @PutMapping
    public R updateById(@RequestBody TCurrentAccountClearing tCurrentAccountClearing) {
        if (StringUtils.isNotEmpty(tCurrentAccountClearing.getStep())){
            if ("step2".equals(tCurrentAccountClearing.getStep())){
                if ("false".equals(tCurrentAccountClearing.getDataApprovalStatus()) || "true".equals(tCurrentAccountClearing.getDataApprovalStatus())){
                    tCurrentAccountClearing.setClaimStatus("step2");
                }else {
                    tCurrentAccountClearing.setClaimStatus("step3");
                }
            }else if ("step3".equals(tCurrentAccountClearing.getStep())){
                if ("false".equals(tCurrentAccountClearing.getDataApprovalStatus())){
                    tCurrentAccountClearing.setClaimStatus("step2");
                }else {
                    tCurrentAccountClearing.setClaimStatus("step3");
                }
            }
            //如果在流程中判断当前填写的清理完成时间是否小于流程开启时间
            if (ObjectUtil.isNotEmpty(tCurrentAccountClearing.getCleanUpDate())){
                boolean b = SpringUtil.getBean(CurrentacCountCurrencyService.class).verifyTime(tCurrentAccountClearing.getBillingPeriod(), tCurrentAccountClearing.getCleanUpDate());
                if (!b){
                    return  R.error("更新失败，当前填写的清理完成时间("+tCurrentAccountClearing.getCleanUpDate()+")必须大于流程下发时间("+tCurrentAccountClearing.getBillingPeriod()+"),请修改！");
                }
            }

        }
        return R.ok(tDepartmentCurrentAccountService.updateById(tCurrentAccountClearing));
    }

    /**
     * 通过id删除内部往来清单
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除内部往来清单", notes = "通过id删除内部往来清单")
    @SysLog(value = "通过id删除内部往来清单", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.DELETE)
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('sidetest_tdepartmentcurrentaccount_del')" )
    public R removeById(@PathVariable Integer id) {
        return R.ok(tDepartmentCurrentAccountService.removeById(id));
    }


    /**
     * 批量更新应付部门内部往来清单
     * @return R
     */
    @ApiOperation(value = "批量更新应付部门内部往来清单", notes = "批量更新应付部门内部往来清单")
    @SysLog(value = "批量更新应付部门内部往来清单", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.UPDATE)
    @RequestMapping(value = "/importCopewithExcel",method = RequestMethod.POST)
    public R importCopewithExcel(MultipartFile file, ContactsImportParams contactsImportParams) throws Exception{
        return  tDepartmentCurrentAccountService.updateCopewithDepartment(file,contactsImportParams);
    }


    /**
     * 批量更新应收部门内部往来清单
     * @return R
     */
    @ApiOperation(value = "批量更新应收部门内部往来清单", notes = "批量更新应收部门内部往来清单")
    @SysLog(value = "批量更新应收部门内部往来清单", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.UPDATE)
    @RequestMapping(value = "/importReceivableExcel",method = RequestMethod.POST)
    public R importReceivableExcel(MultipartFile file, ContactsImportParams contactsImportParams) throws Exception{
        return  tDepartmentCurrentAccountService.updateReceivableDepartment(file,contactsImportParams);
    }


    /**
     * 批量更新预付款部门内部往来清单
     * @return R
     */
    @ApiOperation(value = "批量更新预付款部门内部往来清单", notes = "批量更新预付款部门内部往来清单")
    @SysLog(value = "批量更新预付款部门内部往来清单", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.UPDATE)
    @RequestMapping(value = "/importAdvancechargeExcel",method = RequestMethod.POST)
    public R importAdvancechargeExcel(MultipartFile file, ContactsImportParams contactsImportParams) throws Exception{
        return  tDepartmentCurrentAccountService.updateAdvancechargeDepartment(file,contactsImportParams);
    }


    /**
     * 导出应付往来清单列表
     * @param tCurrentAccountClearing
     *@retutn
     *@create 2021/8/19 16:41
     */
    @ApiOperation(value = "导出应付往来清单列表", notes = "导出应付往来清单列表")
    @SysLog(value = "导出应付往来清单列表", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.EXPORT)
    @RequestMapping(value = "/exportCopewithContacts")
    public void exportCopewithContacts(HttpServletResponse response, TCurrentAccountClearing tCurrentAccountClearing,String step){
        List<TCurrentAccountClearing> tCurrentAccountClearings = tDepartmentCurrentAccountService.exportCopewithContacts(tCurrentAccountClearing,step);
        Map<String,Object> map = new HashMap();
        map.put("list",tCurrentAccountClearings);
        try{
            TemplateExportParams params = new TemplateExportParams("templates/poi/cityContactsChecklist.xlsx",true);
            Workbook workbook = ExcelExportUtil.exportExcel(params,map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        }catch (Exception e) {
            e.printStackTrace();
            logger.error("数据导出失败：", e);
        }
    }


    /**
     * 导出应收往来清单列表
     * @param tCurrentAccountReceivable
     *@retutn
     *@create 2021/8/19 16:41
     */
    @ApiOperation(value = "导出应收往来清单列表", notes = "导出应收往来清单列表")
    @SysLog(value = "导出应收往来清单列表", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.EXPORT)
    @RequestMapping(value = "/exportReceivableContacts")
    public void exportReceivableContacts(HttpServletResponse response, TCurrentAccountReceivable tCurrentAccountReceivable,String step){
        List<TCurrentAccountReceivable> tCurrentAccountReceivables = tDepartmentCurrentAccountService.exportReceivableContacts(tCurrentAccountReceivable,step);
        Map<String,Object> map = new HashMap();
        map.put("list",tCurrentAccountReceivables);
        try{
            TemplateExportParams params = new TemplateExportParams("templates/poi/receivable.xlsx",true);
            Workbook workbook = ExcelExportUtil.exportExcel(params,map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        }catch (Exception e) {
            e.printStackTrace();
            logger.error("数据导出失败：", e);
        }
    }

    /**
     * 导出预付款往来清单列表
     * @param tCurrentAccountAdvancecharge
     *@retutn
     *@create 2021/8/19 16:41
     */
    @ApiOperation(value = "导出预付款往来清单列表", notes = "导出预付款往来清单列表")
    @SysLog(value = "导出预付款往来清单列表", demandId = "KFXQ-SGS20200930095311", demandNo = "**************", operate = DemandStateCode.EXPORT)
    @RequestMapping(value = "/exportAdvancechargeContacts")
    public void exportAdvancechargeContacts(HttpServletResponse response, TCurrentAccountAdvancecharge tCurrentAccountAdvancecharge,String step){
        List<TCurrentAccountAdvancecharge> tCurrentAccountAdvancecharges = tDepartmentCurrentAccountService.exportAdvancechargeContacts(tCurrentAccountAdvancecharge,step);
        Map<String,Object> map = new HashMap();
        map.put("list",tCurrentAccountAdvancecharges);
        try{
            TemplateExportParams params = new TemplateExportParams("templates/poi/advancecharge.xlsx",true);
            Workbook workbook = ExcelExportUtil.exportExcel(params,map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        }catch (Exception e) {
            e.printStackTrace();
            logger.error("数据导出失败：", e);
        }
    }

    /**
     * 应付模板下载
     * @return R
     */
    @ApiOperation(value = "应付模板下载", notes = "应付模板下载")
    @SysLog(value = "应付模板下载", demandNo = "**************", operate = DemandStateCode.EXPORT)
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/apTemplate.xlsx", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
