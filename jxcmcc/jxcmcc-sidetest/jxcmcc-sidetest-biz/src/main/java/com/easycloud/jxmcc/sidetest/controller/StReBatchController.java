/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.sidetest.controller;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.log.util.DemandStateCode;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.sidetest.common.DataSourceConstant;
import com.easycloud.jxmcc.sidetest.entity.StReBatch;
import com.easycloud.jxmcc.sidetest.service.StReBatchService;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Date;


/**
 * 导入批次表 导入批次表
 *
 * <AUTHOR> code generator
 * @date 2020-12-11 19:08:31
 */
@RestController
@AllArgsConstructor
@RequestMapping("/strebatch" )
@Api(value = "strebatch", tags = "导入批次表 导入批次表管理")
@DS(value = DataSourceConstant.BUSINESS_DB)
public class StReBatchController {

    private final  StReBatchService stReBatchService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param stReBatch 导入批次表 导入批次表
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog(value = "导入批次表分页查询", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.SELECT)
    @GetMapping("/page" )
    public R getStReBatchPage(Page page, StReBatch stReBatch) {
        return R.ok(stReBatchService.page(page, Wrappers.query(stReBatch)));
    }


    /**
     * 通过id查询导入批次表 导入批次表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog(value = "通过id查询导入批次表", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.SELECT)
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(stReBatchService.getById(id));
    }

    /**
     * 新增导入批次表 导入批次表
     * @param stReBatch 导入批次表 导入批次表
     * @return R
     */
    @ApiOperation(value = "新增导入批次表 导入批次表", notes = "新增导入批次表 导入批次表")
    @SysLog(value = "新增导入批次表", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.IMPORT)
    @PostMapping
    @PreAuthorize("@pms.hasPermission('sidetest_strebatch_add')" )
    public R save(@RequestBody StReBatch stReBatch) {
        String usetName = SecurityUtils.getUser().getUsername();
        stReBatch.setCreatedBy(usetName);
        stReBatch.setCreatedTime1(new Date());
        if(stReBatch.getState()==null){
            stReBatch.setState(0);
        }
        stReBatch.setUpdatedBy1(usetName);
        stReBatch.setUpdatedTime1(new Date());
        return R.ok(stReBatchService.save(stReBatch));
    }

    /**
     * 修改导入批次表 导入批次表
     * @param stReBatch 导入批次表 导入批次表
     * @return R
     */
    @ApiOperation(value = "修改导入批次表 导入批次表", notes = "修改导入批次表 导入批次表")
    @SysLog(value = "修改导入批次表", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.UPDATE)
    @PutMapping
    @PreAuthorize("@pms.hasPermission('sidetest_strebatch_edit')" )
    public R updateById(@RequestBody StReBatch stReBatch) {
        String usetName = SecurityUtils.getUser().getUsername();
        stReBatch.setUpdatedBy1(usetName);
        stReBatch.setUpdatedTime1(new Date());
        return R.ok(stReBatchService.updateById(stReBatch));
    }

    /**
     * 通过id删除导入批次表 导入批次表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除导入批次表 导入批次表", notes = "通过id删除导入批次表 导入批次表")
    @SysLog(value = "通过id删除导入批次表", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.DELETE)
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('sidetest_strebatch_del')" )
    public R removeById(@PathVariable Integer id) {
        return R.ok(stReBatchService.removeById(id));
    }


    /*查询所有导入批次数据
    * create by zc
    * 2020-12-12
    * */
    @ApiOperation(value = "查询所有导入批次数据", notes = "查询所有导入批次数据")
    @SysLog(value = "查询所有导入批次数据", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.BUSINESS)
    @RequestMapping("/stReBathList" )
    public R getStReBathList(StReBatch stReBatch) {
        stReBatch.setState(0);
        LambdaQueryWrapper<StReBatch> stReBatchLambdaQueryWrapper = Wrappers.query(stReBatch).
                lambda().
                orderByDesc(StReBatch::getCreatedTime1);
        return R.ok(stReBatchService.list(stReBatchLambdaQueryWrapper));
    }

    /*根据批次号去查 没有则更新
     * create by zc
     * 2020-12-16
     * */
    @ApiOperation(value = "根据批次号去查 没有则更新", notes = "根据批次号去查 没有则更新")
    @SysLog(value = "根据批次号去查 没有则更新", demandId = "KFXQ-SGS20210518170127", demandNo = "20230421000027", operate = DemandStateCode.BUSINESS)
    @RequestMapping("/updateStReBath" )
    public R updateStReBath(StReBatch stReBatch) {
        int count = stReBatchService.count(Wrappers.query(stReBatch));
        if(count<=0){
            String usetName = SecurityUtils.getUser().getUsername();
            stReBatch.setCreatedBy(usetName);
            stReBatch.setCreatedTime1(new Date());
            stReBatch.setState(0);
            stReBatch.setUpdatedBy1(usetName);
            stReBatch.setUpdatedTime1(new Date());
            stReBatchService.save(stReBatch);
        }
        return R.ok();
    }

}
