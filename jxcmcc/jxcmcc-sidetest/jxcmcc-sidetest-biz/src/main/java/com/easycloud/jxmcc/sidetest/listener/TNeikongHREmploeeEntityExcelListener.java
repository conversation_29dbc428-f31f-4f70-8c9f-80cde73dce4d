package com.easycloud.jxmcc.sidetest.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.easycloud.jxmcc.sidetest.entity.TNeikongHREmploeeEntity;
import com.easycloud.jxmcc.sidetest.service.TNeikongHREmploeeService;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 人力资源数据导入监听器
 */
public class TNeikongHREmploeeEntityExcelListener extends AnalysisEventListener<TNeikongHREmploeeEntity> {
    private TNeikongHREmploeeService tNeikongHREmploeeService;
    private List<TNeikongHREmploeeEntity> dataList = new ArrayList<>();
    private static final int BATCH_COUNT = 3000; // 每批次处理的数据量
    public TNeikongHREmploeeEntityExcelListener(TNeikongHREmploeeService tNeikongHREmploeeService){
        this.tNeikongHREmploeeService = tNeikongHREmploeeService;
    }


    // 每解析一行都会调用此方法
    @Override
    public void invoke(TNeikongHREmploeeEntity data, AnalysisContext context) {
        data.setAttribute1(StringUtils.lowerCase(data.getAttribute1()));
        data.setAttribute3(data.getAttribute1().substring(0, data.getAttribute1().indexOf("@")));
        dataList.add(data);
        if (dataList.size() >= BATCH_COUNT) {
            saveData(); // 处理数据
        }
    }
    private void saveData() {
        // 将数据保存到数据库或其他存储中
        tNeikongHREmploeeService.saveBatch(dataList);
        dataList.clear();
    }
    // 所有数据解析完成会调用此方法
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 数据处理完成后的操作
        saveData();
    }

    public List<TNeikongHREmploeeEntity> getDataList() {
        return dataList;
    }
}
