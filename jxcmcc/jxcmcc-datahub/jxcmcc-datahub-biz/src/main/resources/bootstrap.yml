server:
  port: 8982

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: nacos
      password: Jxcmcc#2022@azcv
      discovery:
        server-addr: ${NACOS_HOST:jxcmcc-register}:${NACOS_PORT:8848}
        metadata:
          -VERSION: HHL
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - soaConfig.properties
          - systemConfig.properties
          - xxl-job-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  autoconfigure:
    exclude: org.springframework.cloud.gateway.config.GatewayAutoConfiguration,org.springframework.cloud.gateway.config.GatewayClassPathWarningAutoConfiguration
  profiles:
    active: @profiles.active@
