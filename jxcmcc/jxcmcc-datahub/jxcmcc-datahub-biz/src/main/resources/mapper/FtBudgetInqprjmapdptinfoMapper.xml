<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, jxcmcc All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: jxcmcc
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.easycloud.jxcmcc.datahub.mapper.FtBudgetInqprjmapdptinfoMapper">

  <resultMap id="ftBudgetInqprjmapdptinfoMap" type="com.easycloud.jxcmcc.datahub.entity.FtBudgetInqprjmapdptinfo">
                  <id property="id" column="ID"/>
                        <result property="projectNum" column="PROJECT_NUM"/>
                        <result property="projectName" column="PROJECT_NAME"/>
                        <result property="companyCode" column="COMPANY_CODE"/>
                        <result property="companyName" column="COMPANY_NAME"/>
                        <result property="operateDeptCode" column="OPERATE_DEPT_CODE"/>
                        <result property="operateDeptName" column="OPERATE_DEPT_NAME"/>
                        <result property="costCenterCode" column="COST_CENTER_CODE"/>
                        <result property="costCenterName" column="COST_CENTER_NAME"/>
                        <result property="startDate" column="START_DATE"/>
                        <result property="endDate" column="END_DATE"/>
                        <result property="enabledFlag" column="ENABLED_FLAG"/>
                        <result property="lastUpdateDate" column="LAST_UPDATE_DATE"/>
            </resultMap>
</mapper>
