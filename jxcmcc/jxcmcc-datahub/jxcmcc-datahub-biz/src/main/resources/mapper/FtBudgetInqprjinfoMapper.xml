<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, jxcmcc All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: jxcmcc
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.easycloud.jxcmcc.datahub.mapper.FtBudgetInqprjinfoMapper">

  <resultMap id="ftBudgetInqprjinfoMap" type="com.easycloud.jxcmcc.datahub.entity.FtBudgetInqprjinfo">
                  <id property="id" column="ID"/>
                        <result property="year" column="YEAR"/>
                        <result property="projectNum" column="PROJECT_NUM"/>
                        <result property="projectName" column="PROJECT_NAME"/>
                        <result property="projectDescription" column="PROJECT_DESCRIPTION"/>
                        <result property="projectType" column="PROJECT_TYPE"/>
                        <result property="companyCode" column="COMPANY_CODE"/>
                        <result property="companyName" column="COMPANY_NAME"/>
                        <result property="startDate" column="START_DATE"/>
                        <result property="endDate" column="END_DATE"/>
                        <result property="status" column="STATUS"/>
                        <result property="parentFlag" column="PARENT_FLAG"/>
                        <result property="parentProjectNum" column="PARENT_PROJECT_NUM"/>
                        <result property="parentProjectName" column="PARENT_PROJECT_NAME"/>
                        <result property="lastUpdateDate" column="LAST_UPDATE_DATE"/>
            </resultMap>
      <select id="selectPageList" resultType="com.easycloud.jxcmcc.datahub.vo.BudgetMapInfoVo">
          SELECT DISTINCT
          t2.id,
          t2.YEAR,
          t2.PROJECT_NUM      AS projectNum ,
          t2.PROJECT_NAME     AS projectName,
          t2.BUDGET_DEPT_CODE     AS deptCode,
          t2.PRI_KEY          AS priKey,
          t2.ACTIVITY_CODE   AS activityCode,
          t2.BUDGET_ACCOUNT_DESC AS budgetAccount,
          t2.ACTIVITY_NAME   AS activityName
          FROM
          FT_BUDGET_INQPRJMAPDPTINFO t1,
          (<include refid="budgetInfo"></include>) t2
         <where>
             AND t1.PROJECT_NUM=t2.PROJECT_NUM
             and (t1.OPERATE_DEPT_CODE =  #{budgetMapInfoVo.deptCode}
             <if test="budgetMapInfoVo.sysOrgCode!=null and budgetMapInfoVo.sysOrgCode!=''">
                 or  t1.OPERATE_DEPT_CODE =  #{budgetMapInfoVo.sysOrgCode}
             </if>
           )
             <if test="budgetMapInfoVo.activityCode!=null and budgetMapInfoVo.activityCode!=''">
                 and t2.activity_code = #{budgetMapInfoVo.activityCode}
             </if>
             <if test="budgetMapInfoVo.activityName!=null and budgetMapInfoVo.activityName!=''">
                 and t2.activity_Name like CONCAT('%',CONCAT(#{budgetMapInfoVo.activityName},'%'))
             </if>
             <if test="budgetMapInfoVo.projectName!=null and budgetMapInfoVo.projectName!=''">
                 and t2.PROJECT_NAME like CONCAT('%',CONCAT(#{budgetMapInfoVo.projectName},'%'))
             </if>
             <choose>
                 <when test="budgetMapInfoVo.projectNum!=null and budgetMapInfoVo.projectNum!=''">
                     and t2.PROJECT_NUM = #{budgetMapInfoVo.projectNum}
                 </when>
                 <otherwise>
                     <if test="budgetMapInfoVo.projectTime!=null and budgetMapInfoVo.projectTime!=''">
                         and  t2.YEAR = #{budgetMapInfoVo.projectTime}
                     </if>
                 </otherwise>
             </choose>


         </where>


      </select>

      <select id="selectListCheck" resultType="com.easycloud.jxcmcc.datahub.vo.BudgetMapInfoVo">
          SELECT DISTINCT
          t2.id,
          t2.YEAR,
          t2.PROJECT_NUM      AS projectNum ,
          t2.PROJECT_NAME     AS projectName,
          t2.BUDGET_DEPT_CODE     AS deptCode,
          t2.PRI_KEY          AS priKey,
          t2.ACTIVITY_CODE   AS activityCode,
          t2.BUDGET_ACCOUNT_DESC AS budgetAccount,
          t2.ACTIVITY_NAME   AS activityName
          FROM
          FT_BUDGET_INQPRJMAPDPTINFO t1,
          (<include refid="budgetInfo"></include>) t2
         <where>
             AND t1.PROJECT_NUM=t2.PROJECT_NUM
             <if test="budgetMapInfoVo.deptCode!=null and budgetMapInfoVo.deptCode!=''">
                 and t1.OPERATE_DEPT_CODE =  #{budgetMapInfoVo.deptCode}
             </if>
             <if test="budgetMapInfoVo.activityCode!=null and budgetMapInfoVo.activityCode!=''">
                 and t2.activity_code = #{budgetMapInfoVo.activityCode}
             </if>
             <if test="budgetMapInfoVo.activityName!=null and budgetMapInfoVo.activityName!=''">
                 and t2.activity_Name like CONCAT('%',CONCAT(#{budgetMapInfoVo.activityName},'%'))
             </if>
             <if test="budgetMapInfoVo.projectName!=null and budgetMapInfoVo.projectName!=''">
                 and t2.PROJECT_NAME like CONCAT('%',CONCAT(#{budgetMapInfoVo.projectName},'%'))
             </if>
             <choose>
                 <when test="budgetMapInfoVo.projectNum!=null and budgetMapInfoVo.projectNum!=''">
                     and t2.PROJECT_NUM = #{budgetMapInfoVo.projectNum}
                 </when>
                 <otherwise>
                     <if test="budgetMapInfoVo.projectTime!=null and budgetMapInfoVo.projectTime!=''">
                         and  t2.YEAR = #{budgetMapInfoVo.projectTime}
                     </if>
                 </otherwise>
             </choose>


         </where>


      </select>


    <select id="selectMapList" resultType="com.easycloud.jxcmcc.datahub.vo.BudgetMapInfoVo">
        SELECT DISTINCT
        *
        FROM
        (<include refid="budgetInfo"></include>) t2
        <where>
            <if test="budgetMapInfoVo.activityCode!=null and budgetMapInfoVo.activityCode!=''">
                and t2.activity_code = #{budgetMapInfoVo.activityCode}
            </if>
            <if test="budgetMapInfoVo.projectName!=null and budgetMapInfoVo.projectName!=''">
                and t2.PROJECT_NAME like CONCAT('%',CONCAT(#{budgetMapInfoVo.projectName},'%'))
            </if>
            <if test="budgetMapInfoVo.projectNum!=null and budgetMapInfoVo.projectNum!=''">
                and t2.PROJECT_NUM = #{budgetMapInfoVo.projectNum}
            </if>
        </where>
    </select>
    <select id="queryBudgetByKeyAndActivity" resultType="com.easycloud.jxcmcc.datahub.vo.BudgetMapInfoVo">
        SELECT DISTINCT
            t0.PROJECT_NUM      AS projectNum,
            t0.PROJECT_NAME     AS projectName,
            t2.PRI_KEY          AS priKey
        FROM
            FT_BUDGET_INQPRJINFO t0,
            FT_BUDGET_INQPRJMAPDPTINFO t1,
        (<include refid="budgetInfo"></include>) t2,
            FT_BUDGET_INQSTRATEGYINFO t3
        WHERE
            t1.PROJECT_NUM = t0.PROJECT_NUM
          AND t1.PROJECT_NUM = t2.PROJECT_NUM
          AND t2.STRATEGY_CODE = t3.STRATEGY_CODE
          AND t0.status = 'IMPLEMENTING'
          AND t1.OPERATE_DEPT_CODE = #{budgetMapInfoVo.deptCode}

          and t2.PRI_KEY = #{budgetMapInfoVo.priKey}
    </select>
    <select id="selectBusinessSettlebudgetitemmappingList"
            resultType="com.easycloud.jxcmcc.datahub.vo.BudgetMapInfoVo">
        SELECT DISTINCT
        t2.id,
        t2.YEAR,
        t2.PROJECT_NUM      AS projectNum ,
        t2.PROJECT_NAME     AS projectName,
        t2.BUDGET_DEPT_CODE     AS deptCode,
        t2.PRI_KEY          AS priKey,
        t2.ACTIVITY_CODE   AS activityCode,
        t2.BUDGET_ACCOUNT_DESC AS budgetAccount,
        t2.ACTIVITY_NAME   AS activityName
        FROM
        FT_BUDGET_INQPRJMAPDPTINFO t1,
        (<include refid="budgetInfo"></include>) t2
        <where>
            AND t1.PROJECT_NUM=t2.PROJECT_NUM
            <if test="budgetMapInfoVo.activityCode!=null and budgetMapInfoVo.activityCode!=''">
                and t2.activity_code = #{budgetMapInfoVo.activityCode}
            </if>
            <if test="budgetMapInfoVo.activityName!=null and budgetMapInfoVo.activityName!=''">
                and t2.activity_Name like CONCAT('%',CONCAT(#{budgetMapInfoVo.activityName},'%'))
            </if>
            <if test="budgetMapInfoVo.projectName!=null and budgetMapInfoVo.projectName!=''">
                and t2.PROJECT_NAME like CONCAT('%',CONCAT(#{budgetMapInfoVo.projectName},'%'))
            </if>
            <choose>
                <when test="budgetMapInfoVo.projectNum!=null and budgetMapInfoVo.projectNum!=''">
                    and t2.PROJECT_NUM = #{budgetMapInfoVo.projectNum}
                </when>
                <otherwise>
                    <if test="budgetMapInfoVo.projectTime!=null and budgetMapInfoVo.projectTime!=''">
                        and  t2.YEAR = #{budgetMapInfoVo.projectTime}
                    </if>
                </otherwise>
            </choose>


        </where>

    </select>
    <sql id="budgetInfo">
        SELECT
            PRJ.ID,
        PRJ.YEAR,
        MAP.PRI_KEY,
        PRJ.PROJECT_NUM ,
        PRJ.PROJECT_NAME ,
        STRATEGY.STRATEGY_CODE,
        STRATEGY.STRATEGY_NAME,
        MAP.BUDGET_DEPT_CODE,
        ACCT.BUDGET_ACCOUNT,
        ACCT.BUDGET_ACCOUNT_DESC,
        ACT.ACTIVITY_CODE,
        ACT.ACTIVITY_NAME
        <!--,BRAND.BRAND_CODE,-->
        <!--BRAND.BRAND_NAME -->
        FROM
        FT_BUDGET_INQMAPINFO MAP,
        FT_BUDGET_INQPRJINFO PRJ,
        FT_BUDGET_INQBDGTACCTINFO ACCT,
        (
        SELECT DISTINCT
        ACTIVITY_CODE,
        ACTIVITY_NAME
        FROM
        FT_RMBS_ITEM_MAP) ACT,
        FT_BUDGET_INQBRANDINFO BRAND,
        FT_BUDGET_INQSTRATEGYINFO STRATEGY
        WHERE
        MAP.PROJECT_NUM = PRJ.PROJECT_NUM
        AND MAP.BUDGET_ACCOUNT = ACCT.BUDGET_ACCOUNT
        AND MAP.ACTIVITY_CODE = ACT.ACTIVITY_CODE
        <!-- AND MAP.BRAND_CODE = BRAND.BRAND_CODE -->
        AND YEAR >='2019'
        <!--AND PRJ.STATUS = 'IMPLEMENTING'-->
        AND PRJ.STATUS = 'EFFECT'
        AND MAP.PRI_KEY IS NOT NULL
        AND MAP.ENABLED_FLAG != 'N'
    </sql>




    <select id="getBudgetPayProportion" resultType="com.easycloud.jxcmcc.datahub.vo.BudgetInfoVo">
        select YEAR,
            PROJECT_NUM ,
            PROJECT_NAME ,
            BUDGET_DEPT_CODE,
            ACTIVITY_CODE
        from (
                select
                    PRJ.YEAR,
                    PRJ.PROJECT_NUM ,
                    PRJ.PROJECT_NAME ,
                    MAP.BUDGET_DEPT_CODE,
                    ACT.ACTIVITY_CODE
                from
                    FT_BUDGET_INQMAPINFO MAP,
                    FT_BUDGET_INQPRJINFO PRJ,
                    (SELECT DISTINCT ACTIVITY_CODE,ACTIVITY_NAME FROM FT_RMBS_ITEM_MAP) ACT
                WHERE
                    MAP.PROJECT_NUM = PRJ.PROJECT_NUM
                    AND MAP.ACTIVITY_CODE = ACT.ACTIVITY_CODE
                    <choose>
                        <when test="budgetInfoVo.year!=null and budgetInfoVo.year !=''">
                            AND YEAR >= #{budgetInfoVo.year}
                        </when>
                        <otherwise>
                            AND YEAR >='2019'
                        </otherwise>
                    </choose>

                  AND PRJ.STATUS = 'EFFECT'
                  AND MAP.PRI_KEY IS NOT NULL
                  AND MAP.ENABLED_FLAG != 'N'
                <if test="budgetInfoVo.projectNum !=null and budgetInfoVo.projectNum !=''">
                    AND PRJ.PROJECT_NUM = #{budgetInfoVo.projectNum}
                </if>
                <if test="budgetInfoVo.budgetDeptCode!=null and budgetInfoVo.budgetDeptCode !=''">
                    AND MAP.BUDGET_DEPT_CODE = #{budgetInfoVo.budgetDeptCode}
                </if>
                <if test="budgetInfoVo.activityCode !=null and budgetInfoVo.activityCode !=''">
                    AND ACT.ACTIVITY_CODE = #{budgetInfoVo.activityCode}
                </if>
           )a
            group by
                YEAR,
                PROJECT_NUM ,
                PROJECT_NAME ,
                BUDGET_DEPT_CODE,
                ACTIVITY_CODE
    </select>
</mapper>
