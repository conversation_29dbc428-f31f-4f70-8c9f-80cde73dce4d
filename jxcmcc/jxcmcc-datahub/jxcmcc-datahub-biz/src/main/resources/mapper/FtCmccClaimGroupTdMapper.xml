<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, jxcmcc All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: jxcmcc
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.easycloud.jxcmcc.datahub.task.mapper.FtCmccClaimGroupTdMapper">

  <resultMap id="ftCmccClaimGroupTdMap" type="com.easycloud.jxcmcc.datahub.task.entity.FtCmccClaimGroupTd">
                  <id property="id" column="ID"/>
                        <result property="claimNo" column="CLAIM_NO"/>
                        <result property="groupClaimNo" column="GROUP_CLAIM_NO"/>
                        <result property="flowStatus" column="FLOW_STATUS"/>
                        <result property="erpStatus" column="ERP_STATUS"/>
                        <result property="groupStatus" column="GROUP_STATUS"/>
                        <result property="isImport" column="IS_IMPORT"/>
                        <result property="itemId" column="ITEM_ID"/>
                        <result property="claimId" column="CLAIM_ID"/>
                        <result property="isEmail" column="IS_EMAIL"/>
                        <result property="errorNum" column="ERROR_NUM"/>
                        <result property="lastUpdateDate" column="LAST_UPDATE_DATE"/>
            </resultMap>

    <select id="findByQueryList" resultType="com.easycloud.jxcmcc.datahub.task.entity.FtCmccClaimGroupTd">
        select * from FT_CMCC_CLAIM_GROUP_TD where GROUP_CLAIM_NO is not null and
        (GROUP_STATUS is null or GROUP_STATUS = '' or GROUP_STATUS != 'COMPLETED' and GROUP_STATUS != 'ABOLISHED') AND LAST_UPDATE_DATE <![CDATA[ >= ]]> systimestamp - interval '90' day
    </select>
</mapper>
