<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, jxcmcc All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: jxcmcc
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.easycloud.jxmcc.efinance.mapper.FtVVendorCompanyInfoMapper">

  <resultMap id="ftVVendorCompanyInfoMap" type="com.easycloud.jxcmcc.datahub.entity.FtVVendorCompanyInfo">
                  <id property="vendorCompantId" column="VENDOR_COMPANT_ID"/>
                        <result property="vendorId" column="VENDOR_ID"/>
                        <result property="orgName" column="ORG_NAME"/>
                        <result property="ouCode" column="OU_CODE"/>
                        <result property="validFlag" column="VALID_FLAG"/>
                        <result property="creationDate" column="CREATION_DATE"/>
                        <result property="expiryDate" column="EXPIRY_DATE"/>
                        <result property="purchaseType" column="PURCHASE_TYPE"/>
                        <result property="remark" column="REMARK"/>
                        <result property="costOfCoopreation" column="COST_OF_COOPREATION"/>
            </resultMap>
</mapper>
