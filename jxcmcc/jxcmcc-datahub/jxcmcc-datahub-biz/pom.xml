<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jxcmcc-datahub</artifactId>
        <groupId>com.easycloud</groupId>
        <version>3.8.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jxcmcc-datahub-biz</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <xerces.version>2.11.0</xerces.version>
    </properties>
    	<dependencies>
      <!--business api、model 模块-->


		<!-- xxl-job -->
		<dependency>
			<groupId>com.xuxueli</groupId>
			<artifactId>xxl-job-core</artifactId>
			<version>2.4.1</version>
		</dependency>


		<!--日志处理-->
		<dependency>
			<groupId>com.easycloud</groupId>
			<artifactId>jxcmcc-common-log</artifactId>
		</dependency>
		<dependency>
			<groupId>com.easycloud</groupId>
			<artifactId>jxcmcc-common-data</artifactId>
		</dependency>
		<!--swagger-->
		<dependency>
			<groupId>com.easycloud</groupId>
			<artifactId>jxcmcc-common-swagger</artifactId>
		</dependency>
		<!--文件系统-->
		<dependency>
			<groupId>com.easycloud</groupId>
			<artifactId>jxcmcc-common-minio</artifactId>
		</dependency>
		<!--注册中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<!--配置中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<!--spring security 、oauth、jwt依赖-->
		<dependency>
			<groupId>com.easycloud</groupId>
			<artifactId>jxcmcc-common-security</artifactId>
		</dependency>
		<!--支持动态路由配置 -->
		<dependency>
			<groupId>com.easycloud</groupId>
			<artifactId>jxcmcc-common-gateway</artifactId>
		</dependency>
		<!--sentinel 依赖-->
		<dependency>
			<groupId>com.easycloud</groupId>
			<artifactId>jxcmcc-common-sentinel</artifactId>
		</dependency>
		<!--路由控制-->
		<dependency>
			<groupId>com.easycloud</groupId>
			<artifactId>jxcmcc-common-gray</artifactId>
		</dependency>
		<!--mybatis-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<!-- druid 连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
			<!--动态数据源 数据操作-->
		<dependency>
			<groupId>com.easycloud</groupId>
			<artifactId>jxcmcc-common-datasource</artifactId>
		</dependency>
		<!--数据库-->
		<!-- 添加oracle jdbc driver -->
		<dependency>
			<groupId>com.oracle</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>12.2.0.1</version>
		</dependency>

		<!-- db2 -->
		<dependency>
			<groupId>com.ibm.db2.jcc</groupId>
			<artifactId>db2jcc4</artifactId>
			<version>10.1</version>
		</dependency>
		<!-- freemarker -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-base</artifactId>
			<version>${easypoi.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-web</artifactId>
			<version>${easypoi.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-annotation</artifactId>
			<version>${easypoi.version}</version>
		</dependency>

		<dependency>
		<groupId>net.sourceforge.javacsv</groupId>
		<artifactId>javacsv</artifactId>
		<version>2.0</version>
		</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpmime</artifactId>
				<version>4.5.12</version>
			</dependency>
			<dependency>
				<groupId>org.redisson</groupId>
				<artifactId>redisson</artifactId>
				<version>3.16.6</version>
			</dependency>
			<dependency>
				<groupId>log4j</groupId>
				<artifactId>log4j</artifactId>
				<version>1.2.17</version>
			</dependency>
			<!--pdf转图片相关-->
			<!-- https://mvnrepository.com/artifact/org.apache.pdfbox/pdfbox -->
			<dependency>
				<groupId>org.apache.pdfbox</groupId>
				<artifactId>pdfbox</artifactId>
				<version>2.0.13</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.apache.pdfbox/fontbox -->
			<dependency>
				<groupId>org.apache.pdfbox</groupId>
				<artifactId>fontbox</artifactId>
				<version>2.0.13</version>
			</dependency>
			<!--selenium-->
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-java</artifactId>
				<version>3.141.59</version>
			</dependency>
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-htmlunit-driver</artifactId>
				<version>2.52.0</version>
			</dependency>
			<dependency>
				<groupId>com.github.detro</groupId>
				<artifactId>ghostdriver</artifactId>
				<version>2.1.0</version>
			</dependency>

			<!--excelSAX解析-->
            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>${xerces.version}</version>
            </dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>easyexcel</artifactId>
				<version>2.1.1</version>
			</dependency>
			<!--xls-->
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>4.1.1</version>
			</dependency>
			<dependency>
				<groupId>com.thoughtworks.xstream</groupId>
				<artifactId>xstream</artifactId>
				<version>1.4.11</version>
				<scope>compile</scope>
			</dependency>
		<!--web 模块-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<!--undertow容器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.4</version>
		</dependency>

            <!--发送邮件-->
		<dependency>
		<groupId>javax.mail</groupId>
		<artifactId>mail</artifactId>
		  <version>1.4.7</version>
		</dependency>
            <dependency>
                <groupId>com.easycloud</groupId>
                <artifactId>jxcmcc-assist-api</artifactId>
                <version>3.8.0</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.easycloud</groupId>
                <artifactId>jxcmcc-ocr-api</artifactId>
                <version>3.8.0</version>
                <scope>compile</scope>
            </dependency>

			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-websocket</artifactId>
			</dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
            </dependency>
            <dependency>
                <groupId>com.easycloud</groupId>
                <artifactId>jxcmcc-common-excel</artifactId>
                <version>3.8.0</version>
                <scope>compile</scope>
            </dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-test</artifactId>
			</dependency>
            <dependency>
                <groupId>com.easycloud</groupId>
                <artifactId>jxcmcc-eflow-api</artifactId>
                <version>3.8.0</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.easycloud</groupId>
                <artifactId>jxcmcc-eflow-core</artifactId>
            </dependency>


        </dependencies>

    	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>io.fabric8</groupId>
				<artifactId>docker-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<version>2.6</version>
				<artifactId>maven-resources-plugin</artifactId>
				<configuration>
					<encoding>UTF-8</encoding>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>xlsx</nonFilteredFileExtension>   //xlsx结尾的文件不
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>
		</plugins>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>**/*.xlsx</exclude>
					<exclude>**/*.xls</exclude>
					<exclude>**/*.doc</exclude>
					<exclude>**/*.docx</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>false</filtering>
				<includes>
					<include>**/*.xlsx</include>
					<include>**/*.xls</include>
					<include>**/*.doc</include>
					<include>**/*.docx</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
					<include>**/*.json</include>
					<include>**/*.ftl</include>
				</includes>
			</resource>
		</resources>

	</build>


</project>
