<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, jxcmcc All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: jxcmcc
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.easycloud.jxmcc.ocr.mapper.ClaimAuditResultsMapper">

    <resultMap id="claimAuditResultsMap" type="com.easycloud.jxcmcc.ocr.entity.ClaimAuditResults">
        <id property="id" column="ID"/>
        <result property="claimNum" column="CLAIM_NUM"/>
        <result property="claimHeaderId" column="CLAIM_HEADER_ID"/>
        <result property="claimTypeCode" column="CLAIM_TYPE_CODE"/>
        <result property="claimTypeName" column="CLAIM_TYPE_NAME"/>
        <result property="checkType1Code" column="CHECK_TYPE1_CODE"/>
        <result property="checkType1Name" column="CHECK_TYPE1_NAME"/>
        <result property="checkType2Code" column="CHECK_TYPE2_CODE"/>
        <result property="checkType2Name" column="CHECK_TYPE2_NAME"/>
        <result property="checkResult" column="CHECK_RESULT"/>
        <result property="checkResultType" column="CHECK_RESULT_TYPE"/>
        <result property="errorFile" column="ERROR_FILE"/>
        <result property="accountUser" column="ACCOUNT_USER"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdTime" column="CREATED_TIME"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedTime" column="UPDATED_TIME"/>
        <result property="orderNum" column="ORDER_NUM"/>
    </resultMap>


    <select id="allClaimComp" resultType="java.util.HashMap">
        SELECT
            R.COMPANY_CODE,
            COUNT(1) as  CLAIM_COUNT
        FROM
            (
                SELECT
                     CLAIM_NUM,
                     COMPANY_CODE
                FROM
                    FT_CLAIM_ERP
                WHERE
                    GL_DATE <![CDATA[ < ]]> ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                    GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-mm-dd hh24:mi:ss') AND COMPANY_CODE LIKE '30%') R
        GROUP BY
            R.COMPANY_CODE
    </select>


    <select id="allClaimList" resultType="java.util.HashMap">
        SELECT distinct e.CLAIM_NUM,e.CLAIM_TYPE_NAME,e.COMPANY_NAME,e.GL_DATE,
                        NVL2(a1.CLAIM_NUM,'是','否') as A1,
                        NVL2(a2.CLAIM_NUM,'是','否') as A2,
                        NVL2(a3.CLAIM_NUM,'是','否') as A3,
                        a1.AUDIT_BUSINESS_TYPE_NAME,
                        ROUND(b1.REVIEW_APPROVAL_TIME*60, 0) as B1,
                        ROUND(b2.AUDIT_TIME, 0) as B2,
                        ROUND(b3.AUDIT_TIME, 0) as B3
        from
            FT_CLAIM_ERP e
                LEFT JOIN FT_SUBMIT_CLAIM_LINE a1 ON(a1.CLAIM_NUM = e.CLAIM_NUM  AND a1.SUBMIT_FLOW LIKE '省公司%' and ROWNUM = 1 )
                LEFT JOIN T_VIEW_CHECK_RESULT_LOG a2 ON(a2.CLAIM_NUM = e.CLAIM_NUM and a2.APPROVAL_NODE_NAME like '省公司%' and ROWNUM = 1 )
                LEFT JOIN T_AUTO_CHECK_LOG a3 ON(a3.CLAIM_NUM = e.CLAIM_NUM and a3.APPROVAL_NODE_NAME like '省公司%'
           AND a3.STATUS = 2 AND a3.AUTO_SUBMIT_TYPE !=1 and ROWNUM = 1 )
                LEFT JOIN FT_PROTAL_DURATION_STATISTICS b1 ON(b1.CLAIM_NO = e.CLAIM_NUM and ROWNUM = 1)
                LEFT JOIN FT_SUBMIT_CLAIM_LINE b2 ON(b2.CLAIM_NUM = e.CLAIM_NUM AND b2.SUBMIT_FLOW LIKE '省公司%' and ROWNUM = 1 )
                LEFT JOIN T_AUTO_CHECK_LOG b3 ON(b3.CLAIM_NUM = e.CLAIM_NUM AND b3.APPROVAL_NODE_NAME LIKE '省公司%' and b3.AUDIT_BUSINESS_TYPE_NAME IS NOT NULL AND b3.AUTO_SUBMIT_TYPE != 1 AND b3.STATUS = 2 and ROWNUM=1)
        where
            e.GL_DATE <![CDATA[ < ]]> ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
            e.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-mm-dd hh24:mi:ss') AND e.COMPANY_CODE LIKE '30%'
    </select>



    <select id="allClaimViewComp" resultType="java.util.HashMap">
            SELECT
                R.COMPANY_CODE,
                COUNT(1) as  CLAIM_COUNT
            FROM
                (
                    SELECT
                         l.CLAIM_NUM,
                         l.COMPANY_CODE
                    FROM
                        FT_CLAIM_ERP l
                    WHERE
                        l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                        l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
                      AND EXISTS(
                        SELECT
                            c.CLAIM_NUM
                        FROM
                            FT_SUBMIT_CLAIM_LINE c
                        WHERE
                            c.CLAIM_NUM = l.CLAIM_NUM
                          AND c.SUBMIT_FLOW LIKE '省公司%')
                      AND l.COMPANY_CODE LIKE '30%') R
            GROUP BY
                R.COMPANY_CODE
    </select>

    <select id="allClaimAuditComp" resultType="java.util.HashMap">
        SELECT
            R.COMPANY_CODE,
            COUNT(1) as  CLAIM_COUNT
        FROM
            (
                SELECT
                     l.CLAIM_NUM,
                     l.COMPANY_CODE
                FROM
                    FT_CLAIM_ERP l
                WHERE
                    l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                    l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
            AND EXISTS(
            select rs.CLAIM_NUM from T_VIEW_CHECK_RESULT_LOG rs
            where
            rs.APPROVAL_NODE_NAME like '省公司%'
            and  rs.CLAIM_NUM = l.CLAIM_NUM
            )
            AND l.COMPANY_CODE LIKE '30%') R
        GROUP BY
            R.COMPANY_CODE
    </select>

    <select id="allClaimAutoAuditComp" resultType="java.util.HashMap">
        SELECT
            R.COMPANY_CODE,
            COUNT(1) as  CLAIM_COUNT
        FROM
            (
                SELECT
                     l.CLAIM_NUM,
                     l.COMPANY_CODE
                FROM
                    FT_CLAIM_ERP l
                WHERE
                    l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                    l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
            AND EXISTS(
            select rs.CLAIM_NUM from T_AUTO_CHECK_LOG rs
            where
            rs.APPROVAL_NODE_NAME like '省公司%'
            and rs.CLAIM_NUM = l.CLAIM_NUM AND rs.STATUS = 2 AND rs.AUTO_SUBMIT_TYPE !=1
            )
            AND l.COMPANY_CODE LIKE '30%') R
        GROUP BY
            R.COMPANY_CODE
    </select>

    <select id="lastYearTimeAll" resultType="java.util.HashMap">
        select * from (
                          SELECT
                              CLAIM_TYPE_NAME,ROUND(avg(REVIEW_APPROVAL_TIME)* 60, 0) as APPROVAL_TIME
                          FROM
                              FT_PROTAL_DURATION_STATISTICS
                          WHERE
                              PEROID =  TO_CHAR(ADD_MONTHS(to_date(#{param.glDate}, 'yyyy-MM'), -12), 'yyyy-MM') group by CLAIM_TYPE_NAME) r where r.CLAIM_TYPE_NAME in (select distinct CLAIM_TYPE_NAME from CLAIM_AUDIT_RESULTS )   order by APPROVAL_TIME

    </select>

    <select id="thisYearTimeAll" resultType="java.util.HashMap">
        select * from (
                          SELECT
                              CLAIM_TYPE_NAME,ROUND(avg(REVIEW_APPROVAL_TIME)* 60, 0) as APPROVAL_TIME
                          FROM
                              FT_PROTAL_DURATION_STATISTICS
                          WHERE
                              PEROID =   #{param.glDate} group by CLAIM_TYPE_NAME) r where r.CLAIM_TYPE_NAME in (select distinct CLAIM_TYPE_NAME from CLAIM_AUDIT_RESULTS )   order by APPROVAL_TIME
    </select>

    <select id="thisYearTimeAllDetail" resultType="java.util.HashMap">
        select * from (
                          SELECT
                              CLAIM_NO,TOTAL_APPROVAL_TIME,CLAIM_TYPE_NAME,PEROID
                          FROM
                              FT_PROTAL_DURATION_STATISTICS
                          WHERE
                                  PEROID =  TO_CHAR(ADD_MONTHS(to_date(#{param.glDate}, 'yyyy-MM'), -12), 'yyyy-MM') or PEROID =   #{param.glDate} ) r where r.CLAIM_TYPE_NAME in (select distinct CLAIM_TYPE_NAME from CLAIM_AUDIT_RESULTS )

    </select>

    <select id="lastYearTimeAudit" resultType="java.lang.Integer">
        SELECT
        ROUND(avg(AUDIT_TIME)/ 60, 0)
        FROM
        FT_SUBMIT_CLAIM_LINE
        WHERE
        SUBMIT_TIME <![CDATA[ < ]]> ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00', 'yyyy-MM-dd hh24:mi:ss'), -11)
        AND SUBMIT_TIME <![CDATA[ >= ]]> ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00', 'yyyy-MM-dd hh24:mi:ss'), -12)
        AND SUBMIT_FLOW LIKE '省公司%'
    </select>

    <select id="thisYearTimeAudit" resultType="java.util.HashMap">
    SELECT * FROM (
        SELECT
            AUDIT_BUSINESS_TYPE_NAME as CLAIM_TYPE_NAME,  ROUND(avg(AUDIT_TIME)/ 60, 0) as APPROVAL_TIME
        FROM
            FT_SUBMIT_CLAIM_LINE
        WHERE
           1=1
          AND SUBMIT_TIME <![CDATA[ < ]]> ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00', 'yyyy-MM-dd hh24:mi:ss'), 1)
          AND SUBMIT_TIME <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00', 'yyyy-MM-dd hh24:mi:ss')
          AND AUDIT_BUSINESS_TYPE_NAME IS NOT NULL
          AND SUBMIT_FLOW LIKE '省公司%' group by AUDIT_BUSINESS_TYPE_NAME  ) ORDER BY APPROVAL_TIME
    </select>

    <select id="lastYearTimeAutoAudit" resultType="java.lang.Integer">
        SELECT
        ROUND(avg(AUDIT_TIME)/ 60, 0)
        FROM
        T_AUTO_CHECK_LOG
        WHERE
        CREATED_TIME  <![CDATA[ < ]]> ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00', 'yyyy-MM-dd hh24:mi:ss'), -11)
        AND CREATED_TIME >= ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00', 'yyyy-MM-dd hh24:mi:ss'), -12)
        AND APPROVAL_NODE_NAME LIKE '省公司%'
    </select>

    <select id="thisYearTimeAutoAudit" resultType="java.util.HashMap">
    SELECT * FROM (
        SELECT
            AUDIT_BUSINESS_TYPE_NAME as CLAIM_TYPE_NAME,ROUND(avg(AUDIT_TIME), 0) as APPROVAL_TIME
        FROM
            T_AUTO_CHECK_LOG
        WHERE
              CREATED_TIME  <![CDATA[ < ]]> ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-05 00:00:00', 'yyyy-MM-dd hh24:mi:ss'), 1)
        AND CREATED_TIME  <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00', 'yyyy-MM-dd hh24:mi:ss')
        AND AUDIT_BUSINESS_TYPE_NAME IS NOT NULL AND  AUTO_SUBMIT_TYPE != 1
        AND APPROVAL_NODE_NAME LIKE '省公司%' AND STATUS = 2 group by AUDIT_BUSINESS_TYPE_NAME ) ORDER BY APPROVAL_TIME
    </select>

    <select id="allClaimCompType" resultType="java.util.HashMap">
        SELECT rs1.*,nvl(rs2.CLAIM_AUDIT_COUNT,0) as CLAIM_AUDIT_COUNT from (
            SELECT
                r1.CHECK_BUSINESS_TYPE_NAME as BUSINESS_TYPE,
                r1.COMPANY_SEGMENT as COMPANY_CODE,
                count(1) as CLAIM_COUNT
            FROM
                (
                    SELECT
                        DISTINCT h.CLAIM_NUM,
                                 h.COMPANY_SEGMENT,
                                 r.CHECK_BUSINESS_TYPE_NAME
                    FROM
                        CLAIM_AUDIT_HEADER h,
                        CLAIM_AUDIT_RESULTS r
                    WHERE
                        r.CLAIM_NUM = h.CLAIM_NUM
                      AND r.CHECK_BUSINESS_TYPE_NAME IS NOT NULL
                      AND EXISTS(
                        SELECT
                            h.CLAIM_NUM
                        FROM
                            FT_CLAIM_ERP l
                        WHERE
                            l.CLAIM_NUM = h.CLAIM_NUM
                          AND l.COMPANY_CODE LIKE '30%'
                          AND l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                            l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss') )
                ) r1
            GROUP BY
                r1.CHECK_BUSINESS_TYPE_NAME,
                r1.COMPANY_SEGMENT ) rs1
        left join
            (SELECT
                r1.CHECK_BUSINESS_TYPE_NAME as BUSINESS_TYPE,
                    r1.COMPANY_SEGMENT as COMPANY_CODE,
                    count(1) as CLAIM_AUDIT_COUNT
            FROM
                (
                SELECT
                DISTINCT h.CLAIM_NUM,
                h.COMPANY_SEGMENT,
                r.CHECK_BUSINESS_TYPE_NAME
                FROM
                CLAIM_AUDIT_HEADER h,
                CLAIM_AUDIT_RESULTS r
                WHERE
                r.CLAIM_NUM = h.CLAIM_NUM
                AND r.CHECK_BUSINESS_TYPE_NAME IS NOT NULL
                AND EXISTS(
                    select rl.ClAIM_NUM  FROM
                        T_VIEW_CHECK_RESULT_LOG rl
                    where rl.CLAIM_NUM = h.CLAIM_NUM
                )
                AND EXISTS(
                SELECT
                h.CLAIM_NUM
                FROM
                FT_CLAIM_ERP l
                WHERE
                l.CLAIM_NUM = h.CLAIM_NUM
                AND l.COMPANY_CODE LIKE '30%'
                AND l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss') )
                ) r1
            GROUP BY
                r1.CHECK_BUSINESS_TYPE_NAME,
                r1.COMPANY_SEGMENT ) rs2  on (rs1.BUSINESS_TYPE = rs2.BUSINESS_TYPE and rs1.COMPANY_CODE = rs2.COMPANY_CODE )
    </select>



    <select id="submitNum" resultType="java.lang.Integer">
        SELECT
            COUNT(1) as  CLAIM_COUNT
        FROM
            (
                SELECT
                    l.CLAIM_NUM,
                    l.COMPANY_CODE
                FROM
                    FT_CLAIM_ERP l
                WHERE
                        l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                        l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
            AND EXISTS(
        SELECT
        c.CLAIM_NUM
        FROM
        FT_SUBMIT_CLAIM_LINE c
        WHERE
        c.CLAIM_NUM = l.CLAIM_NUM
        AND c.SUBMIT_FLOW LIKE '省公司%' and c.CLAIM_AUDIT_STATUS = 1 and c.OPERATE_TYPE =0 )) R
    </select>


    <select id="submitNum1" resultType="java.lang.Integer">
        SELECT
            COUNT(1) as  CLAIM_COUNT
        FROM
            (
                SELECT
                    l.CLAIM_NUM,
                    l.COMPANY_CODE
                FROM
                    FT_CLAIM_ERP l
                WHERE
                        l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                        l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
            AND EXISTS(
        SELECT
        c.CLAIM_NUM
        FROM
        FT_SUBMIT_CLAIM_LINE c
        WHERE
        c.CLAIM_NUM = l.CLAIM_NUM
        AND c.SUBMIT_FLOW LIKE '省公司%' and c.CLAIM_AUDIT_STATUS in(1,2,4) )) R
    </select>



    <select id="submitNum2" resultType="java.lang.Integer">
        SELECT
            COUNT(1) as  CLAIM_COUNT
        FROM
            (
                SELECT
                    l.CLAIM_NUM,
                    l.COMPANY_CODE
                FROM
                    FT_CLAIM_ERP l
                WHERE
                        l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                        l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
            AND EXISTS(
        SELECT
        c.CLAIM_NUM
        FROM
        FT_SUBMIT_CLAIM_LINE c
        WHERE
        c.CLAIM_NUM = l.CLAIM_NUM
        AND c.SUBMIT_FLOW LIKE '省公司%' and c.CLAIM_AUDIT_STATUS = 1 and  c.OPERATE_TYPE in(1,2) )) R
    </select>


    <select id="submitNum3" resultType="java.lang.Integer">
        SELECT
        COUNT(1) as CLAIM_COUNT
        FROM
        (
        SELECT
        l.CLAIM_NUM,
        l.COMPANY_CODE
        FROM
        FT_CLAIM_ERP l
        WHERE
            l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
            l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
        AND EXISTS(
        SELECT
        c.CLAIM_NUM
        FROM
        FT_SUBMIT_CLAIM_LINE c
        WHERE
        c.CLAIM_NUM = l.CLAIM_NUM
        AND c.SUBMIT_FLOW LIKE '省公司%' and c.CLAIM_AUDIT_STATUS = 2 and c.OPERATE_TYPE =0 )) R
    </select>



    <select id="autoSum" resultType="java.lang.Integer">
                SELECT
                    COUNT(1)
                FROM
                    FT_CLAIM_ERP l
                WHERE
                        l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                        l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
                  AND  EXISTS (
                    select rs.CLAIM_NUM from T_AUTO_CHECK_LOG rs where rs.AUDIT_BUSINESS_TYPE_NAME IS NOT NULL AND  rs.AUTO_SUBMIT_TYPE != 1 AND rs.APPROVAL_NODE_NAME LIKE '省公司%' AND rs.CLAIM_NUM = l.CLAIM_NUM

                )

    </select>

    <select id="autoPassSum" resultType="java.lang.Integer">
            SELECT
                COUNT(1)
            FROM
                FT_CLAIM_ERP l
            WHERE
                    l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                    l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
              AND  EXISTS (
                select rs.CLAIM_NUM from T_AUTO_CHECK_LOG rs where rs.AUDIT_BUSINESS_TYPE_NAME IS NOT NULL AND  rs.AUTO_SUBMIT_TYPE != 1 AND rs.APPROVAL_NODE_NAME LIKE '省公司%' AND rs.STATUS = 2 AND rs.CLAIM_NUM = l.CLAIM_NUM

            )
    </select>

    <select id="invoiceSum" resultType="java.lang.Integer">
        SELECT
            COUNT(1) as  CLAIM_COUNT
        FROM
            (
                SELECT
                    l.CLAIM_NUM,
                    l.COMPANY_CODE
                FROM
                    FT_CLAIM_ERP l
                WHERE
                        l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                        l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
                    AND NOT EXISTS (
                        select rs.CLAIM_NUM from CLAIM_AUDIT_RESULTS rs where   rs.CHECK_TYPE1_CODE  = 'invoiceCheck' and rs.CHECK_RESULT_TYPE!=0 AND rs.CLAIM_NUM = l.CLAIM_NUM

                    )
                  AND EXISTS(
                    SELECT
                        c.CLAIM_NUM
                    FROM
                        FT_SUBMIT_CLAIM_LINE c
                    WHERE
                        c.CLAIM_NUM = l.CLAIM_NUM
                      AND c.SUBMIT_FLOW LIKE '省公司%' and c.CLAIM_AUDIT_STATUS in(1,2,4) and c.OPERATE_TYPE =0 )) R
    </select>

    <select id="claimSum" resultType="java.lang.Integer">
        SELECT
            COUNT(1) as  CLAIM_COUNT
        FROM
            (
                SELECT
                    l.CLAIM_NUM,
                    l.COMPANY_CODE
                FROM
                    FT_CLAIM_ERP l
                WHERE
                        l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                        l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
                  AND NOT EXISTS (
                    select rs.CLAIM_NUM from CLAIM_AUDIT_RESULTS rs where   rs.CHECK_TYPE1_CODE  = 'claimCheck' and rs.CHECK_RESULT_TYPE!=0 AND rs.CLAIM_NUM = l.CLAIM_NUM

                )
                  AND EXISTS(
                    SELECT
                        c.CLAIM_NUM
                    FROM
                        FT_SUBMIT_CLAIM_LINE c
                    WHERE
                        c.CLAIM_NUM = l.CLAIM_NUM
                      AND c.SUBMIT_FLOW LIKE '省公司%' and c.CLAIM_AUDIT_STATUS in(1,2,4) and c.OPERATE_TYPE =0 )) R
    </select>


    <select id="attachmentSum" resultType="java.lang.Integer">
        SELECT
            COUNT(1) as  CLAIM_COUNT
        FROM
            (
                SELECT
                    l.CLAIM_NUM,
                    l.COMPANY_CODE
                FROM
                    FT_CLAIM_ERP l
                WHERE
                        l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                        l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
                  AND NOT EXISTS (
                    select rs.CLAIM_NUM from CLAIM_AUDIT_RESULTS rs where   rs.CHECK_TYPE1_CODE  = 'attachmentCheck' and rs.CHECK_RESULT_TYPE!=0 AND rs.CLAIM_NUM = l.CLAIM_NUM

                )
                  AND EXISTS(
                    SELECT
                        c.CLAIM_NUM
                    FROM
                        FT_SUBMIT_CLAIM_LINE c
                    WHERE
                        c.CLAIM_NUM = l.CLAIM_NUM
                      AND c.SUBMIT_FLOW LIKE '省公司%' and c.CLAIM_AUDIT_STATUS in(1,2,4) and c.OPERATE_TYPE =0 )) R
    </select>
    <select id="lastYearTimeAuditDetail" resultType="java.util.HashMap">
        SELECT * FROM (
                          SELECT
                              AUDIT_BUSINESS_TYPE_NAME as CLAIM_TYPE_NAME,     CLAIM_NUM,SUBMIT_FLOW,SUBMIT_USER_REAL_NAME,SUBMIT_TIME,AUDIT_TIME / 60
                          FROM
                              FT_SUBMIT_CLAIM_LINE
                          WHERE
                              1=1
                            AND SUBMIT_TIME <![CDATA[ < ]]> ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00', 'yyyy-MM-dd hh24:mi:ss'), 1)
                            AND SUBMIT_TIME <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00', 'yyyy-MM-dd hh24:mi:ss')
                            AND AUDIT_BUSINESS_TYPE_NAME IS NOT NULL
                            AND SUBMIT_FLOW LIKE '省公司%'   )

    </select>
    <select id="thisYearTimeAutoAuditDetail" resultType="java.util.Map">
        SELECT * FROM (
                          SELECT
                              AUDIT_BUSINESS_TYPE_NAME as CLAIM_TYPE_NAME,     CLAIM_NUM,CREATED_TIME as SUBMIT_TIME,AUDIT_TIME / 60
                          FROM
                              T_AUTO_CHECK_LOG
                          WHERE
                                  CREATED_TIME  <![CDATA[ < ]]> ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-05 00:00:00', 'yyyy-MM-dd hh24:mi:ss'), 1)
                            AND CREATED_TIME  <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00', 'yyyy-MM-dd hh24:mi:ss')
                            AND AUDIT_BUSINESS_TYPE_NAME IS NOT NULL AND  AUTO_SUBMIT_TYPE != 1
        AND APPROVAL_NODE_NAME LIKE '省公司%' AND STATUS = 2 )
    </select>

    <select id="auditDetail" resultType="java.util.Map">

        SELECT
        l.CLAIM_NUM,l.CLAIM_TYPE_NAME,l.COMPANY_NAME,l.GL_DATE ,
        (SELECT
        DISTINCT '是'
        FROM
        FT_SUBMIT_CLAIM_LINE c
        WHERE
        c.CLAIM_NUM = l.CLAIM_NUM
        AND c.SUBMIT_FLOW LIKE '省公司%' and c.CLAIM_AUDIT_STATUS in(1,2,4) ) as AUTOCHECK
        ,(SELECT
        DISTINCT '是'
        FROM
        FT_SUBMIT_CLAIM_LINE c
        WHERE
        c.CLAIM_NUM = l.CLAIM_NUM
        AND c.SUBMIT_FLOW LIKE '省公司%' and c.CLAIM_AUDIT_STATUS = 1 and c.OPERATE_TYPE =0 ) AUTOCHECKPASS
        ,
        (select DISTINCT '是' from T_AUTO_CHECK_LOG rs where rs.AUDIT_BUSINESS_TYPE_NAME IS NOT NULL AND  rs.AUTO_SUBMIT_TYPE != 1 AND rs.APPROVAL_NODE_NAME LIKE '省公司%' AND rs.CLAIM_NUM = l.CLAIM_NUM) as AUTOAUDIT,
        (select  DISTINCT '是' from T_AUTO_CHECK_LOG rs where rs.AUDIT_BUSINESS_TYPE_NAME IS NOT NULL AND  rs.AUTO_SUBMIT_TYPE != 1 AND rs.APPROVAL_NODE_NAME LIKE '省公司%' AND rs.STATUS = 2 AND rs.CLAIM_NUM = l.CLAIM_NUM) as AUTOAUDITPASS
        ,(
        select DISTINCT '是' as a from FT_CLAIM_ERP le where  le.CLAIM_NUM =l.CLAIM_NUM and not EXISTS ( select rs.CLAIM_NUM from CLAIM_AUDIT_RESULTS rs where   rs.CHECK_TYPE1_CODE  = 'invoiceCheck' and rs.CHECK_RESULT_TYPE!=0 AND rs.CLAIM_NUM = le.CLAIM_NUM) and
        EXISTS(
        SELECT
        c.CLAIM_NUM
        FROM
        FT_SUBMIT_CLAIM_LINE c
        WHERE
        c.CLAIM_NUM = le.CLAIM_NUM
        AND c.SUBMIT_FLOW LIKE '省公司%' and c.CLAIM_AUDIT_STATUS in(1,2,4) and c.OPERATE_TYPE =0 )
        ) as INVOICEPASS
        ,(
        select DISTINCT '是' as a from FT_CLAIM_ERP le where  le.CLAIM_NUM =l.CLAIM_NUM and not EXISTS ( select rs.CLAIM_NUM from CLAIM_AUDIT_RESULTS rs where   rs.CHECK_TYPE1_CODE  = 'claimCheck' and rs.CHECK_RESULT_TYPE!=0 AND rs.CLAIM_NUM = le.CLAIM_NUM) and
        EXISTS(
        SELECT
        c.CLAIM_NUM
        FROM
        FT_SUBMIT_CLAIM_LINE c
        WHERE
        c.CLAIM_NUM = le.CLAIM_NUM
        AND c.SUBMIT_FLOW LIKE '省公司%' and c.CLAIM_AUDIT_STATUS in(1,2,4) and c.OPERATE_TYPE =0 )) as CLAIMPASS
        ,( select DISTINCT '是' as a from FT_CLAIM_ERP le where  le.CLAIM_NUM =l.CLAIM_NUM and not EXISTS ( select rs.CLAIM_NUM from CLAIM_AUDIT_RESULTS rs where   rs.CHECK_TYPE1_CODE  = 'attachmentCheck' and rs.CHECK_RESULT_TYPE!=0 AND rs.CLAIM_NUM = le.CLAIM_NUM) and
        EXISTS(
        SELECT
        c.CLAIM_NUM
        FROM
        FT_SUBMIT_CLAIM_LINE c
        WHERE
        c.CLAIM_NUM = le.CLAIM_NUM
        AND c.SUBMIT_FLOW LIKE '省公司%' and c.CLAIM_AUDIT_STATUS in(1,2,4) and c.OPERATE_TYPE =0 )) as ATTACHMENTPASS
        FROM
        FT_CLAIM_ERP l where
            l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
            l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
    </select>
    <select id="allClaimCompDetail" resultType="java.util.Map">
        SELECT
        l.CLAIM_NUM,l.CLAIM_TYPE_NAME,l.GL_DATE,r.CHECK_BUSINESS_TYPE_NAME,
        CASE l.COMPANY_CODE
        WHEN '303710' THEN '省本部'
        WHEN '303726' THEN '抚州'
        WHEN '303788' THEN '南昌'
        WHEN '303721' THEN '鹰潭'
        WHEN '303729' THEN '赣州'
        WHEN '303727' THEN '宜春'
        WHEN '303728' THEN '吉安'
        WHEN '303722' THEN '新余'
        WHEN '303730' THEN '景德镇'
        WHEN '303725' THEN '上饶'
        WHEN '303724' THEN '九江'
        WHEN '303731' THEN '萍乡'
        WHEN '303783' THEN '虚拟现实中心'
        ELSE '其他' END  as COMPANY_NAME,
        ( select DISTINCT '是'  FROM
        T_VIEW_CHECK_RESULT_LOG rl
        where rl.CLAIM_NUM = l.CLAIM_NUM) as USEVIEW
        FROM
        FT_CLAIM_ERP l
        left join CLAIM_AUDIT_RESULTS r on r.CLAIM_NUM = l.CLAIM_NUM
        WHERE
        l.COMPANY_CODE LIKE '30%'
        AND l.GL_DATE <![CDATA[ < ]]>  ADD_MONTHS(to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss'), 1) AND
                l.GL_DATE <![CDATA[ >= ]]> to_date(#{param.glDate}<![CDATA[ || ]]>'-01 00:00:00','yyyy-MM-dd hh24:mi:ss')
        group by  l.CLAIM_NUM,l.CLAIM_TYPE_NAME,l.GL_DATE,r.CHECK_BUSINESS_TYPE_NAME,l.COMPANY_CODE
    </select>

</mapper>
