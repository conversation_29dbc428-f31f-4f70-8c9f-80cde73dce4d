<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, jxcmcc All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: jxcmcc
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.easycloud.jxmcc.ocr.mapper.OcrUploadInvoiceMapper">

  <resultMap id="ocrUploadInvoiceMap" type="com.easycloud.jxcmcc.ocr.entity.OcrUploadInvoice">
                  <id property="id" column="ID"/>
                        <result property="headerId" column="HEADER_ID"/>
                        <result property="businessInvoiceId" column="BUSINESS_INVOICE_ID"/>
                        <result property="invoiceType" column="INVOICE_TYPE"/>
                        <result property="invoiceCode" column="INVOICE_CODE"/>
                        <result property="invoiceNum" column="INVOICE_NUM"/>
                        <result property="invoiceDate" column="INVOICE_DATE"/>
                        <result property="verificationCode" column="VERIFICATION_CODE"/>
                        <result property="companySegment" column="COMPANY_SEGMENT"/>
                        <result property="enterEmployeeId" column="ENTER_EMPLOYEE_ID"/>
                        <result property="purchaseNum" column="PURCHASE_NUM"/>
                        <result property="purchaseName" column="PURCHASE_NAME"/>
                        <result property="priceAmount" column="PRICE_AMOUNT"/>
                        <result property="taxAmount" column="TAX_AMOUNT"/>
                        <result property="totalAmount" column="TOTAL_AMOUNT"/>
                        <result property="salesTaxNum" column="SALES_TAX_NUM"/>
                        <result property="salesName" column="SALES_NAME"/>
                        <result property="taxRate" column="TAX_RATE"/>
                        <result property="passengerIdentity" column="PASSENGER_IDENTITY"/>
                        <result property="internal" column="INTERNAL"/>
                        <result property="ticketPrice" column="TICKET_PRICE"/>
                        <result property="airportFund" column="AIRPORT_FUND"/>
                        <result property="fuelSurcharge" column="FUEL_SURCHARGE"/>
                        <result property="otherTax" column="OTHER_TAX"/>
                        <result property="deductVatAmount" column="DEDUCT_VAT_AMOUNT"/>
                        <result property="deductionFlag" column="DEDUCTION_FLAG"/>
                        <result property="passTransFlag" column="PASS_TRANS_FLAG"/>
                        <result property="outputExt" column="OUTPUT_EXT"/>
                        <result property="claimId" column="CLAIM_ID"/>
                        <result property="claimNo" column="CLAIM_NO"/>
                        <result property="invoiceTypeCode" column="INVOICE_TYPE_CODE"/>
                        <result property="approvalId" column="APPROVAL_ID"/>
                        <result property="userName" column="USER_NAME"/>
                        <result property="createdRealName" column="CREATED_REAL_NAME"/>
                        <result property="deptName" column="DEPT_NAME"/>
                        <result property="deptId" column="DEPT_ID"/>
                        <result property="createdBy" column="CREATED_BY"/>
                        <result property="createdTime" column="CREATED_TIME"/>
                        <result property="updatedBy" column="UPDATED_BY"/>
                        <result property="updatedTime" column="UPDATED_TIME"/>
            </resultMap>

  <update id="updateInvoiceStatus">
         UPDATE OCR_UPLOAD_INVOICE SET INVOICE_GROUP_TYPE = #{status}, CLAIM_NO = #{claimNo} WHERE ID = #{ocrId}
  </update>

  <select id="getHeaderId" parameterType="java.lang.String" resultType="com.easycloud.jxcmcc.ocr.entity.OcrUploadInvoice">
        SELECT * FROM OCR_UPLOAD_INVOICE
        WHERE HEADER_ID = #{headerId}
  </select>

      <select id="queryById" resultType="com.easycloud.jxcmcc.ocr.entity.OcrUploadInvoice">
          select * from OCR_UPLOAD_INVOICE where id = #{id}
    </select>

      <delete id="delById">
            delete from OCR_UPLOAD_INVOICE where id = #{id}
      </delete>
</mapper>
