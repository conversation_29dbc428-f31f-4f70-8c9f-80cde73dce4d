
package com.cmdi.b2bjoy.hs.std.travel.service;



import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;



@XmlRegistry
public class ObjectFactory {

    private final static QName _ImportTravelAppInfoSrv10_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "importTravelAppInfoSrv_1_0");
    private final static QName _ImportTravelAppInfoSrv10Response_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "importTravelAppInfoSrv_1_0Response");
    private final static QName _ImportTravelAppInfoSrv11_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "importTravelAppInfoSrv_1_1");
    private final static QName _ImportTravelAppInfoSrv11Response_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "importTravelAppInfoSrv_1_1Response");
    private final static QName _ImportTravelAppInfoSrv20_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "importTravelAppInfoSrv_2_0");
    private final static QName _ImportTravelAppInfoSrv20Response_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "importTravelAppInfoSrv_2_0Response");
    private final static QName _ImportTravelAppInfoSrv12_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "importTravelAppInfoSrv_1_2");
    private final static QName _ImportTravelAppInfoSrv12Response_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "importTravelAppInfoSrv_1_2Response");
    private final static QName _InquiryTravelBalanceErpSrv10_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "inquiryTravelBalanceErpSrv_1_0");
    private final static QName _InquiryTravelBalanceErpSrv10Response_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "inquiryTravelBalanceErpSrv_1_0Response");
    private final static QName _ImportTravelAppInfoSrv_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "importTravelAppInfoSrv");
    private final static QName _ImportTravelAppInfoSrvResponse_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "importTravelAppInfoSrvResponse");
    private final static QName _ImportTravelAppInfoSrv13_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "importTravelAppInfoSrv_1_3");
    private final static QName _ImportTravelAppInfoSrv13Response_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "importTravelAppInfoSrv_1_3Response");
    private final static QName _CancelTravelAppInfoSrv10_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "cancelTravelAppInfoSrv_1_0");
    private final static QName _CancelTravelAppInfoSrv10Response_QNAME = new QName("http://service.travel.std.hs.b2bjoy.cmdi.com/", "cancelTravelAppInfoSrv_1_0Response");

    
    public ObjectFactory() {
    }

    
    public ImportTravelAppInfoSrv10 createImportTravelAppInfoSrv10() {
        return new ImportTravelAppInfoSrv10();
    }

    
    public ImportTravelAppInfoSrv10Response createImportTravelAppInfoSrv10Response() {
        return new ImportTravelAppInfoSrv10Response();
    }

    
    public ImportTravelAppInfoSrv11 createImportTravelAppInfoSrv11() {
        return new ImportTravelAppInfoSrv11();
    }

    
    public ImportTravelAppInfoSrv11Response createImportTravelAppInfoSrv11Response() {
        return new ImportTravelAppInfoSrv11Response();
    }

    
    public ImportTravelAppInfoSrv20 createImportTravelAppInfoSrv20() {
        return new ImportTravelAppInfoSrv20();
    }

    
    public ImportTravelAppInfoSrv20Response createImportTravelAppInfoSrv20Response() {
        return new ImportTravelAppInfoSrv20Response();
    }

    
    public ImportTravelAppInfoSrv12 createImportTravelAppInfoSrv12() {
        return new ImportTravelAppInfoSrv12();
    }

    
    public ImportTravelAppInfoSrv12Response createImportTravelAppInfoSrv12Response() {
        return new ImportTravelAppInfoSrv12Response();
    }

    
    public InquiryTravelBalanceErpSrv10 createInquiryTravelBalanceErpSrv10() {
        return new InquiryTravelBalanceErpSrv10();
    }

    
    public InquiryTravelBalanceErpSrv10Response createInquiryTravelBalanceErpSrv10Response() {
        return new InquiryTravelBalanceErpSrv10Response();
    }

    
    public ImportTravelAppInfoSrv createImportTravelAppInfoSrv() {
        return new ImportTravelAppInfoSrv();
    }

    
    public ImportTravelAppInfoSrvResponse createImportTravelAppInfoSrvResponse() {
        return new ImportTravelAppInfoSrvResponse();
    }

    
    public ImportTravelAppInfoSrv13 createImportTravelAppInfoSrv13() {
        return new ImportTravelAppInfoSrv13();
    }

    
    public ImportTravelAppInfoSrv13Response createImportTravelAppInfoSrv13Response() {
        return new ImportTravelAppInfoSrv13Response();
    }

    
    public CancelTravelAppInfoSrv10 createCancelTravelAppInfoSrv10() {
        return new CancelTravelAppInfoSrv10();
    }

    
    public CancelTravelAppInfoSrv10Response createCancelTravelAppInfoSrv10Response() {
        return new CancelTravelAppInfoSrv10Response();
    }

    
    public MsgHeader createMsgHeader() {
        return new MsgHeader();
    }

    
    public ImportTravelAppReq10 createImportTravelAppReq10() {
        return new ImportTravelAppReq10();
    }

    
    public ImportTravelAppRes10 createImportTravelAppRes10() {
        return new ImportTravelAppRes10();
    }

    
    public ImportTravelAppReq20 createImportTravelAppReq20() {
        return new ImportTravelAppReq20();
    }

    
    public ImportTravelAppRes13 createImportTravelAppRes13() {
        return new ImportTravelAppRes13();
    }

    
    public InquiryBalanceErpReq10 createInquiryBalanceErpReq10() {
        return new InquiryBalanceErpReq10();
    }

    
    public InquiryBalanceErpRes10 createInquiryBalanceErpRes10() {
        return new InquiryBalanceErpRes10();
    }

    
    public MsgHeader1 createMsgHeader1() {
        return new MsgHeader1();
    }

    
    public ImportTravelAppRes createImportTravelAppRes() {
        return new ImportTravelAppRes();
    }

    
    public ErrorCollection1 createErrorCollection1() {
        return new ErrorCollection1();
    }

    
    public ResponseCollection1 createResponseCollection1() {
        return new ResponseCollection1();
    }

    
    public ImportTravelAppReq13 createImportTravelAppReq13() {
        return new ImportTravelAppReq13();
    }

    
    public CancelTravelAppReq10 createCancelTravelAppReq10() {
        return new CancelTravelAppReq10();
    }

    
    public CancelTravelAppRes10 createCancelTravelAppRes10() {
        return new CancelTravelAppRes10();
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "importTravelAppInfoSrv_1_0")
    public JAXBElement<ImportTravelAppInfoSrv10> createImportTravelAppInfoSrv10(ImportTravelAppInfoSrv10 value) {
        return new JAXBElement<ImportTravelAppInfoSrv10>(_ImportTravelAppInfoSrv10_QNAME, ImportTravelAppInfoSrv10 .class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "importTravelAppInfoSrv_1_0Response")
    public JAXBElement<ImportTravelAppInfoSrv10Response> createImportTravelAppInfoSrv10Response(ImportTravelAppInfoSrv10Response value) {
        return new JAXBElement<ImportTravelAppInfoSrv10Response>(_ImportTravelAppInfoSrv10Response_QNAME, ImportTravelAppInfoSrv10Response.class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "importTravelAppInfoSrv_1_1")
    public JAXBElement<ImportTravelAppInfoSrv11> createImportTravelAppInfoSrv11(ImportTravelAppInfoSrv11 value) {
        return new JAXBElement<ImportTravelAppInfoSrv11>(_ImportTravelAppInfoSrv11_QNAME, ImportTravelAppInfoSrv11 .class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "importTravelAppInfoSrv_1_1Response")
    public JAXBElement<ImportTravelAppInfoSrv11Response> createImportTravelAppInfoSrv11Response(ImportTravelAppInfoSrv11Response value) {
        return new JAXBElement<ImportTravelAppInfoSrv11Response>(_ImportTravelAppInfoSrv11Response_QNAME, ImportTravelAppInfoSrv11Response.class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "importTravelAppInfoSrv_2_0")
    public JAXBElement<ImportTravelAppInfoSrv20> createImportTravelAppInfoSrv20(ImportTravelAppInfoSrv20 value) {
        return new JAXBElement<ImportTravelAppInfoSrv20>(_ImportTravelAppInfoSrv20_QNAME, ImportTravelAppInfoSrv20 .class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "importTravelAppInfoSrv_2_0Response")
    public JAXBElement<ImportTravelAppInfoSrv20Response> createImportTravelAppInfoSrv20Response(ImportTravelAppInfoSrv20Response value) {
        return new JAXBElement<ImportTravelAppInfoSrv20Response>(_ImportTravelAppInfoSrv20Response_QNAME, ImportTravelAppInfoSrv20Response.class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "importTravelAppInfoSrv_1_2")
    public JAXBElement<ImportTravelAppInfoSrv12> createImportTravelAppInfoSrv12(ImportTravelAppInfoSrv12 value) {
        return new JAXBElement<ImportTravelAppInfoSrv12>(_ImportTravelAppInfoSrv12_QNAME, ImportTravelAppInfoSrv12 .class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "importTravelAppInfoSrv_1_2Response")
    public JAXBElement<ImportTravelAppInfoSrv12Response> createImportTravelAppInfoSrv12Response(ImportTravelAppInfoSrv12Response value) {
        return new JAXBElement<ImportTravelAppInfoSrv12Response>(_ImportTravelAppInfoSrv12Response_QNAME, ImportTravelAppInfoSrv12Response.class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "inquiryTravelBalanceErpSrv_1_0")
    public JAXBElement<InquiryTravelBalanceErpSrv10> createInquiryTravelBalanceErpSrv10(InquiryTravelBalanceErpSrv10 value) {
        return new JAXBElement<InquiryTravelBalanceErpSrv10>(_InquiryTravelBalanceErpSrv10_QNAME, InquiryTravelBalanceErpSrv10 .class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "inquiryTravelBalanceErpSrv_1_0Response")
    public JAXBElement<InquiryTravelBalanceErpSrv10Response> createInquiryTravelBalanceErpSrv10Response(InquiryTravelBalanceErpSrv10Response value) {
        return new JAXBElement<InquiryTravelBalanceErpSrv10Response>(_InquiryTravelBalanceErpSrv10Response_QNAME, InquiryTravelBalanceErpSrv10Response.class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "importTravelAppInfoSrv")
    public JAXBElement<ImportTravelAppInfoSrv> createImportTravelAppInfoSrv(ImportTravelAppInfoSrv value) {
        return new JAXBElement<ImportTravelAppInfoSrv>(_ImportTravelAppInfoSrv_QNAME, ImportTravelAppInfoSrv.class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "importTravelAppInfoSrvResponse")
    public JAXBElement<ImportTravelAppInfoSrvResponse> createImportTravelAppInfoSrvResponse(ImportTravelAppInfoSrvResponse value) {
        return new JAXBElement<ImportTravelAppInfoSrvResponse>(_ImportTravelAppInfoSrvResponse_QNAME, ImportTravelAppInfoSrvResponse.class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "importTravelAppInfoSrv_1_3")
    public JAXBElement<ImportTravelAppInfoSrv13> createImportTravelAppInfoSrv13(ImportTravelAppInfoSrv13 value) {
        return new JAXBElement<ImportTravelAppInfoSrv13>(_ImportTravelAppInfoSrv13_QNAME, ImportTravelAppInfoSrv13 .class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "importTravelAppInfoSrv_1_3Response")
    public JAXBElement<ImportTravelAppInfoSrv13Response> createImportTravelAppInfoSrv13Response(ImportTravelAppInfoSrv13Response value) {
        return new JAXBElement<ImportTravelAppInfoSrv13Response>(_ImportTravelAppInfoSrv13Response_QNAME, ImportTravelAppInfoSrv13Response.class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "cancelTravelAppInfoSrv_1_0")
    public JAXBElement<CancelTravelAppInfoSrv10> createCancelTravelAppInfoSrv10(CancelTravelAppInfoSrv10 value) {
        return new JAXBElement<CancelTravelAppInfoSrv10>(_CancelTravelAppInfoSrv10_QNAME, CancelTravelAppInfoSrv10 .class, null, value);
    }

    
    @XmlElementDecl(namespace = "http://service.travel.std.hs.b2bjoy.cmdi.com/", name = "cancelTravelAppInfoSrv_1_0Response")
    public JAXBElement<CancelTravelAppInfoSrv10Response> createCancelTravelAppInfoSrv10Response(CancelTravelAppInfoSrv10Response value) {
        return new JAXBElement<CancelTravelAppInfoSrv10Response>(_CancelTravelAppInfoSrv10Response_QNAME, CancelTravelAppInfoSrv10Response.class, null, value);
    }
}
