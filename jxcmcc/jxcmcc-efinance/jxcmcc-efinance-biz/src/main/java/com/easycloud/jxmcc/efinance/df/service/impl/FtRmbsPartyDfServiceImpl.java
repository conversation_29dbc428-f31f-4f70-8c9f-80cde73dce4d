package com.easycloud.jxmcc.efinance.df.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsPartyDf;
import com.easycloud.jxmcc.efinance.df.mapper.FtRmbsPartyDfMapper;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsPartyDfService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class FtRmbsPartyDfServiceImpl extends ServiceImpl<FtRmbsPartyDfMapper, FtRmbsPartyDf> implements FtRmbsPartyDfService {

    @Override
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！");
        }
        try {
            List<FtRmbsPartyDf> importClaimLine;
            //解析导入EXCEL数据
            importClaimLine = EasyExcel.read(file.getInputStream())
                    .head(FtRmbsPartyDf.class)
                    .sheet(0)
                    .headRowNumber(1)
                    .doReadSync();

            if (importClaimLine.size() == 0) {
                return R.error("导入的数据为空");
            }
            int start = 1;
            for (int i = 0; i < importClaimLine.size(); i++) {
                FtRmbsPartyDf ftRmbsPartyDf = importClaimLine.get(i);
                //检查导入是否合法
                if (StringUtils.isBlank(ftRmbsPartyDf.getPartyName())) {
                    return R.error("第" + (i + start) + "党组织名称不能为空");
                }
                if (StringUtils.isBlank(ftRmbsPartyDf.getYear())) {
                    return R.error("第" + (i + start) + "年份不能为空");
                }
                if (ftRmbsPartyDf.getAmount() == null) {
                    return R.error("第" + (i + start) + "年度预算金额不能为空");
                }
            }
            List<FtRmbsPartyDf> list = importClaimLine.stream().map(t -> {
                FtRmbsPartyDf partyDf = new FtRmbsPartyDf();
                //数据类型必须保持一致，否则copy该字段为null
                BeanUtils.copyProperties(t, partyDf);
                return partyDf;
            }).collect(Collectors.toList());
            this.saveBatch(list);
            return R.ok(list.size());
        } catch (Exception e) {
            return R.error("上传异常");
        }
    }
}
