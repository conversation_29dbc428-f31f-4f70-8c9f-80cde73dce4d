/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 党费系统支付明细
 *
 * <AUTHOR>
 * @date 2022-03-28 15:00:54
 */
@Data
@TableName("FT_RMBS_PAYLIST_DF")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "党费系统支付明细")
public class FtRmbsPaylistDf extends Model<FtRmbsPaylistDf> {
private static final long serialVersionUID = 1L;

    /**
     * ;
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String payLineId;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String claimId;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String claimNo;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String vendorName;
    /**
     * ;
     */
    @ApiModelProperty(value="供应商开户名")
    @Excel(name = "供应商开户名")
    private String accountName;
    /**
     * ;
     */
    @ApiModelProperty(value="供应商开户行")
    @Excel(name = "供应商开户行")
    private String bankName;
    /**
     * ;
     */
    @ApiModelProperty(value="供应商账号")
    @Excel(name = "供应商账号")
    private String bankAccount;
    /**
     * ;
     */
    @ApiModelProperty(value="付款金额")
    @Excel(name = "付款金额")
    private BigDecimal payAmount;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private BigDecimal listAmount;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private BigDecimal notPayAmount;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private Integer isSelfPay;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String status;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String applyComId;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String currency;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String summary;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String procResult;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String isRealVendor;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private Long relatedPayLineId;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private LocalDateTime paidTime;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private BigDecimal paidAmount;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String attribute1;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String apNumber;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String invoiceType;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String feeItemName;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String payObject;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String bankType;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String bankLocProvince;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String bankLocCity;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String vendorNo;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private LocalDateTime synchroTime;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private LocalDateTime payTime;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String attribute2;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String bankLineId;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private String bankLineType;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private BigDecimal nationalcapital;
    /**
     * ;
     */
    @ApiModelProperty(value="")
    @Excel(name = "")
    private BigDecimal selfcapital;
    }
