/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 年度预算
 *
 * <AUTHOR>
 * @date 2022-08-05 16:07:57
 */
@Data
@TableName("FT_RMBS_PARTY_DF")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "年度预算")
public class FtRmbsPartyDf extends Model<FtRmbsPartyDf> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键;
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 党组织名称
     */
    @ApiModelProperty(value = "党组织名称")
    @ExcelProperty(value = "党组织名称",index = 0)
    @Excel(name = "党组织名称")
    private String partyName;
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @ExcelProperty(value = "年份",index = 1)
    @Excel(name = "年份")
    private String year;
    /**
     * 预算金额
     */
    @ApiModelProperty(value = "年度预算")
    @ExcelProperty(value = "年度预算",index = 2)
    @Excel(name = "年度预算")
    private BigDecimal amount;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;
}
