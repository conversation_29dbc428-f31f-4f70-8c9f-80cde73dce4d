/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.protal.controller;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.util.WebUtils;
import com.easycloud.jxcmcc.common.data.builder.QueryWrapperBuilder;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.minio.MinioProperties;
import com.easycloud.jxcmcc.common.minio.service.MinioTemplate;
import com.easycloud.jxcmcc.common.security.annotation.Inner;
import com.easycloud.jxmcc.acct.entity.FattachmentUploadInfoList;
import com.easycloud.jxmcc.acct.entity.FprivateCarTollInvoiceDataOne;
import com.easycloud.jxmcc.protal.entity.FtAcctDatabaseFile;
import com.easycloud.jxmcc.protal.service.FtAcctDatabaseFileService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.InputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;


/**
 * 业财数据资源库
 *
 * <AUTHOR> code generator
 * @date 2023-06-07 16:48:47
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ftacctdatabasefile" )
@Api(value = "ftacctdatabasefile", tags = "业财数据资源库管理")
public class FtAcctDatabaseFileController {

    private final  FtAcctDatabaseFileService ftAcctDatabaseFileService;
    private final  MinioTemplate minioTemplate;
    private final MinioProperties minioProperties;
    /**
     * 分页查询
     * @param page 分页对象
     * @param ftAcctDatabaseFile 业财数据资源库
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询业财数据资源库" )
    @GetMapping("/page" )
    public R getFtAcctDatabaseFilePage(Page page, FtAcctDatabaseFile ftAcctDatabaseFile) {
        QueryWrapper build = new QueryWrapper<>();
        if (StringUtils.isNotBlank(ftAcctDatabaseFile.getInterfaceTableName())){
            build.like("Interface_Table_Name",ftAcctDatabaseFile.getInterfaceTableName());
        }
        if (StringUtils.isNotBlank(ftAcctDatabaseFile.getFromSystem())){
            build.like("from_Sys_tem",ftAcctDatabaseFile.getFromSystem());
        }
        if (StringUtils.isNotBlank(ftAcctDatabaseFile.getSynchronizationFrequency())){
            build.like("Synchronization_Frequency",ftAcctDatabaseFile.getSynchronizationFrequency());
        }
        build.orderByAsc("SORT");
        return R.ok(ftAcctDatabaseFileService.page(page,build));
    }


    /**
     * 通过id查询业财数据资源库
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询业财数据资源库" )
    @GetMapping("get/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(ftAcctDatabaseFileService.getById(id));
    }

    /**
     * 新增业财数据资源库
     * @param ftAcctDatabaseFile 业财数据资源库
     * @return R
     */
    @ApiOperation(value = "新增业财数据资源库", notes = "新增业财数据资源库")
    @SysLog("新增业财数据资源库" )
    @PostMapping("save")
    @PreAuthorize("@pms.hasPermission('generator_ftacctdatabasefile_add')" )
    public R save(@RequestBody FtAcctDatabaseFile ftAcctDatabaseFile) {
        return R.ok(ftAcctDatabaseFileService.save(ftAcctDatabaseFile));
    }

    /**
     * 修改业财数据资源库
     * @param ftAcctDatabaseFile 业财数据资源库
     * @return R
     */
    @ApiOperation(value = "修改业财数据资源库", notes = "修改业财数据资源库")
    @SysLog("修改业财数据资源库" )
    @PostMapping("update")
    @PreAuthorize("@pms.hasPermission('generator_ftacctdatabasefile_edit')" )
    public R updateById(@RequestBody FtAcctDatabaseFile ftAcctDatabaseFile) {
        return R.ok(ftAcctDatabaseFileService.updateById(ftAcctDatabaseFile));
    }

    /**
     * 通过id删除业财数据资源库
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除业财数据资源库", notes = "通过id删除业财数据资源库")
    @SysLog("通过id删除业财数据资源库" )
    @PostMapping("/delete/{id}" )
    @PreAuthorize("@pms.hasPermission('generator_ftacctdatabasefile_del')" )
    public R removeById(@PathVariable String id) {
        return R.ok(ftAcctDatabaseFileService.removeById(id));
    }


   /**
     * 导入业财数据资源库
     * @return R
     */
    @ApiOperation(value = "导入业财数据资源库", notes = "导入业财数据资源库")
    @SysLog("导入业财数据资源库" )
    @PreAuthorize("@pms.hasPermission('generator_ftacctdatabasefile_import')" )
    @FileImportLogAnnotation(busTable = "业财数据资源库",busSystem = "generator")
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return ftAcctDatabaseFileService.importExcel(file,importParams);
    }

    /**
     * 导出业财数据资源库
     * @return R
     */
    @ApiOperation(value = "导出业财数据资源库", notes = "导出业财数据资源库")
    @SysLog("导出业财数据资源库")
    @PreAuthorize("@pms.hasPermission('generator_ftacctdatabasefile_export')")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, FtAcctDatabaseFile ftAcctDatabaseFile) {
        List<FtAcctDatabaseFile>  ftAcctDatabaseFileList = ftAcctDatabaseFileService.list(Wrappers.lambdaQuery(ftAcctDatabaseFile));
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("业财数据资源库列表", "业财数据资源库"),
                FtAcctDatabaseFile. class,ftAcctDatabaseFileList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出业财数据资源库导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版业财数据资源库文件", notes = "导出导入模版业财数据资源库文件")
    @SysLog("导出业财数据资源库导入模版文件")
    @PreAuthorize("@pms.hasPermission('generator_ftacctdatabasefile_export')")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/ftAcctDatabaseFile.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 获取文件
     *
     * @param id       附件ID
     * @param response
     * @return
     */
    @SysLog(value = "获取文件", demandId = "")
    @Inner(false)
    @GetMapping("/down/{id}")
    public void downloadById(@PathVariable String id, HttpServletResponse response) {
        FtAcctDatabaseFile ftAcctDatabaseFile = ftAcctDatabaseFileService.getById(id);

        if (ObjectUtil.isEmpty(ftAcctDatabaseFile)) {
            throw new RuntimeException("id不存在,请检查");
        }
        try (InputStream inputStream = minioTemplate.getObject(minioProperties.getDefaultBucketName(),ftAcctDatabaseFile.getFilePath())) {
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=%s", URLEncoder.encode(ftAcctDatabaseFile.getFileName(), "UTF-8")));
            response.setContentType("application/octet-stream; charset=UTF-8");
            IoUtil.copy(inputStream, response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
