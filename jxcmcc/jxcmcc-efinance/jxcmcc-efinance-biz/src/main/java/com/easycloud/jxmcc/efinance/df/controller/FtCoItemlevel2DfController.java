/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.efinance.df.entity.FtCoItemlevel2Df;
import com.easycloud.jxmcc.efinance.df.service.FtCoItemlevel2DfService;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;


/**
 * 党费系统业务大类表
 *
 * <AUTHOR>
 * @date 2022-04-02 15:54:00
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ftcoitemlevel2df" )
@Api(value = "ftcoitemlevel2df", tags = "党费系统业务大类表管理")
public class FtCoItemlevel2DfController {

    private final FtCoItemlevel2DfService ftCoItemlevel2DfService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param ftCoItemlevel2Df 党费系统业务大类表
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询党费系统业务大类表" )
    @GetMapping("/page" )
    public R getFtCoItemlevel2DfPage(Page page, FtCoItemlevel2Df ftCoItemlevel2Df) {
        return R.ok(ftCoItemlevel2DfService.page(page, Wrappers.query(ftCoItemlevel2Df)));
    }

    /**
     * 获取业务大类
     * @param fatherItem
     * @return
     */
    @ApiOperation(value = "获取业务大类", notes = "获取业务大类")
    @SysLog("获取业务大类" )
    @GetMapping("/getFtCoItemlevel2" )
    public R getFtCoItemlevel2(String fatherItem) {
        return R.ok(ftCoItemlevel2DfService.getFtCoItemlevel2(fatherItem));
    }


    /**
     * 通过id查询党费系统业务大类表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询党费系统业务大类表" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(ftCoItemlevel2DfService.getById(id));
    }

    /**
     * 新增党费系统业务大类表
     * @param ftCoItemlevel2Df 党费系统业务大类表
     * @return R
     */
    @ApiOperation(value = "新增党费系统业务大类表", notes = "新增党费系统业务大类表")
    @SysLog("新增党费系统业务大类表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('generator_ftcoitemlevel2df_add')" )
    public R save(@RequestBody FtCoItemlevel2Df ftCoItemlevel2Df) {
        return R.ok(ftCoItemlevel2DfService.save(ftCoItemlevel2Df));
    }

    /**
     * 修改党费系统业务大类表
     * @param ftCoItemlevel2Df 党费系统业务大类表
     * @return R
     */
    @ApiOperation(value = "修改党费系统业务大类表", notes = "修改党费系统业务大类表")
    @SysLog("修改党费系统业务大类表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('generator_ftcoitemlevel2df_edit')" )
    public R updateById(@RequestBody FtCoItemlevel2Df ftCoItemlevel2Df) {
        return R.ok(ftCoItemlevel2DfService.updateById(ftCoItemlevel2Df));
    }

    /**
     * 通过id删除党费系统业务大类表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除党费系统业务大类表", notes = "通过id删除党费系统业务大类表")
    @SysLog("通过id删除党费系统业务大类表" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('generator_ftcoitemlevel2df_del')" )
    public R removeById(@PathVariable String id) {
        return R.ok(ftCoItemlevel2DfService.removeById(id));
    }


   /**
     * 导入党费系统业务大类表
     * @return R
     */
    @ApiOperation(value = "导入党费系统业务大类表", notes = "导入党费系统业务大类表")
    @SysLog("导入党费系统业务大类表" )
    @PreAuthorize("@pms.hasPermission('generator_ftcoitemlevel2df_import')" )
    @FileImportLogAnnotation(busTable = "党费系统业务大类表",busSystem = "generator")
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return ftCoItemlevel2DfService.importExcel(file,importParams);
    }

       /**
     * 导出党费系统业务大类表
     * @return R
     */
    @ApiOperation(value = "导出党费系统业务大类表", notes = "导出党费系统业务大类表")
    @SysLog("导出党费系统业务大类表" )
    @PreAuthorize("@pms.hasPermission('generator_ftcoitemlevel2df_export')" )
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, FtCoItemlevel2Df ftCoItemlevel2Df) {
        List<FtCoItemlevel2Df>  ftCoItemlevel2DfList = ftCoItemlevel2DfService.list(Wrappers.lambdaQuery(ftCoItemlevel2Df));
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("党费系统业务大类表列表", "党费系统业务大类表"),
                    FtCoItemlevel2Df.class,ftCoItemlevel2DfList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出党费系统业务大类表导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版党费系统业务大类表文件", notes = "导出导入模版党费系统业务大类表文件")
    @SysLog("导出党费系统业务大类表导入模版文件" )
    @PreAuthorize("@pms.hasPermission('generator_ftcoitemlevel2df_export')" )
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String,Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/FtCoItemlevel2Df.xls",true);
            Workbook workbook= ExcelExportUtil.exportExcel(params,map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
