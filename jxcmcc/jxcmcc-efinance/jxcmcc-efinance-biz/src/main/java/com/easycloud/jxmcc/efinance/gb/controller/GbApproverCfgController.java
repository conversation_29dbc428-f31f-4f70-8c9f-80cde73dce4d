/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.BeanHelperUtil;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.util.WebUtils;
import com.easycloud.jxcmcc.common.data.builder.QueryWrapperBuilder;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.efinance.gb.entity.GbApproverCfg;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfo;
import com.easycloud.jxmcc.efinance.gb.service.GbApproverCfgService;
import io.seata.common.util.StringUtils;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;


/**
 * 国拨项目-流程审批人配置
 *
 * <AUTHOR> code efinance
 * @date 2022-10-18 11:07:15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/gbapprovercfg" )
@Api(value = "gbapprovercfg", tags = "国拨项目-流程审批人配置管理")
public class GbApproverCfgController {

    private final GbApproverCfgService gbApproverCfgService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param gbApproverCfg 国拨项目-流程审批人配置
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询国拨项目-流程审批人配置" )
    @GetMapping("/page" )
    public R getGbApproverCfgPage(Page page, GbApproverCfg gbApproverCfg) {
        QueryWrapper queryWrapper = BeanHelperUtil.setQueryWrapper(gbApproverCfg);
        queryWrapper.eq("DELETE_FLAG","0");
        queryWrapper.orderByDesc("CREATED_TIME");
        return R.ok(gbApproverCfgService.page(page, queryWrapper));
    }


    /**
     * 通过id查询国拨项目-流程审批人配置
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询国拨项目-流程审批人配置" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(gbApproverCfgService.getById(id));
    }

    /**
     * 新增国拨项目-流程审批人配置
     * @param gbApproverCfg 国拨项目-流程审批人配置
     * @return R
     */
    @ApiOperation(value = "新增国拨项目-流程审批人配置", notes = "新增国拨项目-流程审批人配置")
    @SysLog("新增国拨项目-流程审批人配置" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('efinance_gbapprovercfg_add')" )
    public R save(@RequestBody GbApproverCfg gbApproverCfg) {
        return R.ok(gbApproverCfgService.save(gbApproverCfg));
    }

    /**
     * 修改国拨项目-流程审批人配置
     * @param gbApproverCfg 国拨项目-流程审批人配置
     * @return R
     */
    @ApiOperation(value = "修改国拨项目-流程审批人配置", notes = "修改国拨项目-流程审批人配置")
    @SysLog("修改国拨项目-流程审批人配置" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('efinance_gbapprovercfg_edit')" )
    public R updateById(@RequestBody GbApproverCfg gbApproverCfg) {
        return R.ok(gbApproverCfgService.updateById(gbApproverCfg));
    }

    /**
     * 通过id删除国拨项目-流程审批人配置
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除国拨项目-流程审批人配置", notes = "通过id删除国拨项目-流程审批人配置")
    @SysLog("通过id删除国拨项目-流程审批人配置" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('efinance_gbapprovercfg_del')" )
    public R removeById(@PathVariable String id) {
        GbApproverCfg gbApproverCfg = gbApproverCfgService.getById(id);
        gbApproverCfg.setDeleteFlag("1");
        return R.ok(gbApproverCfgService.updateById(gbApproverCfg));
    }


   /**
     * 导入国拨项目-流程审批人配置
     * @return R
     */
    @ApiOperation(value = "导入国拨项目-流程审批人配置", notes = "导入国拨项目-流程审批人配置")
    @SysLog("导入国拨项目-流程审批人配置" )
    @PreAuthorize("@pms.hasPermission('efinance_gbapprovercfg_import')" )
    @PostMapping(value = "/importExcel")
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return gbApproverCfgService.importExcel(file,importParams);
    }

    /**
     * 导出国拨项目-流程审批人配置
     * @return R
     */
    @ApiOperation(value = "导出国拨项目-流程审批人配置", notes = "导出国拨项目-流程审批人配置")
    @SysLog("导出国拨项目-流程审批人配置")
    @PreAuthorize("@pms.hasPermission('efinance_gbapprovercfg_export')")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, GbApproverCfg gbApproverCfg) {
        QueryWrapper queryWrapper = BeanHelperUtil.setQueryWrapper(gbApproverCfg);
        queryWrapper.eq("DELETE_FLAG","0");
        queryWrapper.orderByDesc("CREATED_TIME");
        List<GbApproverCfg>  gbApproverCfgList = gbApproverCfgService.list(queryWrapper);
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("国拨项目-流程审批人配置列表", "国拨项目-流程审批人配置"),
                GbApproverCfg. class,gbApproverCfgList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出国拨项目-流程审批人配置导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版国拨项目-流程审批人配置文件", notes = "导出导入模版国拨项目-流程审批人配置文件")
    @SysLog("导出国拨项目-流程审批人配置导入模版文件")
    @PreAuthorize("@pms.hasPermission('efinance_gbapprovercfg_export')")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/gbApproverCfgTemplate.xlsx", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
