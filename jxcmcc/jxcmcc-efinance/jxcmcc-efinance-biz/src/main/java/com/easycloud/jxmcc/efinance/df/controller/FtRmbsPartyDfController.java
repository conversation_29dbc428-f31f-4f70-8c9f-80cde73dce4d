package com.easycloud.jxmcc.efinance.df.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.util.WebUtils;
import com.easycloud.jxcmcc.common.data.builder.QueryWrapperBuilder;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsPartyDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsPartyDfService;
import com.easycloud.jxmcc.efinance.vo.ClaimLineVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 党费年度预算信息
 *
 * <AUTHOR>
 * @date 2022-03-28 15:00:54
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ftrmbspartydf" )
@Api(value = "ftrmbspartydf", tags = "党费年度预算信息")
public class FtRmbsPartyDfController {
    
    private final FtRmbsPartyDfService ftRmbsPartyDfService;
    
    /**
     * 分页查询
     * @param page 分页对象
     * @param ftRmbsPartyDf 党费年度预算
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询党费年度预算" )
    @GetMapping("/page" )
    public R getFtRmbsConfidentClaimDfPage(Page page, FtRmbsPartyDf ftRmbsPartyDf) {
        QueryWrapper build = QueryWrapperBuilder.create(FtRmbsPartyDf.class).build(WebUtils.getParameterMap());
        build.orderByDesc("YEAR");
        return R.ok(ftRmbsPartyDfService.page(page, build));
    }


    /**
     * 通过id查询党费年度预算
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询党费年度预算" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(ftRmbsPartyDfService.getById(id));
    }

    /**
     * 新增党费年度预算
     * @param ftRmbsPartyDf 党费年度预算
     * @return R
     */
    @ApiOperation(value = "新增党费年度预算", notes = "新增党费年度预算")
    @SysLog("新增党费年度预算" )
    @PostMapping
    public R save(@RequestBody FtRmbsPartyDf ftRmbsPartyDf) {
        return R.ok(ftRmbsPartyDfService.save(ftRmbsPartyDf));
    }
    
    

    /**
     * 修改党费年度预算
     * @param ftRmbsPartyDf 党费年度预算
     * @return R
     */
    @ApiOperation(value = "修改党费年度预算", notes = "修改党费年度预算")
    @SysLog("修改党费年度预算" )
    @PutMapping
    public R updateById(@RequestBody FtRmbsPartyDf ftRmbsPartyDf) {
        return R.ok(ftRmbsPartyDfService.updateById(ftRmbsPartyDf));
    }

    /**
     * 批量删除
     * @return R
     */
    @ApiOperation(value = "批量删除年度预算", notes = "批量删除年度预算")
    @SysLog(value = "批量删除年度预算")
    @PostMapping("/deleteLine")
    public R deleteLine(@RequestBody ClaimLineVo claimLineVo) {
        return R.ok(ftRmbsPartyDfService.removeByIds(claimLineVo.getIdList()));
    }


    /**
     * 通过id删除党费年度预算
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除党费年度预算", notes = "通过id删除党费年度预算")
    @SysLog("通过id删除党费年度预算" )
    @DeleteMapping("/{id}" )
    public R removeById(@PathVariable String id) {
        return R.ok(ftRmbsPartyDfService.removeById(id));
    }


    /**
     * 导入党费年度预算
     * @return R
     */
    @ApiOperation(value = "导入党费年度预算", notes = "导入党费年度预算")
    @SysLog("导入党费年度预算" )
    @FileImportLogAnnotation(busTable = "党费年度预算",busSystem = "df")
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return ftRmbsPartyDfService.importExcel(file,importParams);
    }


    /**
     * 党费年度预算下载模板
     */
    @ApiOperation(value = "下载模板", notes = "下载模板")
    @SysLog(value = "下载模板" ,demandId = "xq_T024DF")
    @GetMapping("/downLoadImportTemplate")
    public void downLoadImportTemplate(HttpServletRequest request, HttpServletResponse response){
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/df/FtRmbsPartyDfTemplate.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导出党费年度预算
     * @return R
     */
    @ApiOperation(value = "导出党费年度预算", notes = "导出党费年度预算")
    @SysLog("导出党费年度预算")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, FtRmbsPartyDf ftRmbsPartyDf) {
        QueryWrapper build = QueryWrapperBuilder.create(FtRmbsPartyDf.class).build(WebUtils.getParameterMap());
        build.orderByDesc("YEAR");
        List<FtRmbsPartyDf> ftRmbsPartyDfList = ftRmbsPartyDfService.list(build);
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("党费年度预算列表", "党费年度预算"),
                    FtRmbsPartyDf. class,ftRmbsPartyDfList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出党费年度预算导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版党费年度预算文件", notes = "导出导入模版党费年度预算文件")
    @SysLog("导出党费年度预算导入模版文件")
    @PreAuthorize("@pms.hasPermission('df_ftrmbsconfidentclaimdf_export')")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/ftRmbsPartyDf.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
