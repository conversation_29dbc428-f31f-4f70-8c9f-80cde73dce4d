/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 国拨项目-流程审批人配置
 *
 * <AUTHOR> code generator
 * @date 2022-10-18 11:07:15
 */
@Data
@TableName("GB_APPROVER_CFG")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "国拨项目-流程审批人配置")
public class GbApproverCfg extends Model<GbApproverCfg> {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "唯一标识")
    private String id;
    /**
     * 责任人用户名
     */
    @ApiModelProperty(value = "责任人用户名")
    @Excel(name = "责任人用户名" ,width = 20)
    private String personLiableNo;
    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @Excel(name = "责任人",width = 15)
    private String personLiableName;
    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    @Excel(name = "项目类型",width = 15)
    private String projectType;
    /**
     * 省公司会计用户名
     */
    @ApiModelProperty(value = "省公司会计用户名")
    @Excel(name = "省公司会计用户名",width = 30)
    private String pcompanyAccountingNo;
    /**
     * 省公司会计
     */
    @ApiModelProperty(value = "省公司会计")
    @Excel(name = "省公司会计",width = 20)
    private String pcompanyAccountingName;
    /**
     * 省项目经理用户名
     */
    @ApiModelProperty(value = "省项目经理用户名")
    @Excel(name = "省项目经理用户名",width = 30)
    private String pprojectManagerNo;
    /**
     * 省项目经理
     */
    @ApiModelProperty(value = "省项目经理")
    @Excel(name = "省项目经理",width = 20)
    private String pprojectManagerName;
    /**
     * 项目经理部门领导用户名
     */
    @ApiModelProperty(value = "项目经理部门领导用户名")
    @Excel(name = "项目经理部门领导用户名",width = 35)
    private String pmDepartmentLeaderNo;
    /**
     * 项目经理部门领导
     */
    @ApiModelProperty(value = "项目经理部门领导")
    @Excel(name = "项目经理部门领导",width = 30)
    private String pmDepartmentLeaderName;
    /**
     * 租户号
     */
    @ApiModelProperty(value = "租户号")
    private Integer tenantId;
    /**
     * 删除标识;删除标识(0=否,1=是)
     */
    @ApiModelProperty(value = "删除标识;删除标识(0=否,1=是)")
    private String deleteFlag;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedTime;
}
