/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.df.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxmcc.efinance.df.entity.FtCoItemlevel3Df;
import com.easycloud.jxmcc.efinance.df.mapper.FtCoItemlevel3DfMapper;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxmcc.efinance.df.service.FtCoItemlevel3DfService;
import org.springframework.stereotype.Service;
import com.easycloud.jxcmcc.common.core.util.R;
import org.springframework.web.multipart.MultipartFile;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import java.util.List;
/**
 * 党费系统业务小类表
 *
 * <AUTHOR>
 * @date 2022-04-02 15:54:00
 */
@Service
public class FtCoItemlevel3DfServiceImpl extends ServiceImpl<FtCoItemlevel3DfMapper, FtCoItemlevel3Df> implements FtCoItemlevel3DfService {



    /**
    * 导入excel
    * @return
    */
    @Override
   public R<Integer>  importExcel(MultipartFile file,JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！");
        }
        try {
            ImportParams params = new ImportParams();
            //解析导入EXCEL数据
            List<FtCoItemlevel3Df> ftCoItemlevel3DfList = ExcelImportUtil.importExcel(file.getInputStream(), FtCoItemlevel3Df.class,
            params);
            this.saveBatch(ftCoItemlevel3DfList);
              return R.ok(ftCoItemlevel3DfList.size());
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return R.failed("导入失败");
    }

    @Override
    public List<FtCoItemlevel3Df> getFtCoItemlevel3(FtCoItemlevel3Df ftCoItemlevel3Df) {
        return baseMapper.getFtCoItemlevel3(ftCoItemlevel3Df);
    }

}
