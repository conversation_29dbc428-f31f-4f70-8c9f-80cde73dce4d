package com.easycloud.jxmcc.efinance.gb.listener;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.easycloud.jxcmcc.common.core.exception.BusinessException;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.efinance.claim.listener.EmisTodoListener;
import com.easycloud.jxmcc.efinance.gb.entity.GbApproverCfg;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfo;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfoSplit;
import com.easycloud.jxmcc.efinance.gb.service.GbApproverCfgService;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectInfoService;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectInfoSplitService;
import com.easycloud.jxmcc.eflow.listener.IProcessListener;
import com.easycloud.jxmcc.eflow.vo.BaseEflowApiVo;
import com.easycloud.jxmcc.eflow.vo.ProcessEflowApiVo;
import com.easycloud.jxmcc.eflow.vo.TaskEflowApiVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 　　* <AUTHOR>
 * 　　* @date 2022/10/18 11:12
 * 国拨信息拆分变更审批流程
 *
 **/
@Component
@AllArgsConstructor
public class GbSplitProjectInfoListener extends EmisTodoListener implements IProcessListener {
    @Autowired
    private GbProjectInfoService gbProjectInfoService;
    @Autowired
    private GbApproverCfgService gbApproverCfgService;
    @Autowired
    private GbProjectInfoSplitService gbProjectInfoSplitService;

    @Override
    public String businessType() {
        return "gbSplitParentProject";
    }

    @Override
    public Map<String, Object> getVariables(BaseEflowApiVo data) {
        GbProjectInfo gbProjectInfo = gbProjectInfoService.getById(data.getBusinessId());
        JxcmccUser jxcmccUser = SecurityUtils.getUser();
        Map<String, Object> map = new HashMap<>();
        map.put("initiator",jxcmccUser.getUser().getUsername());
        map.put("manager",gbProjectInfo.getPersonLiableNo());
        map.put("leader",gbProjectInfo.getPersonLiableNo());
        return map;
    }

    @Override
    public String getTitle(TaskEflowApiVo data) {
        GbProjectInfo gbProjectInfo = gbProjectInfoService.getById(data.getBusinessId());
        return StrUtil.format("关于[国拨项目:{}]的拆分变更申请"
                ,gbProjectInfo.getProjectName()
        );
    }

    @Override
    public void start(ProcessEflowApiVo data) {
        GbProjectInfo gbProjectInfo = gbProjectInfoService.getById(data.getBusinessId());
        gbProjectInfo.setReserveField1(data.getBusinessId());
        gbProjectInfo.setReserveField2("1");
        //修改数据流程状态
        gbProjectInfoService.updateById(gbProjectInfo);
        gbProjectInfoSplitService.update(Wrappers.lambdaUpdate(GbProjectInfoSplit.class).
                set(GbProjectInfoSplit::getReserveField2,"1").
                set(GbProjectInfoSplit::getReserveField1,data.getBusinessId()).
                eq(GbProjectInfoSplit::getProjectNo,gbProjectInfo.getProjectNo()));
    }

    @Override
    public void end(ProcessEflowApiVo data) {
        //修改数据流程状态
        gbProjectInfoService.update(Wrappers.lambdaUpdate(GbProjectInfo.class)
                .set(GbProjectInfo::getReserveField2,"2")
                .eq(GbProjectInfo::getReserveField1,data.getBusinessId()));
        gbProjectInfoSplitService.update(Wrappers.lambdaUpdate(GbProjectInfoSplit.class).
                set(GbProjectInfoSplit::getReserveField2,"2").
                eq(GbProjectInfoSplit::getReserveField1,data.getBusinessId()));
    }

    public void completed(TaskEflowApiVo data) {
        if(data.getVariables().get("leader") != null && data.getVariables().keySet().size() > 7){  //验证总拆分金额等于批复金额
            GbProjectInfo gbProjectInfo = gbProjectInfoService.getById(data.getBusinessId());
            List<GbProjectInfoSplit> gbProjectInfoSplitList = gbProjectInfoSplitService.list(Wrappers.lambdaQuery(GbProjectInfoSplit.class)
                    .eq(GbProjectInfoSplit::getReserveField1,data.getBusinessId()));
            BigDecimal sumSplit = BigDecimal.ZERO;
            for (GbProjectInfoSplit item:gbProjectInfoSplitList){
                sumSplit = sumSplit.add(item.getSplitAmount());
            }
            if(gbProjectInfo.getApprovedAmount().compareTo(sumSplit) != 0){
                throw  new BusinessException("总拆分金额不等于批复金额，提交失败！");
            }
        }
    }
}
