/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.efinance.df.entity.FtEngineProcessCommentDf;
import com.easycloud.jxmcc.efinance.df.service.FtEngineProcessCommentDfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 老党费审批记录表
 *
 * <AUTHOR> code generator
 * @date 2023-02-16 17:17:09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ftengineprocesscommentdf" )
@Api(value = "ftengineprocesscommentdf", tags = "老党费审批记录表管理")
public class FtEngineProcessCommentDfController {

    private final  FtEngineProcessCommentDfService ftEngineProcessCommentDfService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param ftEngineProcessCommentDf 老党费审批记录表
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询老党费审批记录表" )
    @GetMapping("/page" )
    public R getFtEngineProcessCommentDfPage(Page page, FtEngineProcessCommentDf ftEngineProcessCommentDf) {
        return R.ok(ftEngineProcessCommentDfService.page(page, Wrappers.query(ftEngineProcessCommentDf)));
    }


    /**
     * 分页查询
     *
     * @param page                     分页对象
     * @param ftEngineProcessCommentDf 老工会审批记录表
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询老工会审批记录表")
    @GetMapping("/myPage")
    public R myPage(Page page, FtEngineProcessCommentDf ftEngineProcessCommentDf) {
        return R.ok(ftEngineProcessCommentDfService.myPage(page,ftEngineProcessCommentDf));
    }


    /**
     * 通过id查询老党费审批记录表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询老党费审批记录表" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(ftEngineProcessCommentDfService.getById(id));
    }

    /**
     * 新增老党费审批记录表
     * @param ftEngineProcessCommentDf 老党费审批记录表
     * @return R
     */
    @ApiOperation(value = "新增老党费审批记录表", notes = "新增老党费审批记录表")
    @SysLog("新增老党费审批记录表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('efinance.df_ftengineprocesscommentdf_add')" )
    public R save(@RequestBody FtEngineProcessCommentDf ftEngineProcessCommentDf) {
        return R.ok(ftEngineProcessCommentDfService.save(ftEngineProcessCommentDf));
    }

    /**
     * 修改老党费审批记录表
     * @param ftEngineProcessCommentDf 老党费审批记录表
     * @return R
     */
    @ApiOperation(value = "修改老党费审批记录表", notes = "修改老党费审批记录表")
    @SysLog("修改老党费审批记录表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('efinance.df_ftengineprocesscommentdf_edit')" )
    public R updateById(@RequestBody FtEngineProcessCommentDf ftEngineProcessCommentDf) {
        return R.ok(ftEngineProcessCommentDfService.updateById(ftEngineProcessCommentDf));
    }

    /**
     * 通过id删除老党费审批记录表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除老党费审批记录表", notes = "通过id删除老党费审批记录表")
    @SysLog("通过id删除老党费审批记录表" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('efinance.df_ftengineprocesscommentdf_del')" )
    public R removeById(@PathVariable Integer id) {
        return R.ok(ftEngineProcessCommentDfService.removeById(id));
    }


   /**
     * 导入老党费审批记录表
     * @return R
     */
    @ApiOperation(value = "导入老党费审批记录表", notes = "导入老党费审批记录表")
    @SysLog("导入老党费审批记录表" )
    @PreAuthorize("@pms.hasPermission('efinance.df_ftengineprocesscommentdf_import')" )
    @FileImportLogAnnotation(busTable = "老党费审批记录表",busSystem = "efinance.df")
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return ftEngineProcessCommentDfService.importExcel(file,importParams);
    }

    /**
     * 导出老党费审批记录表
     * @return R
     */
    @ApiOperation(value = "导出老党费审批记录表", notes = "导出老党费审批记录表")
    @SysLog("导出老党费审批记录表")
    @PreAuthorize("@pms.hasPermission('efinance.df_ftengineprocesscommentdf_export')")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, FtEngineProcessCommentDf ftEngineProcessCommentDf) {
        List<FtEngineProcessCommentDf>  ftEngineProcessCommentDfList = ftEngineProcessCommentDfService.list(Wrappers.lambdaQuery(ftEngineProcessCommentDf));
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("老党费审批记录表列表", "老党费审批记录表"),
                FtEngineProcessCommentDf. class,ftEngineProcessCommentDfList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出老党费审批记录表导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版老党费审批记录表文件", notes = "导出导入模版老党费审批记录表文件")
    @SysLog("导出老党费审批记录表导入模版文件")
    @PreAuthorize("@pms.hasPermission('efinance.df_ftengineprocesscommentdf_export')")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/ftEngineProcessCommentDf.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
