/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.gb.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxmcc.efinance.gb.entity.GbApproverCfg;
import com.easycloud.jxmcc.efinance.gb.mapper.GbApproverCfgMapper;
import com.easycloud.jxmcc.efinance.gb.service.GbApproverCfgService;
import com.easycloud.jxmcc.efinance.util.CheckObjectIsNullUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 国拨项目-流程审批人配置
 *
 * <AUTHOR> code generator
 * @date 2022-10-18 11:07:15
 */
@Service
@Slf4j
public class GbApproverCfgServiceImpl extends ServiceImpl<GbApproverCfgMapper, GbApproverCfg> implements GbApproverCfgService {


    /**
    * 导入excel
    * @return
    */
    @Override
    public R<Integer> importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！" );
        }
        //导入类型，0：覆盖导入，1：增量导入
        String type = importParams.getType();

        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        //解析导入EXCEL数据
        List<GbApproverCfg> gbApproverCfgList = ExcelImportUtil.importExcel(file.getInputStream(), GbApproverCfg.class, params);
        // 清除空白行
        gbApproverCfgList.removeIf(item -> CheckObjectIsNullUtils.objCheckIsNull(item));
        // 判断是否有数据
        if(null == gbApproverCfgList || gbApproverCfgList.size() < 1){
            return R.ok(0, "文件解析成功，数据行为0");
        }
        // 校验数据
        List<String> errorMsgList = new ArrayList<>();
        StringBuffer sb = null;
        int index = 1;
        for (GbApproverCfg gbApproverCfg : gbApproverCfgList) {
            sb = new StringBuffer();
            if(null == gbApproverCfg.getPersonLiableNo() || gbApproverCfg.getPersonLiableNo().equals("")){
                sb.append("责任人用户名未填写|");
            }
            if(null == gbApproverCfg.getPersonLiableName() || gbApproverCfg.getPersonLiableName().equals("")){
                sb.append("责任人未填写|");
            }
            if(null == gbApproverCfg.getProjectType() || gbApproverCfg.getProjectType().equals("")){
                sb.append("项目类型未填写|");
            }
            if(null == gbApproverCfg.getPcompanyAccountingNo() || gbApproverCfg.getPcompanyAccountingNo().equals("")){
                sb.append("省公司会计用户名未填写|");
            }
            if(null == gbApproverCfg.getPcompanyAccountingName() || gbApproverCfg.getPcompanyAccountingName().equals("")){
                sb.append("省公司会计未填写|");
            }
            if(null == gbApproverCfg.getPprojectManagerNo() || gbApproverCfg.getPprojectManagerNo().equals("")){
                sb.append("省项目经理用户名未填写|");
            }
            if(null == gbApproverCfg.getPprojectManagerName() || gbApproverCfg.getPprojectManagerName().equals("")){
                sb.append("省项目经理未填写|");
            }
            if(null == gbApproverCfg.getPmDepartmentLeaderNo() || gbApproverCfg.getPmDepartmentLeaderNo().equals("")){
                sb.append("项目经理部门领导用户名未填写|");
            }
            if(null == gbApproverCfg.getPmDepartmentLeaderName() || gbApproverCfg.getPmDepartmentLeaderName().equals("")){
                sb.append("项目经理部门领导未填写|");
            }

            gbApproverCfg.setDeleteFlag("0");

            // 拼接错误信息
            if(sb.length() > 0){
                errorMsgList.add(String.format("第%s行数据：%s", index, sb.toString()));
            }

            // 行下标增加
            index += 1;
        }

        // 判断是否有错误信息
        if(null != errorMsgList && errorMsgList.size() > 0){
            return R.failed(0, String.join(",", errorMsgList));
        }

        // 覆盖导入删除数据
        if(type.equals("0")){
            this.remove(Wrappers.lambdaQuery(GbApproverCfg.class));
        }

        // 更新数据
        long start = System.currentTimeMillis();
        this.saveBatch(gbApproverCfgList);
        long end = System.currentTimeMillis();
        log.warn("耗时：{}m", (end - start) / 1000);
        return R.ok(gbApproverCfgList.size(),"导入成功(耗时：" + (end - start) / 1000 + "m)");
    }

}
