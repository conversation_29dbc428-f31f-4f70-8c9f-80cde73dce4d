/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 党费系统报账行明细
 *
 * <AUTHOR>
 * @date 2022-03-28 15:00:54
 */
@Data
@TableName("FT_RMBS_CLAIM_LINE_DF")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "党费系统报账行明细")
public class FtRmbsClaimLineDf extends Model<FtRmbsClaimLineDf> {
private static final long serialVersionUID = 1L;

    /**
     * ;
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String claimLineId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String claimId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String item3Id;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String item3Name;
    /**
     * ;
     */
    @ApiModelProperty(value="小计")
    @Excel(name = "小计")
    private BigDecimal applyAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal listAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal unitAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer quantity;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String invoiceType;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String costSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String costSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String relatedTradeType;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorClientName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorClientRealName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer vendorClientId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorClientNo;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorClientBank;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorClientAccountNo;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorClientAccountName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer vendorClientSiteId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorClientSiteCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apProjectSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apProjectSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apIsProjectActivity;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String liabilityAccount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String itemTypeId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String itemTypeName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apBandSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apBandSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String drSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String drSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String crSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String crSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apIcSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apIcSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String arReceivablesTrxName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String taskNumber;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String taskName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal budgetAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String budgetFlag;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer budgetLockId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String budgetSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String budgetSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String dispatchRule;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer setOfBooksId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer orgId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer batchSourceId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String batchName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime feeStartDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime feeEndDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal amorizeAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime amortizeStartDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime amortizeEndDate;
    /**
     * ;
     */
    @ApiModelProperty(value="备注")
    @Excel(name = "备注")
    private String claimLineDesc;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String payReceiveStatus;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String attributeCategory;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String expenditureType;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String expenditureTypeId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal dispatchRuleQua;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String lineDesc;
    /**
     * ;
     */
    @ApiModelProperty(value="费用类型")
    @Excel(name = "费用类型")
    private String travelTypeId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String travelTypeName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String travelFrom;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String travelTo;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vehicle;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vehicleId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer days;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal fareCar;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal fareBed;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal fareFood;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal fareOther;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String fareReciever;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String fareRecieverId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer attachNumber;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime receiveDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String c01;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String c02;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String c03;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String c04;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String c05;
    /**
     * ;
     */
    @ApiModelProperty(value="天数/次数")
    @Excel(name = "天数/次数")
    private BigDecimal f01;
    /**
     * ;
     */
    @ApiModelProperty(value="数量")
    @Excel(name = "数量")
    private BigDecimal f02;
    /**
     * ;
     */
    @ApiModelProperty(value="单价/标准")
    @Excel(name = "单价/标准")
    private BigDecimal f03;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal f04;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal f05;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer i01;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer i02;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer i03;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime d01;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime d02;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime d03;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime t01;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime t02;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern="yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime t03;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal projectId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String projectNum;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String projectName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Long budgetItemNumber;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Long arReceivablesTrxId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apNumber;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String budgetPrjName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String budgetPrjCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String withholdingContrNo;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String withholdingContrName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal withholdingBeginingBalance;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal withholdingWriteOffOfMonth;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal withholdingAbatementOfMonth;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal withholdingBalanceOfMonth;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String withholdingDetail;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal taxBase;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal taxAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String erpImportStatus;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String erpImportStatusName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String bankType;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String bankLocProvince;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String bankLocCity;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorCategory;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String contractExist;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String contractLink;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String fywdsx;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String fywdsxName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String voucherCategory;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String taxCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String voucherCategoryName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal inputTaxRate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal inputTaxAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal priceAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal outputTaxAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal outputTaxRate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String isEquateSale;
    }
