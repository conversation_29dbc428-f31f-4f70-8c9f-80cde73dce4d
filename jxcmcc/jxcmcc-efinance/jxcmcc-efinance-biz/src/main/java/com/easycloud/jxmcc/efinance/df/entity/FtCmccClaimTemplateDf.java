/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 党费业务大小类对应表
 *
 * <AUTHOR>
 * @date 2022-08-12 12:04:12
 */
@Data
@TableName("FT_CMCC_CLAIM_TEMPLATE_DF")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "党费业务大小类对应表")
public class FtCmccClaimTemplateDf extends Model<FtCmccClaimTemplateDf> {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String id;
    /**
     * 报账单Id
     */
    @ApiModelProperty(value = "报账单Id")
    @Excel(name = "报账单Id")
    private String itemId;
    /**
     * 报账单名称
     */
    @ApiModelProperty(value = "报账单名称")
    @Excel(name = "报账单名称")
    private String itemName;
    /**
     * 集团报账单CODE
     */
    @ApiModelProperty(value = "集团报账单CODE")
    @Excel(name = "集团报账单CODE")
    private String claimTypeCode;
    /**
     * 特殊地市标识
     */
    @ApiModelProperty(value = "特殊地市标识")
    @Excel(name = "特殊地市标识")
    private String orgCode;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @Excel(name = "业务类型")
    private String bussinessCode;
    /**
     * 集团报账单名称
     */
    @ApiModelProperty(value = "集团报账单名称")
    @Excel(name = "集团报账单名称")
    private String claimTypeName;
    /**
     * 业务大类代码
     */
    @ApiModelProperty(value = "业务大类代码")
    @Excel(name = "业务大类代码")
    private String classCode;
    /**
     * 业务大类名称
     */
    @ApiModelProperty(value = "业务大类名称")
    @Excel(name = "业务大类名称")
    private String className;
    /**
     * 业务小类代码
     */
    @ApiModelProperty(value = "业务小类代码")
    @Excel(name = "业务小类代码")
    private String classSmCode;
    /**
     * 业务小类名称
     */
    @ApiModelProperty(value = "业务小类名称")
    @Excel(name = "业务小类名称")
    private String classSmName;
    /**
     * 业务活动代码
     */
    @ApiModelProperty(value = "业务活动代码")
    @Excel(name = "业务活动代码")
    private String activityCode;
    /**
     * 业务活动名称
     */
    @ApiModelProperty(value = "业务活动名称")
    @Excel(name = "业务活动名称")
    private String activityName;
    /**
     * 启用标识
     */
    @ApiModelProperty(value = "启用标识")
    @Excel(name = "启用标识")
    private String enabledFlag;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String bussinessName;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String whetherEnableBudget;
}
