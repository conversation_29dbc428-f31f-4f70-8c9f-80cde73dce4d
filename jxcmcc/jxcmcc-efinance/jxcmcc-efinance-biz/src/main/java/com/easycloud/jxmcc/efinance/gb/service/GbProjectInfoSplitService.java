/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfo;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfoSplit;
import com.easycloud.jxmcc.efinance.gb.vo.GbProjectInfoSplitVo;
import com.easycloud.jxmcc.efinance.vo.FtAcctDprojBudgetEntertainVo;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;

/**
 * 国拨项目信息拆分
 *
 * <AUTHOR> code generator
 * @date 2022-10-20 11:24:12
 */
public interface GbProjectInfoSplitService extends IService<GbProjectInfoSplit> {


    /**
    * 导入excel
    * @return
    */
    R<Integer>  importExcel(MultipartFile file,JxcmccImportParams importParams) throws Exception ;

    /**
     * 新增拆分项目
     * @param gbProjectInfoSplit
     * @return
     */
    R saveGbProjectSplit(GbProjectInfoSplit gbProjectInfoSplit,String reType);

    /**
     * 修改拆分项目
     * @param gbProjectInfoSplit
     * @param reType
     * @return
     */
    R updateByGbProjectSplit(GbProjectInfoSplit gbProjectInfoSplit,String reType);

    /**
     *
     * @param id
     * @param type
     * @return
     */
    BigDecimal splitProjectOneVerify(String id , String type,String upId,String reType);

    /**
     * 校验地区编码是否可新增
     * @param companyCode  地区编码
     * @param id           项目id
     * @param type         添加add/修改update类型
     * @param upId         修改数据id
     * @return
     */
    Boolean findCodeVerify(String companyCode, String id, String type, String upId);

    /**
     * 校验报账金额
     * @param gbProjectInfoSplit 拆分项目对象
     * @param applyAmount  本次报账金额
     * @return
     */
    BigDecimal checkSplitProjectClaim(GbProjectInfoSplit gbProjectInfoSplit, BigDecimal applyAmount);

    /**
     * 分页查询拆分报账信息
     * @param page
     * @param Vo
     * @return
     */
    Page<GbProjectInfoSplitVo> selectPage(IPage<GbProjectInfoSplitVo> page, GbProjectInfoSplitVo Vo);

    /**
     * 分页查询拆分报账信息(未报账)
     * @param page
     * @param gbProjectInfoSplit
     * @return
     */
    IPage<GbProjectInfoSplitVo> selectNoClaimPage(IPage<GbProjectInfoSplitVo> page, GbProjectInfoSplit gbProjectInfoSplit);

    /**
     * 根据id删除拆分项目数据
     * @param id
     * @return
     */
    R  reById(String id);

    /**
     * 获取项目信息
     * @param id
     * @return
     */
    GbProjectInfo getPro(String id);
}
