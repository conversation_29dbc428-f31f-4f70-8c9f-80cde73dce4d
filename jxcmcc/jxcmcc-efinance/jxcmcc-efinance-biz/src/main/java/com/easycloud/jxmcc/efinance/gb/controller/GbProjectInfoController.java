/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.admin.api.feign.RemoteUserService;
import com.easycloud.jxcmcc.common.core.util.BeanHelperUtil;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfo;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectInfoService;
import com.easycloud.jxmcc.efinance.td.entity.FtAcctApprovalEmployeeLoanTd;
import com.easycloud.jxmcc.efinance.util.PermissionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.HashMap;
import java.util.Map;
import java.util.List;


/**
 * 国拨项目信息管理
 *
 * <AUTHOR> code efinance
 * @date 2022-10-18 10:01:22
 */
@RestController
@RequestMapping("/gbprojectinfo" )
@Api(value = "gbprojectinfo", tags = "国拨项目信息管理管理")
public class GbProjectInfoController {

    @Autowired
    private GbProjectInfoService gbProjectInfoService;
    @Autowired
    private RemoteUserService remoteUserService;
    /**
     * 分页查询
     * @param page 分页对象
     * @param gbProjectInfo 国拨项目信息管理
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询国拨项目信息管理" )
    @GetMapping("/page" )
    public R getGbProjectInfoPage(Page page, GbProjectInfo gbProjectInfo) {
        QueryWrapper queryWrapper = BeanHelperUtil.setQueryWrapper(gbProjectInfo);
        queryWrapper.eq("DELETE_FLAG","0");
        queryWrapper.orderByDesc("CREATED_TIME");
        JxcmccUser jxcmccUser=SecurityUtils.getUser();
        String  permissionCode = "";
        List<Map<String, Object>> mapList = remoteUserService.queryUserRoleCode(jxcmccUser.getUsername());
        for (Map<String, Object> map : mapList) {
            String roleCode = map.get("ROLE_CODE").toString().toLowerCase();
            //系统管理员
            if (roleCode.equals("FZ_GBXM_ADMIN")){
                permissionCode="1";
                break;
            }else{
                permissionCode="2";
            }
        }
        if(!PermissionUtil.isAdmin()&&"2".equals(permissionCode)){
            gbProjectInfo.setPersonLiableNo(jxcmccUser.getUsername());
        }
        return R.ok(gbProjectInfoService.page(page, queryWrapper));
    }


    /**
     * 通过id查询国拨项目信息管理
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询国拨项目信息管理" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(gbProjectInfoService.getById(id));
    }

    /**
     * 新增国拨项目信息管理
     * @param gbProjectInfo 国拨项目信息管理
     * @return R
     */
    @ApiOperation(value = "新增国拨项目信息管理", notes = "新增国拨项目信息管理")
    @SysLog("新增国拨项目信息管理" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('efinance_gbprojectinfo_add')" )
    public R save(@RequestBody GbProjectInfo gbProjectInfo) {
        // 项目编号重复校验
        int count = gbProjectInfoService.count(Wrappers.lambdaQuery(GbProjectInfo.class).
                eq(GbProjectInfo::getProjectNo, gbProjectInfo.getProjectNo()).
                eq(GbProjectInfo::getDeleteFlag, "0"));
        if(count > 0){
            return R.failed(false, "该项目编号【" + gbProjectInfo.getProjectNo() + "】已存在");
        }
        gbProjectInfo.setDeleteFlag("0");
        gbProjectInfo.setProcessStatus("0"); //流程状态
        gbProjectInfo.setPushStatus("0"); //推送状态
        return R.ok(gbProjectInfoService.save(gbProjectInfo));
    }

    /**
     * 修改国拨项目信息管理
     * @param gbProjectInfo 国拨项目信息管理
     * @return R
     */
    @ApiOperation(value = "修改国拨项目信息管理", notes = "修改国拨项目信息管理")
    @SysLog("修改国拨项目信息管理" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('efinance_gbprojectinfo_edit')" )
    public R updateById(@RequestBody GbProjectInfo gbProjectInfo) {
        if(null != gbProjectInfo.getProcessStatus() && (gbProjectInfo.getProcessStatus().equals("1") || gbProjectInfo.getProcessStatus().equals("2"))){
            return R.failed(false, "该项目正在审批或审批通过，无法进行修改");
        }
        return R.ok(gbProjectInfoService.updateById(gbProjectInfo));
    }

    /**
     * 通过id删除国拨项目信息管理
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除国拨项目信息管理", notes = "通过id删除国拨项目信息管理")
    @SysLog("通过id删除国拨项目信息管理" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('efinance_gbprojectinfo_del')" )
    public R removeById(@PathVariable String id) {
        GbProjectInfo gbProjectInfo = gbProjectInfoService.getById(id);
        if(null != gbProjectInfo.getProcessStatus() && (gbProjectInfo.getProcessStatus().equals("1") || gbProjectInfo.getProcessStatus().equals("2"))){
            return R.failed(false, "该项目正在审批或审批通过，无法进行删除");
        }
        gbProjectInfo.setDeleteFlag("1");
        return R.ok(gbProjectInfoService.updateById(gbProjectInfo));
    }


   /**
     * 导入国拨项目信息管理
     * @return R
     */
    @ApiOperation(value = "导入国拨项目信息管理", notes = "导入国拨项目信息管理")
    @SysLog("导入国拨项目信息管理" )
    @PreAuthorize("@pms.hasPermission('efinance_gbprojectinfo_import')" )
    @PostMapping(value = "/importExcel")
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return gbProjectInfoService.importExcel(file,importParams);
    }

    /**
     * 导出国拨项目信息管理
     * @return R
     */
    @ApiOperation(value = "导出国拨项目信息管理", notes = "导出国拨项目信息管理")
    @SysLog("导出国拨项目信息管理")
    @PreAuthorize("@pms.hasPermission('efinance_gbprojectinfo_export')")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, GbProjectInfo gbProjectInfo) {
        QueryWrapper queryWrapper = BeanHelperUtil.setQueryWrapper(gbProjectInfo);
        queryWrapper.eq("DELETE_FLAG","0");
        queryWrapper.orderByDesc("CREATED_TIME");
        List<GbProjectInfo>  gbProjectInfoList = gbProjectInfoService.list(queryWrapper);
        //导出Excel
        try {
            ExportParams exportParams = new ExportParams();
            exportParams.setTitle("国拨项目信息管理列表");
            exportParams.setSheetName("国拨项目信息管理");
            exportParams.setSecondTitle("注意点：（1）、项目名称由项目编号查询自动补充（2）、项目类型填无线/传输（3）、部门名称填全称");
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams,GbProjectInfo. class,gbProjectInfoList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出国拨项目信息管理导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版国拨项目信息管理文件", notes = "导出导入模版国拨项目信息管理文件")
    @SysLog("导出国拨项目信息管理导入模版文件")
    @PreAuthorize("@pms.hasPermission('efinance_gbprojectinfo_export')")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/gbProjectInfoTemplate.xlsx", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取所有地市信息
     * @return R
     */
    @ApiOperation(value = "获取所有地市信息", notes = "获取所有地市信息")
    @SysLog("获取所有地市信息" )
    @GetMapping("/getCompanyList" )
    public R getCompanyList(){
       return gbProjectInfoService.getCompanyList();
    }

    /**
     * 国拨项目-发起流程
     * @param idList  国投信息id集合
     * @return R
     */
    @ApiOperation(value = "国拨项目-发起流程", notes = "国拨项目-发起流程")
    @SysLog("国拨项目-发起流程" )
    @PostMapping("/startFlow")
    @PreAuthorize("@pms.hasPermission('efinance_gbprojectinfo_startFlow')" )
    public R startFlow(@RequestBody List<String> idList) {
        return gbProjectInfoService.startFlow(idList);
    }

    /**
     * 国拨项目-发起拆分审批流程
     * @param idList  国投信息id集合
     * @return R
     */
    @ApiOperation(value = "国拨项目-发起拆分审批流程", notes = "国拨项目-发起拆分审批流程")
    @SysLog("国拨项目-发起拆分审批流程" )
    @PostMapping("/startSplieFlow")
    @PreAuthorize("@pms.hasPermission('efinance_gbprojectinfo_startSplieFlow')" )
    public R startSplieFlow(@RequestBody List<String> idList) {
        return gbProjectInfoService.startSplitFlow(idList);
    }

    /**
     * 通过项目编号查询
     * @param projectNo projectNo
     * @return R
     */
    @ApiOperation(value = "通过项目编号查询", notes = "通过项目编号查询")
    @SysLog("通过项目编号查询" )
    @GetMapping("/getCountByProjectNo/{projectNo}" )
    public R getCountByProjectNo(@PathVariable("projectNo" ) String projectNo) {
        return R.ok(gbProjectInfoService.count(Wrappers.lambdaQuery(GbProjectInfo.class).eq(GbProjectInfo::getProjectNo,projectNo)));
    }

    /**
     * 根据项目编号查询项目信息
     * @param projectNo projectNo
     * @return R
     */
    @ApiOperation(value = "根据项目编号查询项目信息", notes = "根据项目编号查询项目信息")
    @SysLog("根据项目编号查询项目信息" )
    @GetMapping("/getProjectInfoByProjectNo/{projectNo}" )
    public R getProjectInfoByProjectNo(@PathVariable("projectNo") String projectNo) {
        return gbProjectInfoService.getProjectInfoByProjectNo(projectNo);
    }
}
