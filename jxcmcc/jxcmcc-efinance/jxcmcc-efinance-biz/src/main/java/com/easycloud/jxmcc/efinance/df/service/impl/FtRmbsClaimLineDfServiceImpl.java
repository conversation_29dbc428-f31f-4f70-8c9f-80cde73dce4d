/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.df.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimLineDf;
import com.easycloud.jxmcc.efinance.df.mapper.FtRmbsClaimLineDfMapper;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimLineDfService;
import com.easycloud.jxmcc.efinance.entity.FtCmccClaim;
import com.easycloud.jxmcc.efinance.vo.ClaimLineVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.easycloud.jxcmcc.common.core.util.R;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import java.util.List;
/**
 * 党费系统报账行明细
 *
 * <AUTHOR>
 * @date 2022-03-28 15:00:54
 */
@Service
@AllArgsConstructor
public class FtRmbsClaimLineDfServiceImpl extends ServiceImpl<FtRmbsClaimLineDfMapper, FtRmbsClaimLineDf> implements FtRmbsClaimLineDfService {

    private final FtRmbsClaimDfService rmbsClaimDfService;

    /**
    * 导入excel
    * @return
    */
    @Override
   public R<Integer>  importExcel(MultipartFile file,JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！");
        }
        try {
            ImportParams params = new ImportParams();
            //解析导入EXCEL数据
            List<FtRmbsClaimLineDf> ftRmbsClaimLineDfList = ExcelImportUtil.importExcel(file.getInputStream(), FtRmbsClaimLineDf.class,
            params);
            this.saveBatch(ftRmbsClaimLineDfList);
              return R.ok(ftRmbsClaimLineDfList.size());
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return R.failed("导入失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveOrUpdateLine(FtRmbsClaimLineDf ftRmbsClaimLineDf) {
        boolean update = StrUtil.isNotBlank(ftRmbsClaimLineDf.getClaimLineId());
        if(update){
            updateById(ftRmbsClaimLineDf);
        }else {
            save(ftRmbsClaimLineDf);
        }
        rmbsClaimDfService.countAmount(ftRmbsClaimLineDf.getClaimId());
        return 1;
    }

    @Override
    public int deleteLine(ClaimLineVo claimLineVo) {
        removeByIds(claimLineVo.getIdList());
        rmbsClaimDfService.countAmount(claimLineVo.getClaimId());
        return claimLineVo.getIdList().size();
    }

}
