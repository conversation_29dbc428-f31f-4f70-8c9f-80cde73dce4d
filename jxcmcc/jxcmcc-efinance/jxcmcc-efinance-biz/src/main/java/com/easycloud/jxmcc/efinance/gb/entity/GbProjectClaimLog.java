/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 国拨项目报帐日志表
 *
 * <AUTHOR> code generator
 * @date 2022-10-24 09:21:00
 */
@Data
@TableName("GB_PROJECT_CLAIM_LOG")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "国拨项目报帐日志表")
public class GbProjectClaimLog extends Model<GbProjectClaimLog> {
private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    @Excel(name = "ID")
    private String id;
    /**
     * 报账类型
     */
    @ApiModelProperty(value = "报账类型")
    @Excel(name = "报账类型")
    private String claimType;
    /**
     * 辅助报账单号
     */
    @ApiModelProperty(value = "辅助报账单号")
    @Excel(name = "辅助报账单号")
    private String claimNo;

    /**
     * 集团报账单号
     */
    @ApiModelProperty(value = "集团报账单号")
    @Excel(name = "集团报账单号")
    private String groupClaimNo;
    /**
     * 报账金额
     */
    @ApiModelProperty(value = "报账金额")
    @Excel(name = "报账金额")
    private BigDecimal claimAmount;
    /**
     * 报账金额
     */
    @ApiModelProperty(value = "付款金额")
    @Excel(name = "付款金额")
    private BigDecimal payAmount;
    /**
     * 数据id
     */
    @ApiModelProperty(value = "数据id")
    @Excel(name = "数据id")
    private String dataId;
    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @Excel(name = "项目编号")
    private String projectNo;
    /**
     * 地市编码
     */
    @ApiModelProperty(value = "地市编码")
    @Excel(name = "地市编码")
    private String companyCode;
    /**
     * 是否自动生成报账单 Y-是 N-否
     */
    @ApiModelProperty(value = "是否虚拟账户 Y-是 N-否")
    @Excel(name = "是否虚拟账户")
    private String isClaim;
    /**
     * 父级报账id
     */
    @ApiModelProperty(value = "子级报账id")
    @Excel(name = "子级报账id")
    private String parentId;
    /**
     * 租户号
     */
    @ApiModelProperty(value = "租户号")
    @Excel(name = "租户号")
    private Integer tenantId;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @Excel(name = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @Excel(name = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @Excel(name = "更新人")
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @Excel(name = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedTime;
    }
