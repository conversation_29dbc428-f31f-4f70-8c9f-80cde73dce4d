package com.easycloud.jxmcc.efinance.gb.listener;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.easycloud.jxcmcc.admin.api.dto.UserInfo;
import com.easycloud.jxcmcc.admin.api.feign.RemoteUserService;
import com.easycloud.jxcmcc.common.core.constant.SecurityConstants;
import com.easycloud.jxcmcc.common.core.exception.BusinessException;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.efinance.claim.listener.BaseListener;
import com.easycloud.jxmcc.efinance.claim.listener.EmisTodoListener;
import com.easycloud.jxmcc.efinance.dict.entity.FtRmbsIctParentProject;
import com.easycloud.jxmcc.efinance.entity.FtRmbsCityAccountConfig;
import com.easycloud.jxmcc.efinance.gb.entity.GbApproverCfg;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfo;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfoSplit;
import com.easycloud.jxmcc.efinance.gb.service.GbApproverCfgService;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectInfoService;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectInfoSplitService;
import com.easycloud.jxmcc.efinance.service.FtRmbsCityAccountConfigService;
import com.easycloud.jxmcc.efinance.util.TCmccClaimUtil;
import com.easycloud.jxmcc.eflow.listener.IProcessListener;
import com.easycloud.jxmcc.eflow.vo.BaseEflowApiVo;
import com.easycloud.jxmcc.eflow.vo.ProcessEflowApiVo;
import com.easycloud.jxmcc.eflow.vo.TaskEflowApiVo;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * 　　* <AUTHOR>
 * 　　* @date 2022/10/18 11:12
 * 国拨信息审批流程
 *
 **/
@Component
@AllArgsConstructor
public class GbProjectInfoListener extends EmisTodoListener implements IProcessListener {

    @Autowired
    private GbProjectInfoService gbProjectInfoService;
    @Autowired
    private GbApproverCfgService gbApproverCfgService;
    @Autowired
    private GbProjectInfoSplitService gbProjectInfoSplitService;
    @Autowired
    private FtRmbsCityAccountConfigService tRmbsCityAccountConfigService;
    @Autowired
    private RemoteUserService remoteUserService;

    @Override
    public String businessType() {
        return "gbParentProject";
    }

    @Override
    public Map<String, Object> getVariables(BaseEflowApiVo data) {
        List<FtRmbsCityAccountConfig> ftRmbsCityAccountConfigList = tRmbsCityAccountConfigService.list(Wrappers.lambdaQuery(FtRmbsCityAccountConfig.class)
                .eq(FtRmbsCityAccountConfig::getItemId,"T0133GB"));
        GbProjectInfo gbProjectInfo = gbProjectInfoService.getById(data.getBusinessId());
        R<UserInfo> r = remoteUserService.info(gbProjectInfo.getPersonLiableNo(), SecurityConstants.FROM_IN);
        String companyCode = r.getData().getSysUser().getOrgCodeShort();
        Optional<FtRmbsCityAccountConfig> optional = ftRmbsCityAccountConfigList.stream().filter(item -> item.getCompId().equals(companyCode)).findFirst();
        JxcmccUser jxcmccUser = SecurityUtils.getUser();
        Map<String, Object> map = new HashMap<>();
        map.put("initiator",jxcmccUser.getUser().getUsername());
        map.put("bookkeeper",optional.get().getUserId());
        map.put("manager",gbProjectInfo.getPersonLiableNo());
        map.put("leader",gbProjectInfo.getPersonLiableNo());

        return map;
    }

    @Override
    public String getTitle(TaskEflowApiVo data) {
        GbProjectInfo gbProjectInfo = gbProjectInfoService.getById(data.getBusinessId());
        return StrUtil.format("关于[国拨项目:{}]的申请"
                ,gbProjectInfo.getProjectName()
        );
    }

    @Override
    public void start(ProcessEflowApiVo data) {
        GbProjectInfo gbProjectInfo = gbProjectInfoService.getById(data.getBusinessId());
        gbProjectInfo.setProcessId(data.getBusinessId());
        gbProjectInfo.setProcessStatus("1");
        //修改数据流程状态
        gbProjectInfoService.updateById(gbProjectInfo);
    }

    @Override
    public void end(ProcessEflowApiVo data) {
        //修改数据流程状态
        gbProjectInfoService.update(Wrappers.lambdaUpdate(GbProjectInfo.class).
                set(GbProjectInfo::getProcessStatus,"2").
                eq(GbProjectInfo::getProcessId,data.getBusinessId()));
        gbProjectInfoSplitService.update(Wrappers.lambdaUpdate(GbProjectInfoSplit.class).
                set(GbProjectInfoSplit::getProcessStatus,"2").
                eq(GbProjectInfoSplit::getProcessId,data.getBusinessId()));
    }

/*    public void completed(TaskEflowApiVo data) {
        if(data.getVariables().get("leader") != null && data.getVariables().keySet().size() > 8){  //验证总拆分金额等于批复金额
            GbProjectInfo gbProjectInfo = gbProjectInfoService.getById(data.getBusinessId());
            List<GbProjectInfoSplit> gbProjectInfoSplitList = gbProjectInfoSplitService.list(Wrappers.lambdaQuery(GbProjectInfoSplit.class)
                    .eq(GbProjectInfoSplit::getProcessId,data.getBusinessId()));
            BigDecimal sumSplit = BigDecimal.ZERO;
            for (GbProjectInfoSplit item:gbProjectInfoSplitList){
                sumSplit = sumSplit.add(item.getSplitAmount());
            }
            if(gbProjectInfo.getApprovedAmount().compareTo(sumSplit) != 0){
                throw  new BusinessException("总拆分金额不等于批复金额，提交失败！");
            }
        }
    }*/

}
