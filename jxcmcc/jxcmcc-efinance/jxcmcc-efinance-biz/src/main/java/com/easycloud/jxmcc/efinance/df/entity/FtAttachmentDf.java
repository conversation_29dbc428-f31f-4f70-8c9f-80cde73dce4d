/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 老党费附件表
 *
 * <AUTHOR> code generator
 * @date 2023-02-16 14:40:12
 */
@Data
@TableName("FT_ATTACHMENT_DF")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "老党费附件表")
public class FtAttachmentDf extends Model<FtAttachmentDf> {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private Long attachId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private Long claimId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String attachName;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String attachPath;
}
