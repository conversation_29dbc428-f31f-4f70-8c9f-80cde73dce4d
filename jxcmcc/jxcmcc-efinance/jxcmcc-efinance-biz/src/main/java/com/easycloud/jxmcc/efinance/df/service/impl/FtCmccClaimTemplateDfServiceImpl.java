/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.df.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxmcc.efinance.df.entity.FtCmccClaimTemplateDf;
import com.easycloud.jxmcc.efinance.df.mapper.FtCmccClaimTemplateDfMapper;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxmcc.efinance.df.service.FtCmccClaimTemplateDfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.easycloud.jxcmcc.common.core.util.R;
import org.springframework.web.multipart.MultipartFile;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;

import java.util.List;

/**
 * 党费业务大小类对应表
 *
 * <AUTHOR>
 * @date 2022-08-12 12:04:12
 */
@Service
@Slf4j
public class FtCmccClaimTemplateDfServiceImpl extends ServiceImpl<FtCmccClaimTemplateDfMapper, FtCmccClaimTemplateDf> implements FtCmccClaimTemplateDfService {


    /**
    * 导入excel
    * @return
    */
    @Override
    public R<Integer> importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！" );
        }
        ImportParams params = new ImportParams();
        //解析导入EXCEL数据
        List<FtCmccClaimTemplateDf> ftCmccClaimTemplateDfList = ExcelImportUtil.importExcel(file.getInputStream(), FtCmccClaimTemplateDf. class,
        params);
        this.saveBatch(ftCmccClaimTemplateDfList);
        return R.ok(ftCmccClaimTemplateDfList.size());
    }

    @Override
    public List<FtCmccClaimTemplateDf> getAllActivity() {
        return baseMapper.getAllActivity();
    }
}
