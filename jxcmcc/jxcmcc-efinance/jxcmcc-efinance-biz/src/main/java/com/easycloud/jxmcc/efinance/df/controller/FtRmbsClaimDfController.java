/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.util.WebUtils;
import com.easycloud.jxcmcc.common.data.builder.QueryWrapperBuilder;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.efinance.df.check.CheckDFService;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.efinance.df.vo.PartyOrgNameVo;
import com.easycloud.jxmcc.efinance.df.vo.StatisticsDfVo;
import com.easycloud.jxmcc.efinance.vo.CheckVo;
import com.easycloud.jxmcc.efinance.vo.UserRightsVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 党费系统报账头信息
 *
 * <AUTHOR>
 * @date 2022-03-28 15:00:54
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ftrmbsclaimdf" )
@Api(value = "ftrmbsclaimdf", tags = "党费系统报账头信息管理")
public class FtRmbsClaimDfController {

    private final FtRmbsClaimDfService ftRmbsClaimDfService;

    private final CheckDFService checkService;

    /**
     * 分页查询
     * @param page 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/myPage" )
    public R myPage(Page page) {
        QueryWrapper<FtRmbsClaimDf> build = QueryWrapperBuilder.<FtRmbsClaimDf>create(FtRmbsClaimDf.class).build(WebUtils.getParameterMap());
        build.lambda().eq(FtRmbsClaimDf::getApplyUserId, SecurityUtils.getUser().getUsername());
        return R.ok(ftRmbsClaimDfService.page(page, build));
    }

    /**
     * 报账单检测(党费系统)
     * @return R
     */
    @ApiOperation(value = "报账单检测(党费系统)", notes = "报账单检测(党费系统)")
    @SysLog(value = "报账单检测(党费系统)",demandId="xq_common")
    @RequestMapping(value = "/checkRule",method = RequestMethod.POST)
    public R checkRule(@RequestBody CheckVo checkVo) {
        checkService.checkDf(checkVo);
        return  R.ok();
    }

    /**
     * 查询党组织列表
     * @param page 分页对象
     * @param partyOrgNameVo
     * @return
     */
    @ApiOperation(value = "查询党组织列表", notes = "查询党组织列表")
    @SysLog("查询党组织列表" )
    @GetMapping("/getPartyOrgNameList" )
    public R getPartyOrgNameList(Page page, PartyOrgNameVo partyOrgNameVo) {
        return R.ok(ftRmbsClaimDfService.getPartyOrgNameList(page, partyOrgNameVo));
    }

    /**
     * 查询党组织列表
     * @param page 分页对象
     * @param partyOrgNameVo
     * @return
     */
    @ApiOperation(value = "查询党组织所有年份列表", notes = "查询党组织所有年份列表")
    @SysLog("查询党组织所有年份列表" )
    @GetMapping("/getPartyOrgNameListAll" )
    public R getPartyOrgNameListAll(Page page, PartyOrgNameVo partyOrgNameVo) {
        return R.ok(ftRmbsClaimDfService.getPartyOrgNameListAll(page, partyOrgNameVo));
    }


    /**
     * 查询审批单列表
     * @param page 分页对象
     * @param ftRmbsClaimDf
     * @return
     */
    @ApiOperation(value = "查询审批单列表", notes = "查询审批单列表")
    @SysLog("查询审批单列表" )
    @GetMapping("/getProjectMisNo" )
    public R getProjectMisNo(Page page, FtRmbsClaimDf ftRmbsClaimDf) {
        return R.ok(ftRmbsClaimDfService.getProjectMisNo(page, ftRmbsClaimDf));
    }

    /**
     * 报账单管理/已归档-分页查询
     * @param page
     * :{formType:[manage,end]} 查询数据来源，manage为报账单管理页面，end为已归档页面
     * @return
     */
    @ApiOperation(value = "报账单管理/已归档-分页查询", notes = "报账单管理/已归档-分页查询")
    @GetMapping("/custPage" )
    public R selectFtCmccClaimListByPage(Page page) {
        Map<String, String> paramMap = WebUtils.getParameterMap();
        JxcmccUser jxcmccUser = SecurityUtils.getUser();
        if(null == paramMap.get("formType") || paramMap.get("formType") == "" || paramMap.get("formType").equals("manage")){
            //获取用户权限
            UserRightsVo userRightsVo = ftRmbsClaimDfService.getUserRights(jxcmccUser);
            if("2".equals(userRightsVo.getUserRights())){
                paramMap.put("applyComCode", userRightsVo.getApplyComCode());
            } else if("3".equals(userRightsVo.getUserRights())){
                paramMap.put("applyDeptCode", userRightsVo.getApplyDeptCode());
            }
        }else if(paramMap.get("formType").equals("end")){
            paramMap.put("applyUserId", jxcmccUser.getUsername());
        }
        return R.ok(ftRmbsClaimDfService.selectFtRmbsDfClaimListByPage(page, paramMap));
    }


    /**
     * 分页查询
     * @param page 分页对象
     * @return
     */
    @ApiOperation(value = "我的已办分页查询", notes = "我的已办分页查询")
    @GetMapping("/getPage")
    public R getPage(Page page) {
        Map<String, String> paramMap = WebUtils.getParameterMap();
        JxcmccUser jxcmccUser = SecurityUtils.getUser();
        paramMap.put("applyUserId", jxcmccUser.getUsername());
        return R.ok(ftRmbsClaimDfService.getPage(page, paramMap));
    }


    /**
     * 分页查询党费统计
     * @param page 分页对象
     * @return
     */
    @ApiOperation(value = "党费统计分页查询", notes = "党费统计分页查询")
    @GetMapping("/statisticsPage")
    public R statisticsPage(Page page) {
        Map<String, String> paramMap = WebUtils.getParameterMap();
        return R.ok(ftRmbsClaimDfService.statisticsPage(page, paramMap));
    }


    /**
     * 查询原报账单号列表
     * @param page 分页对象
     * @param ftRmbsClaimDf
     * @return
     */
    @ApiOperation(value = "查询原报账单号列表", notes = "查询原报账单号列表")
    @SysLog("查询原报账单号列表" )
    @GetMapping("/getPayClaimNoList" )
    public R getPayClaimNoList(Page page, FtRmbsClaimDf ftRmbsClaimDf) {
        return R.ok(ftRmbsClaimDfService.getPayClaimNoList(page, ftRmbsClaimDf));
    }

    /**
     * 查询党组织已报账列表
     * @param page 分页对象
     * @param partyOrgNameVo
     * @return
     */
    @ApiOperation(value = "查询党组织已报账列表", notes = "查询党组织已报账列表")
    @SysLog("查询党组织已报账列表" )
    @GetMapping("/getPartyClaim" )
    public R getPartyClaim(Page page, PartyOrgNameVo partyOrgNameVo) {
        return R.ok(ftRmbsClaimDfService.getPartyClaim(page, partyOrgNameVo));
    }

    /**
     * 查询党组织已报账所有年份列表
     * @param page 分页对象
     * @param partyOrgNameVo
     * @return
     */
    @ApiOperation(value = "查询党组织已报账列表", notes = "查询党组织已报账列表")
    @SysLog("查询党组织已报账列表" )
    @GetMapping("/getPartyClaimAll" )
    public R getPartyClaimAll(Page page, PartyOrgNameVo partyOrgNameVo) {
        return R.ok(ftRmbsClaimDfService.getPartyClaimAll(page, partyOrgNameVo));
    }

    /**
     * 查询党组织已审批未报账列表
     * @param page 分页对象
     * @param partyOrgNameVo
     * @return
     */
    @ApiOperation(value = "查询党组织已审批未报账列表", notes = "查询党组织已审批未报账列表")
    @SysLog("查询党组织已审批未报账列表" )
    @GetMapping("/getPartyClaimT002DF" )
    public R getPartyClaimT002DF(Page page, PartyOrgNameVo partyOrgNameVo) {
        return R.ok(ftRmbsClaimDfService.getPartyClaimT002DF(page, partyOrgNameVo));
    }

    /**
     * 查询党组织已审批未报账所有年份列表
     * @param page 分页对象
     * @param partyOrgNameVo
     * @return
     */
    @ApiOperation(value = "查询党组织已审批未报账所有年份列表", notes = "查询党组织已审批未报账所有年份列表")
    @SysLog("查询党组织已审批未报账所有年份列表" )
    @GetMapping("/getPartyClaimT002DFAll" )
    public R getPartyClaimT002DFAll(Page page, PartyOrgNameVo partyOrgNameVo) {
        return R.ok(ftRmbsClaimDfService.getPartyClaimT002DFAll(page, partyOrgNameVo));
    }


    /**
     * 通过id查询党费系统报账头信息
     * @param claimId id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询党费系统报账头信息" )
    @GetMapping("/{claimId}" )
    public R getById(@PathVariable("claimId" ) String claimId) {
        return R.ok(ftRmbsClaimDfService.getById(claimId));
    }

    /**
     * 通过报账单号查询党费系统报账头信息
     * @param claimNo
     * @return R
     */
    @ApiOperation(value = "通过报账单号查询党费系统报账头信息", notes = "通过报账单号查询党费系统报账头信息")
    @SysLog("通过报账单号查询党费系统报账头信息" )
    @GetMapping("/getByClaimNo/{claimNo}" )
    public R getByClaimNo(@PathVariable("claimNo" ) String claimNo) {
        return R.ok(ftRmbsClaimDfService.getOne(new QueryWrapper<FtRmbsClaimDf>().lambda().eq(FtRmbsClaimDf::getClaimNo,claimNo)));
    }

    /**
     * 新增党费系统报账头信息
     * @param ftRmbsClaimDf 头表
     * @return R
     */
    @ApiOperation(value = "新增党费系统报账头信息", notes = "新增党费系统报账头信息")
    @SysLog(value = "新增党费系统报账头信息")
    @PostMapping("/saveOrUpdate")
    public R saveOrUpdate(@RequestBody FtRmbsClaimDf ftRmbsClaimDf) {
        return R.ok(ftRmbsClaimDfService.saveOrUpdateClaim(ftRmbsClaimDf));
    }

    /**
     * 初始化党费报账单基本信息
     * @return R
     */
    @ApiOperation(value = "初始化党费报账单基本信息", notes = "初始化党费报账单基本信息")
    @SysLog(value = "初始化党费报账单基本信息")
    @GetMapping("/initialize/claim")
    public R initializeClaim(FtRmbsClaimDf ftRmbsClaimDf) {
        return  R.ok(ftRmbsClaimDfService.initializeClaim(ftRmbsClaimDf));
    }

    /**
     * 初始化党费报账单基本信息(无行信息的报账单)
     * @return R
     */
    @ApiOperation(value = "初始化党费报账单基本信息(无行信息的报账单)", notes = "初始化党费报账单基本信息(无行信息的报账单)")
    @SysLog(value = "初始化党费报账单基本信息(无行信息的报账单)")
    @GetMapping("/initialize/claimNoLine")
    public R initializeClaimNoLine(FtRmbsClaimDf ftRmbsClaimDf) {
        return  R.ok(ftRmbsClaimDfService.initializeClaimNoLine(ftRmbsClaimDf));
    }

    /**
     * 新增党费系统报账头信息
     * @param ftRmbsClaimDf 党费系统报账头信息
     * @return R
     */
    @ApiOperation(value = "新增党费系统报账头信息", notes = "新增党费系统报账头信息")
    @SysLog("新增党费系统报账头信息" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('generator_ftrmbsclaim_add')" )
    public R save(@RequestBody FtRmbsClaimDf ftRmbsClaimDf) {
        return R.ok(ftRmbsClaimDfService.save(ftRmbsClaimDf));
    }

    /**
     * 修改党费系统报账头信息
     * @param ftRmbsClaimDf 党费系统报账头信息
     * @return R
     */
    @ApiOperation(value = "修改党费系统报账头信息", notes = "修改党费系统报账头信息")
    @SysLog("修改党费系统报账头信息" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('generator_ftrmbsclaim_edit')" )
    public R updateById(@RequestBody FtRmbsClaimDf ftRmbsClaimDf) {
        return R.ok(ftRmbsClaimDfService.updateById(ftRmbsClaimDf));
    }

    /**
     * 通过id删除党费系统报账头信息
     * @param claimId id
     * @return R
     */
    @ApiOperation(value = "通过id删除党费系统报账头信息", notes = "通过id删除党费系统报账头信息")
    @SysLog("通过id删除党费系统报账头信息" )
    @DeleteMapping("/{claimId}" )
    public R removeById(@PathVariable String claimId) {
        ftRmbsClaimDfService.deleteClaim(claimId);
        return R.ok();
    }


   /**
     * 导入党费系统报账头信息
     * @return R
     */
    @ApiOperation(value = "导入党费系统报账头信息", notes = "导入党费系统报账头信息")
    @SysLog("导入党费系统报账头信息" )
    @PreAuthorize("@pms.hasPermission('generator_ftrmbsclaim_import')" )
    @FileImportLogAnnotation(busTable = "党费系统报账头信息",busSystem = "generator")
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return ftRmbsClaimDfService.importExcel(file,importParams);
    }


    @ApiOperation(value = "导出党费报账单统计表", notes = "导出党费报账单统计表")
    @SysLog("导出党费报账单统计表" )
    @GetMapping(value = "/exportStatistics")
    public void exportStatistics(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> paramMap = WebUtils.getParameterMap();
        Page page = new Page();
        page.setSize(-1);
        page.setCurrent(1);
        List<StatisticsDfVo> statisticsDfList = ftRmbsClaimDfService.statisticsPage(page, paramMap).getRecords();
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("党费报账单统计表", "党费报账单统计表"),
                    StatisticsDfVo.class,statisticsDfList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "导出党费预算使用情况表", notes = "导出党费预算使用情况表")
    @SysLog("导出党费预算使用情况表" )
    @GetMapping(value = "/exportBudgetDf")
    public void exportBudgetDf(HttpServletRequest request, HttpServletResponse response,PartyOrgNameVo partyOrgNameVo) {
        Page page = new Page();
        page.setSize(-1);
        page.setCurrent(1);
        List<PartyOrgNameVo> list = ftRmbsClaimDfService.getPartyOrgNameListAll(page, partyOrgNameVo).getRecords();
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("党费预算使用情况表", "党费预算使用情况表"),
                    PartyOrgNameVo.class,list);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


       /**
     * 导出党费系统报账头信息
     * @return R
     */
    @ApiOperation(value = "导出党费系统报账头信息", notes = "导出党费系统报账头信息")
    @SysLog("导出党费系统报账头信息" )
    @PreAuthorize("@pms.hasPermission('generator_ftrmbsclaim_export')" )
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, FtRmbsClaimDf ftRmbsClaimDf) {
        List<FtRmbsClaimDf> ftRmbsClaimDfList = ftRmbsClaimDfService.list(Wrappers.lambdaQuery(ftRmbsClaimDf));
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("党费系统报账头信息列表", "党费系统报账头信息"),
                  FtRmbsClaimDf.class, ftRmbsClaimDfList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出党费系统报账头信息导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版党费系统报账头信息文件", notes = "导出导入模版党费系统报账头信息文件")
    @SysLog("导出党费系统报账头信息导入模版文件" )
    @PreAuthorize("@pms.hasPermission('generator_ftrmbsclaim_export')" )
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String,Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/FtRmbsClaim.xls",true);
            Workbook workbook= ExcelExportUtil.exportExcel(params,map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
