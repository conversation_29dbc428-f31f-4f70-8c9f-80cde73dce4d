/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 党费系统业务小类表
 *
 * <AUTHOR>
 * @date 2022-04-02 15:54:00
 */
@Data
@TableName("FT_CO_ITEMLEVEL3_DF")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "党费系统业务小类表")
public class FtCoItemlevel3Df extends Model<FtCoItemlevel3Df> {
private static final long serialVersionUID = 1L;

    /**
     * ;
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String id;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String itemId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String itemName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String fatherItem;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer state;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer invoiceType;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apDrSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apDrSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apProjectSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apProjectSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer isProjectAct;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apBandSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apBandSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apIcSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apIcSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String glDrSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String glDrSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String glCrSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String glCrSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String arCrSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String arCrSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String description;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apBudgetSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apBudgetSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal tax;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String templateid;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String hkLevel;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String hasFywdsx;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String isVoucher;
    }
