/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 国拨项目信息管理
 *
 * <AUTHOR> code generator
 * @date 2022-10-18 10:01:22
 */
@Data
@TableName("GB_PROJECT_INFO")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "国拨项目信息管理")
public class GbProjectInfo extends Model<GbProjectInfo> {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "唯一标识")
    private String id;
    /**
     * 辅助报账单号
     */
    @ApiModelProperty(value = "辅助报账单号")
    private String claimNo;
    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @Excel(name = "项目编号",width = 30)
    private String projectNo;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @Excel(name = "项目名称",width = 30)
    private String projectName;
    /**
     * 批复金额
     */
    @ApiModelProperty(value = "批复金额")
    @Excel(name = "批复金额",width = 15)
    private BigDecimal approvedAmount;
    /**
     * 地市编码
     */
    @ApiModelProperty(value = "地市编码")
//    @Excel(name = "地市编码",width = 20)
    private String companyCode;
    /**
     * 地市名称
     */
    @ApiModelProperty(value = "地市名称")
//    @Excel(name = "地市名称",width = 15)
    private String companyName;
    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门编码")
//    @Excel(name = "部门编码",width = 20)
    private String responsibleDeptCode;
    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @Excel(name = "部门名称",width = 20)
    private String responsibleDeptName;
    /**
     * 责任人账号
     */
    @ApiModelProperty(value = "省项目经理账号")
    @Excel(name = "省项目经理账号",width = 20)
    private String personLiableNo;
    /**
     * 责任人
     */
    @ApiModelProperty(value = "省项目经理")
    @Excel(name = "省项目经理",width = 15)
    private String personLiable;
    /**
     * 项目类型;无线或传输
     */
    @ApiModelProperty(value = "项目类型")
    @Excel(name = "项目类型",width = 15)
    private String projectType;
    /**
     * 流程ID
     */
    @ApiModelProperty(value = "流程ID")
//    @Excel(name = "流程ID")
    private String processId;
    /**
     * 流程审核状态;(0=未发起,1=审批中,2=审批通过,3=审批未通过)
     */
    @ApiModelProperty(value = "流程审核状态;(0=未发起,1=审批中,2=审批通过,3=审批未通过)")
//    @Excel(name = "流程审核状态;(0=未发起,1=审批中,2=审批通过,3=审批未通过)")
    private String processStatus;
    /**
     * 报账金额
     */
    @ApiModelProperty(value = "报账金额")
//    @Excel(name = "报账金额")
    private BigDecimal claimAmount;
    /**
     * 推送状态
     */
    @ApiModelProperty(value = "推送状态;(0=未推送，1=已推送，2=推送失败)")
    private String pushStatus;
    /**
     * 预留字段1
     */
    @ApiModelProperty(value = "预留字段1-拆分流程ID")
    private String reserveField1;
    /**
     * 预留字段2
     */
    @ApiModelProperty(value = "预留字段2-拆分流程状态")
    private String reserveField2;
    /**
     * 预留字段3
     */
    @ApiModelProperty(value = "预留字段3")
    private String reserveField3;
    /**
     * 租户号
     */
    @ApiModelProperty(value = "租户号")
    private Integer tenantId;
    /**
     * 删除标识;删除标识(0=否,1=是)
     */
    @ApiModelProperty(value = "删除标识;删除标识(0=否,1=是)")
    private String deleteFlag;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedTime;
}
