package com.easycloud.jxmcc.efinance.df.check;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.easycloud.jxcmcc.common.core.exception.BusinessException;
import com.easycloud.jxcmcc.common.core.util.SpringContextUtils;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimLineDf;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsConfidentClaimDf;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsPaylistDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimLineDfService;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsConfidentClaimDfService;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsPaylistDfService;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: zp
 * @create: 2022-04-07 15:08
 **/
@Slf4j
public abstract class BaseCheckDF implements IDFCheck {

    protected FtRmbsClaimDf rmbsClaimDf;
    protected FtRmbsClaimDf rmbsClaimDfByNo;
    protected List<FtRmbsClaimDf> rmbsClaimDfByProjectMisNo;
    protected List<FtRmbsPaylistDf> rmbsPaylistDf;
    protected List<FtRmbsConfidentClaimDf> rmbsConfidentClaimDf;
    protected List<FtRmbsClaimLineDf> rmbsClaimLineDfList;
    protected JxcmccUser user;
    protected Map<String, String> data;
    private static Map<Class, Object> cache = new HashMap<>();

    @Override
    public IDFCheck createDf(FtRmbsClaimDf rmbsClaimDf, Map<String, String> data) {
        this.rmbsClaimDf = rmbsClaimDf;
        this.data = data;
        this.user = SecurityUtils.getUser();
        return this;
    }

    public FtRmbsClaimDf getRmbsClaimDf() {
        if (rmbsClaimDf == null) {
            rmbsClaimDf = getBean(FtRmbsClaimDfService.class).getOne(Wrappers.lambdaQuery(FtRmbsClaimDf.class).eq(FtRmbsClaimDf::getClaimId, rmbsClaimDf.getClaimId()));
        }
        return rmbsClaimDf;
    }

    public FtRmbsClaimDf getRmbsClaimDfByNo(String claimNo){
        if (rmbsClaimDf != null){
            rmbsClaimDfByNo = getBean(FtRmbsClaimDfService.class).getOne(Wrappers.lambdaQuery(FtRmbsClaimDf.class).eq(FtRmbsClaimDf::getClaimNo,claimNo));
        }
        return rmbsClaimDfByNo;
    }

    public FtRmbsClaimDf getRmbsClaimDfByData(Map<String, String> data){
        if (rmbsClaimDf != null){
            this.data = data;
            rmbsClaimDfByNo = getBean(FtRmbsClaimDfService.class).getOne(Wrappers.lambdaQuery(FtRmbsClaimDf.class).eq(FtRmbsClaimDf::getClaimNo,data.get("assClaimNo")));
        }
        return rmbsClaimDfByNo;
    }

    public List<FtRmbsClaimDf> getRmbsClaimDfByProjectMisNo(String projectMisNo){
        if (rmbsClaimDf != null){
            rmbsClaimDfByProjectMisNo = getBean(FtRmbsClaimDfService.class).list(Wrappers.lambdaQuery(FtRmbsClaimDf.class).eq(FtRmbsClaimDf::getProjectMisNo,projectMisNo).ne(FtRmbsClaimDf::getProcessStateEng,"step10").ne(FtRmbsClaimDf::getClaimId,rmbsClaimDf.getClaimId()));
        }
        return rmbsClaimDfByProjectMisNo;
    }


    public List<FtRmbsPaylistDf> getRmbsPaylistDf() {
        if (rmbsPaylistDf == null) {
            rmbsPaylistDf = getBean(FtRmbsPaylistDfService.class).list(Wrappers.lambdaQuery(FtRmbsPaylistDf.class).eq(FtRmbsPaylistDf::getClaimId, getRmbsClaimDf().getClaimId()));
        }
        return rmbsPaylistDf;
    }

    public List<FtRmbsConfidentClaimDf> getRmbsConfidentClaimDf() {
        if (rmbsConfidentClaimDf == null) {
            rmbsConfidentClaimDf = getBean(FtRmbsConfidentClaimDfService.class).list(Wrappers.lambdaQuery(FtRmbsConfidentClaimDf.class).eq(FtRmbsConfidentClaimDf::getClaimId, getRmbsClaimDf().getClaimId()));
        }
        return rmbsConfidentClaimDf;
    }

    public List<FtRmbsClaimLineDf> getRmbsClaimLineDfList(){
        if (rmbsClaimLineDfList == null){
            rmbsClaimLineDfList = getBean(FtRmbsClaimLineDfService.class).list(Wrappers.lambdaQuery(FtRmbsClaimLineDf.class).eq(FtRmbsClaimLineDf::getClaimId,getRmbsClaimDf().getClaimId()));
        }
        return rmbsClaimLineDfList;
    }


    public void check002() {
        //报账金额
        String claimNo = getRmbsClaimDf().getProjectMisNo();
        if (StringUtils.isNotBlank(claimNo)){
            BigDecimal applyAmount = getRmbsClaimDf().getApplyAmount();
            FtRmbsClaimDf ftRmbsClaimDf = getRmbsClaimDfByNo(claimNo);
            BigDecimal amount = ftRmbsClaimDf.getApplyAmount();

            if (applyAmount.compareTo(amount) == 1){
                throw new BusinessException("验证失败:报账金额不能大于审批单金额");
            }
        }
    }

    public void check003() {
        if (getRmbsPaylistDf().size() == 0) {
            throw new BusinessException("验证失败:付款清单不能为空");
        }
        BigDecimal payListTotal = BigDecimal.ZERO;
        for (FtRmbsPaylistDf ftRmbsPaylistDf : getRmbsPaylistDf()) {
            payListTotal = payListTotal.add(ftRmbsPaylistDf.getPayAmount());
        }
        if (!(payListTotal.compareTo(getRmbsClaimDf().getApplyAmount()) == 0)) {
            throw new BusinessException("验证失败:付款清单金额必须等于报账金额");
        }
    }

    public void check004() {
        BigDecimal c03 = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(getRmbsClaimDf().getC03())) {
            c03 = new BigDecimal(getRmbsClaimDf().getC03());
        }
        if (StringUtils.isNotBlank(getRmbsClaimDf().getApplyAmount() + "")) {
            if (getRmbsClaimDf().getApplyAmount().compareTo(c03) == 1) {
                throw new BusinessException("验证失败:报账金额不能大于预算年度剩余金额!");
            }
        } else {
            throw new BusinessException("验证失败:报账金额不能空");
        }
    }

    /**
     * 记账凭证记录不能少于两条
     */
    public void check005() {
        if (getRmbsClaimLineDfList().isEmpty()){
            throw new BusinessException("验证失败:明细信息中不能为空");
        }
    }

    public void check006(){
       if (getRmbsClaimDf().getApplyAmount().compareTo(getRmbsClaimDf().getPayAmount()) != 0){
           throw new BusinessException("验证失败:列账金额必须和付款金额相等");
       }
    }

    public void check007(){
        if (ObjectUtil.isNull(getRmbsClaimDfByData(data))){
            throw new BusinessException("验证失败:根据审批单号查询的审批信息为空，请检查审批单号是否存在");
        }else {
            FtRmbsClaimDf ftRmbsClaimDf = getRmbsClaimDfByData(data);
            if (ftRmbsClaimDf.getApplyAmount().compareTo(getRmbsClaimDf().getPayAmount()) == -1){
                throw new BusinessException("验证失败:付款金额必须小于等于原报账单付款金额");
            }
        }
    }

    public void check008(){
        if (getRmbsPaylistDf().size() == 0) {
            throw new BusinessException("验证失败:付款清单不能为空");
        }
    }

    public void check009(){
        FtRmbsClaimDf claimDf = getRmbsClaimDf();
        if (claimDf.getProjectMisNo() == null && !"1000".equals(claimDf.getItem2Id())) {
            throw new BusinessException("验证失败:审批单号不能为空");
        }
    }

    public void check010(){
        FtRmbsClaimDf claimDf = getRmbsClaimDf();
        List<FtRmbsClaimDf> rmbsClaimDfByProjectMisNo = getRmbsClaimDfByProjectMisNo(claimDf.getProjectMisNo());
        String claimNos = rmbsClaimDfByProjectMisNo.stream().map(FtRmbsClaimDf::getClaimNo).collect(Collectors.joining(","));
        if (claimDf.getProjectMisNo() != null && !rmbsClaimDfByProjectMisNo.isEmpty()) {
            throw new BusinessException(String.format("验证失败:审批单号已被申请人：%s的党费单号：%s选中提交",claimDf.getApplyUserName(),claimNos));
        }
    }

    public static <T> T getBean(Class<T> clazz) {
        if (cache.containsKey(clazz)) {
            return (T) cache.get(clazz);
        } else {
            T bean = SpringContextUtils.getBean(clazz);
            cache.put(clazz, bean);
            return bean;
        }
    }
}
