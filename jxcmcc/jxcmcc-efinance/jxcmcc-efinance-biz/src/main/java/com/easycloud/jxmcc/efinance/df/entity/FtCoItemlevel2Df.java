/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 党费系统业务大类表
 *
 * <AUTHOR>
 * @date 2022-04-02 15:54:00
 */
@Data
@TableName("FT_CO_ITEMLEVEL2_DF")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "党费系统业务大类表")
public class FtCoItemlevel2Df extends Model<FtCoItemlevel2Df> {
private static final long serialVersionUID = 1L;

    /**
     * ;
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String id;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String itemId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String itemName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String fatherItem;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer state;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String description;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String provincePiid;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String provincePtVersion;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String cityPiid;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String cityPtVersion;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String countyPiid;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String countyPtVersion;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String zhongxinPiid;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String zhongxinPtVersion;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String isSelfPay;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String isPriority;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String summary;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String hasFywdsx;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer sortField;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer isVisible;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apCrSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apCrSegCode;
    }
