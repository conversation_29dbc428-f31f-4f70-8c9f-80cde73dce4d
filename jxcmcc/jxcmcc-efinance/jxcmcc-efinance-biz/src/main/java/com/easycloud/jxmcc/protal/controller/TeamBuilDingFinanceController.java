package com.easycloud.jxmcc.protal.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.admin.api.feign.RemoteUserService;
import com.easycloud.jxcmcc.common.core.exception.BusinessException;
import com.easycloud.jxcmcc.common.core.util.IdUtils;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.util.WebUtils;
import com.easycloud.jxcmcc.common.data.builder.QueryWrapperBuilder;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.efinance.util.TCmccClaimUtil;
import com.easycloud.jxmcc.protal.entity.teambuildingentity.*;
import com.easycloud.jxmcc.protal.service.teambuildingservice.*;
import com.easycloud.jxmcc.protal.util.BeanHelperUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@AllArgsConstructor
@RequestMapping("/teamBuilDingFinanceController" )
@Api(value = "teamBuilDingFinanceController", tags = "团队建设财务信息管理")
@Slf4j
public class TeamBuilDingFinanceController {

    private final FtProtalAccountantStaffInfoService ftProtalAccountantStaffInfoService;
    private final FtProtalFinancialStaffAddinfoService ftProtalFinancialStaffAddinfoService;
    private final FtProtalFinancialStaffProinfoService ftProtalFinancialStaffProinfoService;
    private final FtProtalFinancialStaffInfoService ftProtalFinancialStaffInfoService;
    private final FtProtalFinancialStaffExpertinfoService ftProtalFinancialStaffExpertinfoService;
    private final RemoteUserService remoteUserService;
    private final FtProtalFinanceProvinceMajorService ftProtalFinanceProvinceMajorService;
    private final FtProtalFinanceIpaTransitService ftProtalFinanceIpaTransitService;
    private final FtProtalFinancialStaffLecturerService ftProtalFinancialStaffLecturerservice;


    @ApiOperation(value = "财务人员信息录入", notes = "财务人员信息录入")
    @SysLog(value = "财务人员信息录入")
    @PostMapping("/saveFormbasic")
    public R saveFormbasic(@RequestBody FtProtalFinancialStaffInfo ftProtalFinancialStaffInfo,String method){
        String id= IdUtils.getIncreaseIdByCurrentTimeMillis();
        JxcmccUser user = SecurityUtils.getUser();
        ftProtalFinancialStaffInfo.setStartDeptCode(user.getUser().getOrgCode());
        return R.ok(ftProtalFinancialStaffInfoService.save(ftProtalFinancialStaffInfo),id);
    }

    @ApiOperation(value = "根据id查询财务人员信息", notes = "根据id查询财务人员信息")
    @SysLog("根据id查询财务人员信息" )
    @GetMapping("/selectFormbasicById")
    public R selectFormbasicById(@RequestParam("id") String id){
        return R.ok(ftProtalFinancialStaffInfoService.getById(id));
    }

    @ApiOperation(value = "财务人员信息修改", notes = "财务人员信息修改")
    @SysLog(value = "财务人员信息修改")
    @PostMapping("/updateFormbasic")
    public R updateFormbasic(@RequestBody FtProtalFinancialStaffInfo ftProtalFinancialStaffInfo,String method){
        JxcmccUser user = SecurityUtils.getUser();
        ftProtalFinancialStaffInfoService.checkFinanceCode(user.getOrgCode(),user.getUser().getOrgCode())  ;
        ftProtalFinancialStaffInfo.setStartDeptCode(user.getUser().getOrgCode());
        if (StringUtils.isNotBlank(ftProtalFinancialStaffInfo.getOrgCode())){
            ftProtalFinancialStaffInfo.setOrgName(TCmccClaimUtil.COMPANY_MAP.get(ftProtalFinancialStaffInfo.getOrgCode()));
        }
        ftProtalFinancialStaffInfo.setUpdateDate(new Date()); //更新时间
        return R.ok(ftProtalFinancialStaffInfoService.saveOrUpdate(ftProtalFinancialStaffInfo));
    }

    @ApiOperation(value = "根据id删除财务人员信息", notes = "根据id删除财务人员信息")
    @SysLog("根据id删除财务人员信息" )
    @GetMapping("/delFormbasicById")
    @PreAuthorize("@pms.hasPermission('portal_cwpersonnel_del')" )
    public R delFormbasicById(@RequestParam("id") String id){
        ftProtalFinancialStaffInfoService.removeById(id);



        return R.ok();
    }

    @ApiOperation(value = "财务人员专业信息录入", notes = "财务人员专业信息录入")
    @SysLog(value = "财务人员专业信息录入")
    @PostMapping("/savespecialty")
    public R savespecialty(@RequestBody FtProtalFinancialStaffProinfo ftProtalFinancialStaffInfo){
        JxcmccUser user = SecurityUtils.getUser();
        ftProtalFinancialStaffInfoService.checkFinanceCode(user.getOrgCode(),user.getUser().getOrgCode())  ;
        if(ftProtalFinancialStaffInfoService.checkObjAllFieldsIsNull(ftProtalFinancialStaffInfo)){
            return R.ok();
        }
        return R.ok(ftProtalFinancialStaffProinfoService.save(ftProtalFinancialStaffInfo));
    }

    @ApiOperation(value = "财务人员岗位信息录入", notes = "财务人员岗位信息录入")
    @SysLog(value = "财务人员岗位信息录入")
    @PostMapping("/savepost")
    public R savepost(@RequestBody FtProtalFinancialStaffAddinfo ftProtalFinancialStaffInfo){
        JxcmccUser user = SecurityUtils.getUser();
        ftProtalFinancialStaffInfoService.checkFinanceCode(user.getOrgCode(),user.getUser().getOrgCode())  ;
        if(ftProtalFinancialStaffInfoService.checkObjAllFieldsIsNull(ftProtalFinancialStaffInfo)){
            return R.ok();
        }
        return R.ok(ftProtalFinancialStaffAddinfoService.save(ftProtalFinancialStaffInfo));
    }

    @ApiOperation(value = "财务人员岗位信息删除", notes = "财务人员岗位信息删除")
    @SysLog(value = "财务人员岗位信息删除")
    @GetMapping("/removep")
    public R removep(@RequestParam("id") String id){
        return R.ok(ftProtalFinancialStaffAddinfoService.removeById(id));
    }

    @ApiOperation(value = "财务人员专业信息删除", notes = "财务人员专业信息删除")
    @SysLog(value = "财务人员专业信息删除")
    @GetMapping("/removes")
    public R removes(@RequestParam("id") String id){
        return R.ok(ftProtalFinancialStaffProinfoService.removeById(id));
    }

    @ApiOperation(value = "财务人员集团财务信息录入", notes = "财务人员集团财务信息录入")
    @SysLog(value = "财务人员集团财务信息录入")
    @PostMapping("/saveFgroup")
    public R saveFgroup(@RequestBody FtProtalFinancialStaffExpertinfo ftProtalAccountantStaffInfo) {
        JxcmccUser user = SecurityUtils.getUser();
        ftProtalFinancialStaffInfoService.checkFinanceCode(user.getOrgCode(),user.getUser().getOrgCode())  ;
        if(ftProtalFinancialStaffInfoService.checkObjAllFieldsIsNull(ftProtalAccountantStaffInfo)){
            return R.ok();
        }
        return R.ok(ftProtalFinancialStaffExpertinfoService.save(ftProtalAccountantStaffInfo));
    }

    @ApiOperation(value = "财务人员集团财务信息删除", notes = "财务人员集团财务信息删除")
    @SysLog(value = "财务人员集团财务信息删除")
    @GetMapping("/removefg")
    public R removefg(@RequestParam("id") String id){
        return R.ok(ftProtalFinancialStaffExpertinfoService.removeById(id));
    }

    @ApiOperation(value = "财务人员集团财务信息", notes = "财务人员集团财务信息")
    @SysLog("财务人员集团财务信息" )
    @GetMapping(value ="/getFgroupList" )
    public R  getFgroupList(Page page,FtProtalFinancialStaffExpertinfo ftProtalFinancialStaffExpertinfo){
        QueryWrapper<FtProtalFinancialStaffExpertinfo> build= QueryWrapperBuilder.create(FtProtalFinancialStaffExpertinfo.class).build(WebUtils.getParameterMap());
        return R.ok(ftProtalFinancialStaffExpertinfoService.page(page,build));
    }

    @ApiOperation(value = "根据id查询财务人员信息", notes = "根据id查询财务人员信息")
    @SysLog("根据id查询财务人员信息" )
    @GetMapping("/getFinancePeopleById")
    public R getFinancePeopleById(@RequestParam("id") String id){
        return R.ok(ftProtalFinancialStaffInfoService.getById(id));
    }

    /**
     * 报账员信息录入
     * @return
     */
    @ApiOperation(value = "报账员信息录入", notes = "报账员信息录入")
    @SysLog(value = "报账员信息录入")
    @PostMapping("/saveReimbursementclerk")
    public R saveReimbursementclerk(@RequestBody FtProtalAccountantStaffInfo ftProtalAccountantStaffInfo) {
        if (StringUtils.isNotBlank(ftProtalAccountantStaffInfo.getOrgName())){
            ftProtalAccountantStaffInfo.setOrgName(TCmccClaimUtil.COMPANY_MAP.get(ftProtalAccountantStaffInfo.getOrgName()));
        }
        return R.ok(ftProtalAccountantStaffInfoService.save(ftProtalAccountantStaffInfo));
    }

    @ApiOperation(value = "根据id查询报账员信息", notes = "根据id查询报账员信息")
    @SysLog("根据id查询报账员信息" )
    @GetMapping("/getReimbursementById")
    public R getReimbursementById(@RequestParam("id") String id){
        return R.ok(ftProtalAccountantStaffInfoService.getById(id));
    }

    @ApiOperation(value = "修改报账员信息", notes = "修改报账员信息")
    @SysLog("修改报账员信息" )
    @PostMapping("/updateReimbursement")
    @PreAuthorize("@pms.hasPermission('portal_cwpersonnel_edit')" )
    public R updateReimbursement(@RequestBody FtProtalAccountantStaffInfo ftProtalAccountantStaffInfo){
        return R.ok(ftProtalAccountantStaffInfoService.updateById(ftProtalAccountantStaffInfo));
    }

    @ApiOperation(value = "修改财务集团信息", notes = "修改财务集团信息")
    @SysLog("修改财务集团信息" )
    @PostMapping("/updateFgroup")
    public R updateFgroup(@RequestBody FtProtalFinancialStaffExpertinfo ftProtalFinancialStaffExpertinfo){
        return R.ok(ftProtalFinancialStaffExpertinfoService.updateById(ftProtalFinancialStaffExpertinfo));
    }

    /**
     * 财务服务支撑导出
     * @return R
     */
    @ApiOperation(value = "报账员信息导出", notes = "报账员信息导出")
    @SysLog("报账员信息导出")
    @GetMapping(value = "/exportBZY")
    public void exportExcelBYY(HttpServletRequest request, HttpServletResponse response, FtProtalAccountantStaffInfo ftProtalAccountantStaffInfo) {
        JxcmccUser user = SecurityUtils.getUser();
        QueryWrapper build = BeanHelperUtil.setQueryWrapper(ftProtalAccountantStaffInfo);
        if(StringUtils.isBlank(WebUtils.getParameterMap().get("orgCode"))){
            if (!"303710".equals(user.getOrgCode())){
                build.eq("org_Name",TCmccClaimUtil.COMPANY_MAP.get(user.getOrgCode()));
            }
        }else {
            build.eq("org_Name", TCmccClaimUtil.COMPANY_MAP.get(WebUtils.getParameterMap().get("orgCode")));
        }
        List<FtProtalAccountantStaffInfo> ftProtalAccountantStaffInfoList=ftProtalAccountantStaffInfoService.list(build);   //导出Excel
        for (int i = 0; i < ftProtalAccountantStaffInfoList.size(); i++) {
            FtProtalAccountantStaffInfo ftProtalAccountantStaffInfo1 = ftProtalAccountantStaffInfoList.get(i);
            ftProtalAccountantStaffInfo1.setSequence(String.valueOf(i+1));
        }
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("报账员信息", "报账员信息"),
                    FtProtalAccountantStaffInfo.class,ftProtalAccountantStaffInfoList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "根据id查询岗位认证信息", notes = "根据id查询岗位认证信息")
    @SysLog("根据id查询岗位认证信息" )
    @GetMapping("/getFinancePostById")
    public R getFinancePostById(@RequestParam("id") String id){
        return R.ok(ftProtalFinancialStaffAddinfoService.getById(id));
    }

    @ApiOperation(value = "岗位认证信息导出", notes = "岗位认证信息导出")
    @SysLog("岗位认证信息导出")
    @GetMapping(value = "/exportGW")
    public void exportExcelGW(HttpServletRequest request, HttpServletResponse response, FtProtalFinancialStaffAddinfo ftProtalFinancialStaffAddinfo) {
        JxcmccUser user = SecurityUtils.getUser();
        QueryWrapper build = BeanHelperUtil.setQueryWrapper(ftProtalFinancialStaffAddinfo);
        if(StringUtils.isBlank(WebUtils.getParameterMap().get("orgCode"))){
            if (!"303710".equals(user.getOrgCode())){
                build.eq("org_Code",user.getOrgCode());
            }
        }
        build.isNotNull("employee_Num");
        List<FtProtalFinancialStaffAddinfo> ftProtalFinancialStaffAddinfoList=ftProtalFinancialStaffAddinfoService.list(build);   //导出Excel
        for (FtProtalFinancialStaffAddinfo protalFinancialStaffAddinfo : ftProtalFinancialStaffAddinfoList) {
            if ("U".equals(protalFinancialStaffAddinfo.getType())){
                protalFinancialStaffAddinfo.setType("非财经");
            }else if ("F".equals(protalFinancialStaffAddinfo.getType())){
                protalFinancialStaffAddinfo.setType("财经");
            }
        }
        for (int i = 0; i < ftProtalFinancialStaffAddinfoList.size(); i++) {
            FtProtalFinancialStaffAddinfo ftProtalFinancialStaffAddinfo1 = ftProtalFinancialStaffAddinfoList.get(i);
            ftProtalFinancialStaffAddinfo1.setSequence(String.valueOf(i+1));
        }
        try {
            ExportParams exportParams = new ExportParams();
            exportParams.setTitle("财务人员岗位认证信息");
            exportParams.setSecondTitle("导出时间:"+new DateTime());
            exportParams.setSheetName("财务人员岗位认证表");
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams,
                    FtProtalFinancialStaffAddinfo.class,ftProtalFinancialStaffAddinfoList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "根据id查询专业资格信息", notes = "根据id查询专业资格信息")
    @SysLog("根据id查询专业资格信息" )
    @GetMapping("/getFinancemajorById")
    public R getFinancemajorById(@RequestParam("id") String id){
        return R.ok(ftProtalFinancialStaffProinfoService.getById(id));
    }

    @ApiOperation(value = "专业资格信息导出", notes = "专业资格信息导出")
    @SysLog("专业资格信息导出")
    @GetMapping(value = "/exportZY")
    public void exportExcelZY(HttpServletRequest request, HttpServletResponse response, FtProtalFinancialStaffProinfo ftProtalFinancialStaffAddinfo) {
        JxcmccUser user = SecurityUtils.getUser();
        QueryWrapper build = BeanHelperUtil.setQueryWrapper(ftProtalFinancialStaffAddinfo);
        if(StringUtils.isBlank(WebUtils.getParameterMap().get("orgCode"))){
            if (!"303710".equals(user.getOrgCode())){
                build.eq("org_Code",user.getOrgCode());
            }
        }
        build.isNotNull("employee_Num");
        List<FtProtalFinancialStaffProinfo> ftProtalFinancialStaffProinfos=ftProtalFinancialStaffProinfoService.list(build);   //导出Excel
        for (FtProtalFinancialStaffProinfo ftProtalFinancialStaffProinfo : ftProtalFinancialStaffProinfos) {
            if ("U".equals(ftProtalFinancialStaffProinfo.getType())){
                ftProtalFinancialStaffProinfo.setType("非财经");
            }else if ("F".equals(ftProtalFinancialStaffProinfo.getType())){
                ftProtalFinancialStaffProinfo.setType("财经");
            }
        }
        for (int i = 0; i < ftProtalFinancialStaffProinfos.size(); i++) {
            FtProtalFinancialStaffProinfo ftProtalFinancialStaffProinfo = ftProtalFinancialStaffProinfos.get(i);
            ftProtalFinancialStaffProinfo.setSequence(String.valueOf(i+1));
        }
        try {
            ExportParams exportParams = new ExportParams();
            exportParams.setTitle("财务人员专业资格信息");
            exportParams.setSecondTitle("导出时间:"+new DateTime());
            exportParams.setSheetName("财务人员专业资格表");
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams,
                    FtProtalFinancialStaffProinfo.class,ftProtalFinancialStaffProinfos);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "财务人员信息导出", notes = "财务人员信息导出")
    @SysLog("财务人员信息导出")
    @GetMapping(value = "/exportCWRY")
    public void exportCWRY(HttpServletRequest request, HttpServletResponse response, FtProtalFinancialStaffInfo ftProtalFinancialStaffInfo) {
        JxcmccUser user = SecurityUtils.getUser();
        QueryWrapper build = BeanHelperUtil.setQueryWrapper(ftProtalFinancialStaffInfo);
        if(StringUtils.isBlank(WebUtils.getParameterMap().get("orgCode"))){
            if (!"303710".equals(user.getOrgCode())){
                build.eq("org_Code",user.getOrgCode());
            }
        }
        build.orderByDesc("update_Date");
        List<FtProtalFinancialStaffInfo> ftProtalFinancialStaffInfos=ftProtalFinancialStaffInfoService.list(build);   //导出Excel
        for (int i = 0; i < ftProtalFinancialStaffInfos.size(); i++) {
            FtProtalFinancialStaffInfo protalFinancialStaffInfo = ftProtalFinancialStaffInfos.get(i);
            protalFinancialStaffInfo.setSequence(String.valueOf(i+1));
        }
        try {
            Map<String,Object> map = new HashMap();
            map.put("list",ftProtalFinancialStaffInfos);
            TemplateExportParams params = new TemplateExportParams("templates/poi/robot/FinancialUserInformation.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = response.getOutputStream();
            workbook.write(oss);
            oss.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "财务人员信息统计表", notes = "财务人员信息统计表")
    @SysLog("财务人员信息统计表" )
    @GetMapping(value ="/getReport" )public R  getReport(){
        JxcmccUser user = SecurityUtils.getUser();
        Map<String,Object> map=new HashMap<>();
        map.put("303710","省本部");map.put("303726","抚州");
        map.put("303788","南昌");map.put("303721","鹰潭");
        map.put("303729","赣州");map.put("303727","宜春");
        map.put("303728","吉安");map.put("303722","新余");
        map.put("303730","景德镇"); map.put("303725","上饶");
        map.put("303724","九江"); map.put("303731","萍乡");
        String orgName= (String) map.get(user.getOrgCode());
        return R.ok(ftProtalFinancialStaffInfoService.selectReport(orgName));
    }

    @ApiOperation(value = "获取当前用户人信息", notes = "获取当前用户人信息")
    @SysLog("获取当前用户人信息" )
    @GetMapping(value ="/getLoginperson" )
    public R  getLoginperson(){
        JxcmccUser user = SecurityUtils.getUser();
        QueryWrapper build = QueryWrapperBuilder.<FtProtalFinancialStaffInfo>create(FtProtalFinancialStaffInfo.class).build(WebUtils.getParameterMap());
        build.eq("EMAIL",user.getUser().getEmail());
        ftProtalFinancialStaffInfoService.checkFinanceCode(user.getOrgCode(),user.getUser().getOrgCode())  ;
        List<FtProtalFinancialStaffInfo> ftProtalFinancialStaffInfoList=ftProtalFinancialStaffInfoService.list(build);
        if (CollUtil.isEmpty(ftProtalFinancialStaffInfoList)){
            FtProtalFinancialStaffInfo info = new FtProtalFinancialStaffInfo();
            info.setStatus(TCmccClaimUtil.CLAIM_STEP_10_CODE);
            /*info.setId(IdWorker.getIdStr());*/
            info.setEmail(user.getUser().getEmail());
            info.setEmployeeName(user.getRealname());
            info.setOrgCode(user.getOrgCode());
            info.setOrgName(TCmccClaimUtil.COMPANY_MAP.get(user.getOrgCode()));
            /*ftProtalFinancialStaffInfoService.save(info);*/
            ftProtalFinancialStaffInfoList.add(info);
        }
        return R.ok(ftProtalFinancialStaffInfoList);
    }

    @ApiOperation(value = "台账数据导出", notes = "台账数据导出")
    @SysLog("台账数据导出")
    @GetMapping("/reportExport")
    public void reportExport(HttpServletRequest request, HttpServletResponse response) {
        try {
            R l=getReport();
            List<Map<String,Object>> reportList=(List<Map<String,Object>>)l.getData();
            Map<String, Object> map = new HashMap<>(8);
            for (int i = 0; i < reportList.size(); i++) {
                Map<String,Object> report = reportList.get(i);
                report.put("sequence",String.valueOf(i+1));
            }
            map.put("list",reportList);
            TemplateExportParams params = new TemplateExportParams("templates/poi/statisticaltable.xls");
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = response.getOutputStream();
            workbook.write(oss);
            oss.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "财务人员省级专家信息录入", notes = "财务人员省级专家信息录入")
    @SysLog(value = "财务人员省级专家信息录入")
    @PostMapping("/saveMajorGrade")
    public R saveMajorGrade(@RequestBody FtProtalFinanceProvinceMajor ftProtalFinanceProvinceMajor) {
        JxcmccUser user = SecurityUtils.getUser();
        ftProtalFinancialStaffInfoService.checkFinanceCode(user.getOrgCode(),user.getUser().getOrgCode());
        if(ftProtalFinancialStaffInfoService.checkObjAllFieldsIsNull(ftProtalFinanceProvinceMajor)){
            return R.ok();
        }
        return R.ok(ftProtalFinanceProvinceMajorService.save(ftProtalFinanceProvinceMajor));
    }

    @ApiOperation(value = "财务人员省级专家信息删除", notes = "财务人员省级专家信息删除")
    @SysLog(value = "财务人员省级专家信息删除")
    @GetMapping("/removeMajorGrade")
    public R removeMajorGrade(@RequestParam("id") String id){
        return R.ok(ftProtalFinanceProvinceMajorService.removeById(id));
    }

    @ApiOperation(value = "财务人员省级专家信息", notes = "财务人员省级专家信息")
    @SysLog("财务人员省级专家信息" )
    @GetMapping(value ="/getMajorGradeList" )
    public R  getMajorGradeList(Page page,FtProtalFinanceProvinceMajor ftProtalFinanceProvinceMajor){
        QueryWrapper<FtProtalFinanceProvinceMajor> build= QueryWrapperBuilder.create(FtProtalFinanceProvinceMajor.class).build(WebUtils.getParameterMap());
        return R.ok(ftProtalFinanceProvinceMajorService.page(page,build));
    }

    @ApiOperation(value = "财务人员数智化认证信息录入", notes = "财务人员数智化认证信息录入")
    @SysLog(value = "财务人员数智化认证信息录入")
    @PostMapping("/saveTransit")
    public R saveTransit(@RequestBody FtProtalFinanceIpaTransit ftProtalFinanceIpaTransit) {
        JxcmccUser user = SecurityUtils.getUser();
        ftProtalFinancialStaffInfoService.checkFinanceCode(user.getOrgCode(),user.getUser().getOrgCode());
        if(ftProtalFinancialStaffInfoService.checkObjAllFieldsIsNull(ftProtalFinanceIpaTransit)){
            return R.ok();
        }
        return R.ok(ftProtalFinanceIpaTransitService.save(ftProtalFinanceIpaTransit));
    }

    @ApiOperation(value = "财务人员数智化认证信息删除", notes = "财务人员数智化认证信息删除")
    @SysLog(value = "财务人员数智化认证信息删除")
    @GetMapping("/removeTransit")
    public R removeTransit(@RequestParam("id") String id){
        return R.ok(ftProtalFinanceIpaTransitService.removeById(id));
    }

    @ApiOperation(value = "财务人员数智化认证信息", notes = "财务人员数智化认证信息")
    @SysLog("财务人员数智化认证信息" )
    @GetMapping(value ="/getTransitList" )
    public R  getTransitList(Page page,FtProtalFinanceIpaTransit ftProtalFinanceIpaTransit){
        QueryWrapper<FtProtalFinanceIpaTransit> build= QueryWrapperBuilder.create(FtProtalFinanceIpaTransit.class).build(WebUtils.getParameterMap());
        return R.ok(ftProtalFinanceIpaTransitService.page(page,build));
    }


    @ApiOperation(value = "财务人员省级认证讲师信息录入", notes = "财务人员省级认证讲师信息录入")
    @SysLog(value = "财务人员省级认证讲师信息录入")
    @PostMapping("/saveLecuturer")
    public R saveLecuturer(@RequestBody FtProtalFinancialStaffLecturer ftProtalFinancialStaffLecturer) {
        JxcmccUser user = SecurityUtils.getUser();
        ftProtalFinancialStaffInfoService.checkFinanceCode(user.getOrgCode(),user.getUser().getOrgCode());
        if(ftProtalFinancialStaffInfoService.checkObjAllFieldsIsNull(ftProtalFinancialStaffLecturer)){
            return R.ok();
        }
        return R.ok(ftProtalFinancialStaffLecturerservice.save(ftProtalFinancialStaffLecturer));
    }

    @ApiOperation(value = "财务人员省级认证讲师信息删除", notes = "财务人员省级认证讲师信息删除")
    @SysLog(value = "财务人员省级认证讲师信息删除")
    @GetMapping("/removeLecturer")
    public R removeLecturer(@RequestParam("id") String id){
        return R.ok(ftProtalFinancialStaffLecturerservice.removeById(id));
    }

    @ApiOperation(value = "财务人员省级认证讲师信息", notes = "财务人员省级认证讲师信息")
    @SysLog("财务人员省级认证讲师证信息" )
    @GetMapping(value ="/getLecturerList" )
    public R  getLecturerList(Page page,FtProtalFinancialStaffLecturer ftProtalFinancialStaffLecturer){
        QueryWrapper<FtProtalFinancialStaffLecturer> build= QueryWrapperBuilder.create(FtProtalFinancialStaffLecturer.class).build(WebUtils.getParameterMap());
        return R.ok(ftProtalFinancialStaffLecturerservice.page(page,build));
    }


}
