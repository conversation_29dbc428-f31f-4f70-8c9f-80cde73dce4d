/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxmcc.efinance.vo.ClaimLineVo;
import org.springframework.web.multipart.MultipartFile;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsConfidentClaimDf;
import org.w3c.dom.stylesheets.LinkStyle;

import java.util.List;

/**
 * 记账凭证
 *
 * <AUTHOR>
 * @date 2022-08-05 16:07:57
 */
public interface FtRmbsConfidentClaimDfService extends IService<FtRmbsConfidentClaimDf> {



    /**
    * 导入excel
    * @return
    */
    R<Integer>  importExcel(MultipartFile file,JxcmccImportParams importParams) throws Exception ;

    void updateByClaimId(String claimId);

    List<FtRmbsConfidentClaimDf> getByClaimId(String claimId);

    int deleteConfident(ClaimLineVo claimLineVo);

    void updateCode(String claimId);
}
