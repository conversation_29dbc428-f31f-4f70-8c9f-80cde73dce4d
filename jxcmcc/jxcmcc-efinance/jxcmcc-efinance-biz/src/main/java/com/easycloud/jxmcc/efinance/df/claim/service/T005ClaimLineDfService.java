package com.easycloud.jxmcc.efinance.df.claim.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxmcc.efinance.df.vo.claim.T005DFClaimLineVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface T005ClaimLineDfService {


    IPage<T005DFClaimLineVo> page(Page page, T005DFClaimLineVo t005DFClaimLineVo);

    List<T005DFClaimLineVo> getById(String id);

    R importExcel(MultipartFile file, String claimId) throws Exception;
}
