package com.easycloud.jxmcc.efinance.df.claim.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxmcc.efinance.df.vo.claim.T002DFClaimLineVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface T002ClaimLineDfService {


    IPage<T002DFClaimLineVo> page(Page page, T002DFClaimLineVo t002DFClaimLineVo);

    List<T002DFClaimLineVo> getById(String id);

    R importExcel(MultipartFile file, String claimId) throws Exception;
}
