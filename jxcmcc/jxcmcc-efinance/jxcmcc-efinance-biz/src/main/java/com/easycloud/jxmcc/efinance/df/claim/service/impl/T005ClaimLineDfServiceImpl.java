package com.easycloud.jxmcc.efinance.df.claim.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxmcc.efinance.df.claim.mapper.T005ClaimDfMapper;
import com.easycloud.jxmcc.efinance.df.claim.service.IClaimDFEvent;
import com.easycloud.jxmcc.efinance.df.claim.service.T005ClaimLineDfService;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimLineDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimLineDfService;
import com.easycloud.jxmcc.efinance.df.utils.TravelTypeComp;
import com.easycloud.jxmcc.efinance.df.vo.claim.T005DFClaimLineVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class T005ClaimLineDfServiceImpl implements T005ClaimLineDfService, IClaimDFEvent {

    private final T005ClaimDfMapper claimDfMapper;
    private final FtRmbsClaimDfService ftRmbsClaimDf;
    private final FtRmbsClaimLineDfService ftRmbsClaimLineDf;


    public static final String RECEIVE_CLAIM_TYPE = "T005DF";
    public static final String RECEIVE_CLAIM_NAME = "党费支出报账单";

    @Override
    public String itemId() {
        return RECEIVE_CLAIM_TYPE;
    }

    @Override
    public void deleteClaim(FtRmbsClaimDf ftRmbsClaimDf) {

    }

    @Override
    public IPage<T005DFClaimLineVo> page(Page page, T005DFClaimLineVo T005DFClaimLineVo) {
        return claimDfMapper.page(page,T005DFClaimLineVo);
    }

    @Override
    public List<T005DFClaimLineVo> getById(String id) {
        return claimDfMapper.getById(id);
    }

    @Override
    public R importExcel(MultipartFile file, String claimId) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！");
        }
        try {
            List<T005DFClaimLineVo> importClaimLine;
            //解析导入EXCEL数据
            importClaimLine = EasyExcel.read(file.getInputStream())
                    .head(T005DFClaimLineVo.class)
                    .sheet(0)
                    .headRowNumber(1)
                    .doReadSync();

            if (importClaimLine.size() == 0) {
                return R.error("导入的数据为空");
            }

            int start = 1;
            for (int i = 0; i < importClaimLine.size(); i++) {
                T005DFClaimLineVo t005DFClaimLineVo = importClaimLine.get(i);
                //检查导入是否合法
                if (StringUtils.isBlank(t005DFClaimLineVo.getTravelTypeName())) {
                    return R.error("第" + (i + start) + "行费用类型不能为空");
                }
                if (StringUtils.isBlank(t005DFClaimLineVo.getItem3Id())) {
                    return R.error("第" + (i + start) + "业务小类id不能为空");
                }
                if (StringUtils.isBlank(t005DFClaimLineVo.getItem3Name())) {
                    return R.error("第" + (i + start) + "业务小类名称不能为空");
                }
                if (StringUtils.isBlank(t005DFClaimLineVo.getF03())) {
                    return R.error("第" + (i + start) + "单价/标准不能为空");
                } else {
                    if (!(t005DFClaimLineVo.getF03().matches("[0-9]+.?[0-9]*"))) {
                        return R.error("第" + (i + start) + "行单价/标准只能为数字");
                    }
                }
                if (StringUtils.isBlank(t005DFClaimLineVo.getF02())) {
                    return R.error("第" + (i + start) + "数量不能为空");
                } else {
                    if (!(t005DFClaimLineVo.getF02().matches("[0-9]+.?[0-9]*"))) {
                        return R.error("第" + (i + start) + "行数量只能为数字");
                    }
                }
                if (StringUtils.isBlank(t005DFClaimLineVo.getF01())) {
                    return R.error("第" + (i + start) + "天数/次数不能为空");
                } else {
                    if (!(t005DFClaimLineVo.getF01().matches("[0-9]+.?[0-9]*"))) {
                        return R.error("第" + (i + start) + "行天数/次数只能为数字");
                    }
                }
                if (StringUtils.isBlank(t005DFClaimLineVo.getClaimLineDesc())) {
                    return R.error("第" + (i + start) + "行备注不能为空");
                }
                if (StringUtils.isBlank(t005DFClaimLineVo.getApplyAmount())) {
                    return R.error("第" + (i + start) + "列账不能为空");
                } else {
                    if (!(t005DFClaimLineVo.getApplyAmount().matches("[0-9]+.?[0-9]*"))) {
                        return R.error("第" + (i + start) + "行列账金额只能为数字");
                    }
                }
            }
            List<FtRmbsClaimLineDf> list = importClaimLine.stream().map(t -> {
                FtRmbsClaimLineDf claimLine = new FtRmbsClaimLineDf();
                //将String类型的f01、f02、f03转换成BigDecimal
                BigDecimal f01 = new BigDecimal(t.getF01());
                BigDecimal f02 = new BigDecimal(t.getF02());
                BigDecimal f03 = new BigDecimal(t.getF03());
                BigDecimal applyAmount = new BigDecimal(t.getApplyAmount());
                claimLine.setF01(f01);
                claimLine.setF02(f02);
                claimLine.setF03(f03);
                claimLine.setApplyAmount(applyAmount);
                //数据类型必须保持一致，否则copy该字段为null
                BeanUtils.copyProperties(t, claimLine);
                //费用类型切分
                String[] split = t.getTravelTypeName().split("\\|");
                claimLine.setTravelTypeId(split[0]==null?"":split[0].trim());
                claimLine.setTravelTypeName(split[1]==null?"":split[1].trim());
                return claimLine;
            }).collect(Collectors.toList());
            List<FtRmbsClaimLineDf> saveList=new ArrayList<>();

            for(int i=0; i<list.size(); i++){
                FtRmbsClaimLineDf ftRmbsClaimLineDf = list.get(i);
                ftRmbsClaimLineDf.setClaimId(claimId);
                //拼接单位
                ftRmbsClaimLineDf.setC01(TravelTypeComp.getC01(ftRmbsClaimLineDf.getTravelTypeId()));
                ftRmbsClaimLineDf.setC02(TravelTypeComp.getC02(ftRmbsClaimLineDf.getTravelTypeId()));
                ftRmbsClaimLineDf.setC03(TravelTypeComp.getC03(ftRmbsClaimLineDf.getTravelTypeId()));
                saveList.add(ftRmbsClaimLineDf);
            }
            ftRmbsClaimLineDf.saveBatch(saveList);
            ftRmbsClaimDf.countAmount(claimId);
            return R.ok(list.size());
        }catch (Exception e){
            return R.error("上传异常");
        }
    }
}
