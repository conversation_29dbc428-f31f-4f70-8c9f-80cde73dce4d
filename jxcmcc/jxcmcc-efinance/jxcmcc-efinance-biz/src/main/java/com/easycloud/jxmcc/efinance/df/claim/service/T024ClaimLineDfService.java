package com.easycloud.jxmcc.efinance.df.claim.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxmcc.efinance.df.vo.claim.T024DFClaimLineVo;
import org.springframework.web.multipart.MultipartFile;


import java.util.List;

public interface T024ClaimLineDfService {


    IPage<T024DFClaimLineVo> page(Page page, T024DFClaimLineVo t024DFClaimLineVo);

    List<T024DFClaimLineVo> getById(String id);

    R importExcel(MultipartFile file, String claimId) throws Exception;
}
