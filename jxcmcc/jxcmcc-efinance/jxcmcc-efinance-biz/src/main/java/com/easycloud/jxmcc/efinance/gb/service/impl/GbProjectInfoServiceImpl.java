/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.gb.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.admin.api.dto.UserInfo;
import com.easycloud.jxcmcc.admin.api.entity.SysDictItem;
import com.easycloud.jxcmcc.admin.api.entity.SysUser;
import com.easycloud.jxcmcc.admin.api.feign.RemoteDictService;
import com.easycloud.jxcmcc.admin.api.feign.RemoteUserService;
import com.easycloud.jxcmcc.common.core.constant.SecurityConstants;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.BeanHelperUtil;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxmcc.efinance.entity.FtRmbsCityAccountConfig;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfo;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfoSplit;
import com.easycloud.jxmcc.efinance.gb.listener.GbProjectInfoListener;
import com.easycloud.jxmcc.efinance.gb.listener.GbSplitProjectInfoListener;
import com.easycloud.jxmcc.efinance.gb.mapper.GbProjectInfoMapper;
import com.easycloud.jxmcc.efinance.gb.service.GbApproverCfgService;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectInfoService;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectInfoSplitService;
import com.easycloud.jxmcc.efinance.service.FtRmbsCityAccountConfigService;
import com.easycloud.jxmcc.efinance.util.CheckObjectIsNullUtils;
import com.easycloud.jxmcc.efinance.util.SystemConfig;
import com.easycloud.jxmcc.eflow.feign.RemoteEflowProcessService;
import com.easycloud.jxmcc.eflow.vo.StartProcessInstanceVo;
import com.easycloud.jxmcc.eflow.vo.TaskEflowApiVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 国拨项目信息管理
 *
 * <AUTHOR> code generator
 * @date 2022-10-18 10:01:22
 */
@Service
@Slf4j
public class GbProjectInfoServiceImpl extends ServiceImpl<GbProjectInfoMapper, GbProjectInfo> implements GbProjectInfoService {

    private final static String FLOW_ID = "gbParentProject";

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RemoteEflowProcessService remoteEflowProcessService;
    @Autowired
    private GbProjectInfoListener gbProjectInfoListener;
    @Autowired
    private GbSplitProjectInfoListener gbSplitProjectInfoListener;
    @Autowired
    private GbProjectInfoSplitService gbProjectInfoSplitService;
    @Autowired
    private GbApproverCfgService gbApproverCfgService;
    @Autowired
    private FtRmbsCityAccountConfigService tRmbsCityAccountConfigService;
    @Autowired
    private RemoteDictService remoteDictService;

    /**
    * 导入excel
    * @return
    */
    @Override
    public R<Integer> importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！" );
        }
        //导入类型，0：覆盖导入，1：增量导入
        String type = importParams.getType();

        ImportParams params = new ImportParams();
        params.setTitleRows(2);//标题总行数
        params.setHeadRows(1);
        //解析导入EXCEL数据
        List<GbProjectInfo> gbProjectInfoList = ExcelImportUtil.importExcel(file.getInputStream(), GbProjectInfo.class, params);
        // 清除空白行
        gbProjectInfoList.removeIf(item -> CheckObjectIsNullUtils.objCheckIsNull(item));
        // 判断是否有数据
        if(null == gbProjectInfoList || gbProjectInfoList.size() < 1){
            return R.ok(0, "文件解析成功，数据行为0");
        }

        // 校验数据
        List<String> errorMsgList = new ArrayList<>();
        StringBuffer sb = null;
        int index = 1;
        for (GbProjectInfo gbProjectInfo : gbProjectInfoList) {
            sb = new StringBuffer();
            if(null == gbProjectInfo.getProjectNo() || gbProjectInfo.getProjectNo().equals("")){
                sb.append("项目编号未填写|");
            }else{
                // 项目编号重复校验
                int count = this.count(Wrappers.lambdaQuery(GbProjectInfo.class).
                        eq(GbProjectInfo::getProjectNo, gbProjectInfo.getProjectNo()).
                        eq(GbProjectInfo::getDeleteFlag, "0"));
                if(count > 0){
                    sb.append("该项目编号【" + gbProjectInfo.getProjectNo() + "】已存在");
                }
                R projectInfoByProjectNo = this.getProjectInfoByProjectNo(gbProjectInfo.getProjectNo());
                if("false".equals(projectInfoByProjectNo.getData().toString())){
                    sb.append(projectInfoByProjectNo.getMsg()+"|");
                }else{
                    JSONObject jsonObject = (JSONObject)JSONObject.toJSON(projectInfoByProjectNo.getData());
                    gbProjectInfo.setProjectName(jsonObject.getString("projectName"));
                }
            }
            if(null == gbProjectInfo.getApprovedAmount() || gbProjectInfo.getApprovedAmount().equals("")){
                sb.append("批复金额未填写|");
            }

            if(null == gbProjectInfo.getPersonLiableNo() || gbProjectInfo.getPersonLiableNo().equals("")){
                sb.append("省项目经理未填写|");
            }else {
                R<UserInfo> userInfo= remoteUserService.info(gbProjectInfo.getPersonLiableNo(), SecurityConstants.FROM_IN);
                if(0==userInfo.getCode()){
                   if(ObjectUtil.isNotEmpty(userInfo.getData())){
                       gbProjectInfo.setPersonLiable( userInfo.getData().getSysUser().getRealname());
                       gbProjectInfo.setPersonLiableNo(userInfo.getData().getSysUser().getUsername());
                       gbProjectInfo.setCompanyCode(userInfo.getData().getSysDept().getOrgCode());
                       gbProjectInfo.setCompanyName(userInfo.getData().getSysDept().getSysCompanyName());
                       gbProjectInfo.setResponsibleDeptCode(userInfo.getData().getSysDept().getSysOrgCode());
                       gbProjectInfo.setResponsibleDeptName(userInfo.getData().getSysDept().getSimpleName());
                   } else {
                       sb.append("省项目经理"+gbProjectInfo.getPersonLiableNo()+"未找到,请输入正确的用户名|");
                   }
                }else{
                    sb.append("省项目经理"+gbProjectInfo.getPersonLiableNo()+"未找到,请输入正确的用户名|");
                }
            }
            if(null == gbProjectInfo.getProjectType() || gbProjectInfo.getProjectType().equals("")){
                sb.append("项目类型未填写|");
            }else{
                R<List<SysDictItem>> listR=  remoteDictService.getDictByType("projectType");
                if(0==(listR.getCode())){
                    if(ObjectUtil.isNotEmpty(listR.getData())){
                         String values= listR.getData().stream().map(x->x.getValue()).collect(Collectors.joining("；"));
                         List<SysDictItem>  sysDictItemList=   listR.getData().stream().filter(x-> gbProjectInfo.getProjectType().equals(x.getValue())).collect(Collectors.toList());
                         if(ObjectUtil.isEmpty(sysDictItemList)){
                             sb.append("项目类型填写错误，请填写【"+values+"】其中一条");
                         }
                    }else {
                        sb.append("项目类型数据字典内容为空|");
                    }
                }else{
                    sb.append("项目类型数据字典未找到|");
                }
            }
            // 拼接错误信息
            if(sb.length() > 0){
                errorMsgList.add(String.format("第%s行数据：%s", index, sb.toString()));
            }

            // 行下标增加
            index += 1;
        }

        // 判断是否有错误信息
        if(null != errorMsgList && errorMsgList.size() > 0){
            return R.failed(0, String.join(",", errorMsgList));
        }
        //查询用户
        List<SysUser> sysUserList = remoteUserService.getList(new SysUser());

        //处理数据
        for (GbProjectInfo gbProjectInfo : gbProjectInfoList) {
            this.getUserNameByImport(sysUserList, gbProjectInfo);
            gbProjectInfo.setDeleteFlag("0");
            gbProjectInfo.setProcessStatus("0"); //流程状态
            gbProjectInfo.setPushStatus("0"); //推送状态
        }

        // 覆盖导入删除数据
        if(type.equals("0")){
            this.remove(Wrappers.lambdaQuery(GbProjectInfo.class));
        }

        // 更新数据
        long start = System.currentTimeMillis();
        this.saveBatch(gbProjectInfoList);
        long end = System.currentTimeMillis();
        log.info("耗时：{}m", (end - start) / 1000);

        return R.ok(gbProjectInfoList.size(), "导入成功(耗时：" + (end - start) / 1000 + "m)");
    }

    /**
     * 获取所有地市信息
     * @return R
     */
    @Override
    public R getCompanyList() {
        List<Map<String, String>> companyList = new ArrayList<>();
        Map<String, String> companyMap = null;
        for(String key: BeanHelperUtil.companyMapOrderCode.keySet()){
            companyMap = new LinkedHashMap<>();
            companyMap.put("compAcctCode", key);
            companyMap.put("companyName", BeanHelperUtil.companyMap.get(key));
            companyList.add(companyMap);
        }

        return R.ok(companyList);
    }

    /**
     * 根据部门名称、用户真实姓名获取用户名
     * @param sysUserList
     * @param gbProjectInfo
     * @return
     */
    private void getUserNameByImport(List<SysUser> sysUserList, GbProjectInfo gbProjectInfo){
        //获取部门名称
        String responsibleDept = null == gbProjectInfo.getResponsibleDeptName() ? "" : gbProjectInfo.getResponsibleDeptName();
        //获取责任人名称
        String personLiable = null == gbProjectInfo.getPersonLiable() ? "" : gbProjectInfo.getPersonLiable();
        String userName = "";
        String deptCode = "";
        for(SysUser sysUser : sysUserList){
            //获取名字
            String realname = null == sysUser.getRealname() ? "" : sysUser.getRealname();
            //获取部门名称
            String deptName = null == sysUser.getDeptName() ? "" : sysUser.getDeptName();
            if(!responsibleDept.equals(deptName) || !personLiable.equals(realname)){
                continue;
            }
            userName = sysUser.getUsername();
            deptCode = sysUser.getOrgCode();
            break;
        }
        //设置用户名
        gbProjectInfo.setPersonLiableNo(userName);
        //设置部门编码
        gbProjectInfo.setResponsibleDeptCode(deptCode);
    }

    /**
     * 发起流程
     *
     * @param idList
     * @return
     */
    @Override
    public R startFlow(List<String> idList) {
        List<GbProjectInfo> gbProList = this.listByIds(idList);
        if(null == gbProList || gbProList.size() < 1){
            return R.failed(false, "未查询到选中数据");
        }
        // 判断流程是否能重新发起(审批流程状态为未发起/未通过，拆分流程未发起的情况下可以重新发起审批流程)
//        List<GbProjectInfo> errorProjectList = gbProList.stream().filter(item -> null != item.getProcessStatus() && (item.getProcessStatus().equals("0") || item.getProcessStatus().equals("3")) && null == item.getReserveField1()).collect(Collectors.toList());
        // 获取审批中和审批完成项目
        List<GbProjectInfo> errorProjectList = gbProList.stream().filter(item -> null != item.getProcessStatus() && (item.getProcessStatus().equals("1") || item.getProcessStatus().equals("2"))).collect(Collectors.toList());
        if(null != errorProjectList && errorProjectList.size() > 0){
            List<String> nameList = errorProjectList.stream().map(GbProjectInfo::getProjectName).collect(Collectors.toList());
            return R.failed(false, StringUtil.join(",", nameList) + "项目正在审批中或已审过，请勿重复发起！");
        }

//        List<GbApproverCfg> cfgList = gbApproverCfgService.list();
        List<FtRmbsCityAccountConfig> ftRmbsCityAccountConfigList = tRmbsCityAccountConfigService.list(Wrappers.lambdaQuery(FtRmbsCityAccountConfig.class)
                .eq(FtRmbsCityAccountConfig::getItemId,"T0133GB"));
        if(null == ftRmbsCityAccountConfigList || ftRmbsCityAccountConfigList.size() < 1){
            return R.failed(false, "未配置省公司会计,请前往初核会计管理配置");
        }

        List<GbProjectInfoSplit> gbProjectInfoSplitList = new ArrayList<>();
        for(GbProjectInfo gbProjectInfo : gbProList){
            TaskEflowApiVo vo = new TaskEflowApiVo();
            vo.setBusinessId(gbProjectInfo.getId());
            StartProcessInstanceVo startProcessInstanceVo = new StartProcessInstanceVo();
            startProcessInstanceVo.setBusinessKey(gbProjectInfo.getId());
            startProcessInstanceVo.setTitle(gbProjectInfoListener.getTitle(vo));
            startProcessInstanceVo.setProcessDefinitionKey("gbParentProject");
            startProcessInstanceVo.setVariables(gbProjectInfoListener.getVariables(vo));
            remoteEflowProcessService.startProcessInstanceByKey(startProcessInstanceVo);
            //生成省本部的拆分数据
            GbProjectInfoSplit gbProjectInfoSplit = new GbProjectInfoSplit();
            BeanUtils.copyProperties(gbProjectInfo,gbProjectInfoSplit);
            gbProjectInfoSplit.setId(null);
            gbProjectInfoSplit.setSplitAmount(gbProjectInfoSplit.getApprovedAmount());
            gbProjectInfoSplit.setCompanyCode("303710");
            gbProjectInfoSplit.setCompanyName("省公司");
            gbProjectInfoSplit.setProcessId(gbProjectInfo.getId());
            gbProjectInfoSplit.setClaimAmount(BigDecimal.ZERO);
            gbProjectInfoSplit.setClaimAmountZf(BigDecimal.ZERO);
            gbProjectInfoSplit.setCollectionClaimAmount(BigDecimal.ZERO);
            gbProjectInfoSplitList.add(gbProjectInfoSplit);
        }
        gbProjectInfoSplitService.saveBatch(gbProjectInfoSplitList);
        return R.ok(true, "流程发起成功");
    }

    /**
     * 发起拆分流程
     *
     * @param idList
     * @return
     */
    @Override
    public R startSplitFlow(List<String> idList) {
        List<GbProjectInfo> gbProList = this.listByIds(idList);
        if(null == gbProList || gbProList.size() < 1){
            return R.failed(false, "未查询到选中数据");
        }

        List<GbProjectInfo> errorProjectList = gbProList.stream().filter(item -> null == item.getProcessStatus() || !item.getProcessStatus().equals("2")).collect(Collectors.toList());
        if(null != errorProjectList && errorProjectList.size() > 0){
            List<String> nameList = errorProjectList.stream().map(GbProjectInfo::getProjectName).collect(Collectors.toList());
            return R.failed(false, StringUtil.join(",", nameList) + "项目未通过审批，无法发起拆分流程！");
        }

        List<GbProjectInfo> errorProjectList2 = gbProList.stream().filter(item -> null != item.getReserveField2() && item.getReserveField2().equals("1")).collect(Collectors.toList());
        if(null != errorProjectList2 && errorProjectList2.size() > 0){
            List<String> nameList = errorProjectList2.stream().map(GbProjectInfo::getProjectName).collect(Collectors.toList());
            return R.failed(false, StringUtil.join(",", nameList) + "项目拆分正在审批中，请勿重复发起！");
        }

        for (GbProjectInfo item : gbProList) {
            TaskEflowApiVo vo = new TaskEflowApiVo();
            vo.setBusinessId(item.getId());
            StartProcessInstanceVo startProcessInstanceVo = new StartProcessInstanceVo();
            startProcessInstanceVo.setBusinessKey(item.getId());
            startProcessInstanceVo.setTitle(gbSplitProjectInfoListener.getTitle(vo));
            startProcessInstanceVo.setProcessDefinitionKey("gbSplitParentProject");
            startProcessInstanceVo.setVariables(gbSplitProjectInfoListener.getVariables(vo));
            remoteEflowProcessService.startProcessInstanceByKey(startProcessInstanceVo);
        }

        return R.ok(true, "拆分流程发起成功");
    }

    /**
     * 根据项目编号查询项目信息
     * @param projectNo projectNo
     * @return R
     */
    @Override
    public R getProjectInfoByProjectNo(String projectNo) {
        // 资产系统项目信息查询
        Map<String, Object> params = new HashMap<>();
        params.put("projectNumber", projectNo);
        String result = HttpUtil.get(SystemConfig.getPro("GB_GET_PROJECT_INFO_ZC"), params);
        log.info("【国拨项目】查询项目信息返回结果：" + result);
        if(null == result || "".equals(result)){
            return R.failed(false, "项目编号校验失败，访问资产系统失败");
        }
        JSONObject jsonObject = JSONObject.parseObject(result);
        String code = jsonObject.getString("code");
        if(null == code || code.equals("1")){
            return R.failed(false, "项目编号校验失败，访问资产系统失败");
        }
        if(null == jsonObject){
            return R.failed(false, "项目编号校验失败，资产系统返回项目信息内容格式不正确");
        }
        String data = jsonObject.getString("data");
        if(null == data || data.equals("false")){
            return R.failed(false, "项目编号校验失败，未在资产系统查询到该项目编号");
        }
        JSONObject info = JSONObject.parseObject(data);
        return R.ok(info);
    }

}
