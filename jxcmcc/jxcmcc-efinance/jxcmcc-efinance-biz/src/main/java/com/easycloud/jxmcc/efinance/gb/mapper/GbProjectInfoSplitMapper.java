/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.data.mapper.BaseMapper;
import com.easycloud.jxmcc.efinance.claim.vo.T0133EntityVo;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfoSplit;
import com.easycloud.jxmcc.efinance.gb.vo.GbProjectInfoSplitVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 国拨项目信息拆分
 *
 * <AUTHOR> code generator
 * @date 2022-10-20 11:24:12
 */
@Mapper
public interface GbProjectInfoSplitMapper extends BaseMapper<GbProjectInfoSplit> {

    /**
     * 查询拆分项目分页
     * @param page
     * @param gbProjectInfoSplitVo
     * @return
     */
    Page<GbProjectInfoSplitVo> page(@Param("page") IPage<GbProjectInfoSplitVo> page, @Param("gbProjectInfoSplitVo") GbProjectInfoSplitVo gbProjectInfoSplitVo);

    /**
     * 查询拆分项目分页
     * @param page
     * @param gbProjectInfoSplitVo
     * @return
     */
    Page<GbProjectInfoSplitVo> pagezf(@Param("page") IPage<GbProjectInfoSplitVo> page, @Param("gbProjectInfoSplitVo") GbProjectInfoSplitVo gbProjectInfoSplitVo);

    /**
     * 查询拆分项目分页
     * @param page
     * @param gbProjectInfoSplit
     * @return
     */
    IPage<GbProjectInfoSplitVo> pageNoClaim(@Param("page") IPage<GbProjectInfoSplitVo> page, @Param("gbProjectInfoSplit") GbProjectInfoSplit gbProjectInfoSplit);


    IPage<T0133EntityVo> getRawClaim(@Param("t0133EntityVo") IPage<T0133EntityVo> page,@Param("vo") T0133EntityVo t0133EntityVo);
}
