package com.easycloud.jxmcc.efinance.df.claim.listener;

import cn.hutool.core.util.StrUtil;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.efinance.service.IEmisTodoService;
import com.easycloud.jxmcc.eflow.listener.IProcessListener;
import com.easycloud.jxmcc.eflow.vo.BaseEflowApiVo;
import com.easycloud.jxmcc.eflow.vo.ProcessEflowApiVo;
import com.easycloud.jxmcc.eflow.vo.TaskEflowApiVo;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

public class EmisTodoDfListener implements IProcessListener {

    @Autowired
    private IEmisTodoService emisTodoService;
    @Autowired
    private FtRmbsClaimDfService ftRmbsClaimDfService;

    @Override
    public Map<String, Object> getVariables(BaseEflowApiVo data) {
        return null;
    }

    @Override
    public String getTitle(TaskEflowApiVo data) {
        return null;
    }

    @Override
    public void start(ProcessEflowApiVo data) {

    }

    @Override
    public void end(ProcessEflowApiVo data) {

    }

    @Override
    public void completed(TaskEflowApiVo data) {

    }

    @Override
    public String businessType() {
        return null;
    }

    @Override
    public void rejected(TaskEflowApiVo data) {

    }

    public Integer getFrom(){
        return 6;
    }


    public String getAppId() {
        return null;
    }
    @Override
    public void taskDeleted(TaskEflowApiVo data) {

		if(StrUtil.isNotBlank(data.getAssignee())){
			String[] assignee = data.getAssignee().split(",");
			//删除待办
			String businessKey = data.getBusinessId();
			String uuid = getUUID(data.getTaskId(), businessKey);
			emisTodoService.updateEmis(data.getProcessId(),assignee[0],uuid,businessKey);
		}
    }

    @Override
    public void taskAssigned(TaskEflowApiVo data) {
		//发送待办
		String businessKey = data.getBusinessId();
        FtRmbsClaimDf ftRmbsClaimDf = ftRmbsClaimDfService.getById(businessKey);
        String claimNo = ftRmbsClaimDf.getClaimNo();
        String title = getTitle(data);
		String uuid = getUUID(data.getTaskId(), businessKey);
		String link = StrUtil.indexedFormat("&component={0}&businessId={1}&activityId={2}&taskId={3}&deptCode={4}&from={5}",data.getProcessId(),businessKey,data.getTaskDefinitionKey(),data.getTaskId(),data.getDeptCode(),getFrom());
		emisTodoService.sendEmis(data.getProcessId(),claimNo,title,uuid,data.getUsername(),businessKey,link,data.getName(),getFrom(),getAppId());

    }
    private String getUUID(String id,String businessId) {
        return businessId+ "#" + id;
    }

    @Override
    public void processDelete(ProcessEflowApiVo data) {

    }

    @Override
    public void deleteProcessHistory(ProcessEflowApiVo data) {

    }
}
