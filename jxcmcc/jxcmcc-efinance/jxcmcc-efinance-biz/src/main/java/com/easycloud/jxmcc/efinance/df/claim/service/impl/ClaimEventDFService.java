package com.easycloud.jxmcc.efinance.df.claim.service.impl;

import cn.hutool.core.util.StrUtil;
import com.easycloud.jxmcc.efinance.df.claim.service.IClaimDFEvent;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Service
@Slf4j
public class ClaimEventDFService {
    private Map<String, IClaimDFEvent> claimEventMap = new HashMap();

    public ClaimEventDFService(ObjectProvider<IClaimDFEvent> objectProvider) {
        Iterator var2 = objectProvider.iterator();

        while(var2.hasNext()) {
            IClaimDFEvent claimEvent =(IClaimDFEvent) var2.next();
            if(StrUtil.isBlank(claimEvent.itemId())){
                throw new RuntimeException(claimEvent.getClass().getName()+"报账单Id为空");
            }
            if(claimEventMap.containsKey(claimEvent.itemId())){
                throw new RuntimeException("已经存在"+claimEvent.itemId()+"报账单事件接口");
            }
            this.claimEventMap.put(claimEvent.itemId(),claimEvent);
        }
    }

    public void deleteClaim(FtRmbsClaimDf ftRmbsClaimDf) {
        if(!claimEventMap.containsKey(ftRmbsClaimDf.getItemId())){
            log.error(ftRmbsClaimDf.getItemId()+"事件接口不存在");
            return;
        }
        claimEventMap.get(ftRmbsClaimDf.getItemId()).deleteClaim(ftRmbsClaimDf);
    }
}
