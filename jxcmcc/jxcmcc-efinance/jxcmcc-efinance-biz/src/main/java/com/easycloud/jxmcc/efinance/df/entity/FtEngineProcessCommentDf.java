/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 老党费审批记录表
 *
 * <AUTHOR> code generator
 * @date 2023-02-16 17:17:09
 */
@Data
@TableName("FT_ENGINE_PROCESS_COMMENT_DF")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "老党费审批记录表")
public class FtEngineProcessCommentDf extends Model<FtEngineProcessCommentDf> {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private Integer id;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private Integer processInstanceid;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String stateId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String stateName;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String lable;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String content;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String drafterName;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String drafterId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private LocalDateTime createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String orderNum;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String deptName;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String deptId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private Integer activityInstanceid;

    @TableField(exist = false)
    private String businessId;
}
