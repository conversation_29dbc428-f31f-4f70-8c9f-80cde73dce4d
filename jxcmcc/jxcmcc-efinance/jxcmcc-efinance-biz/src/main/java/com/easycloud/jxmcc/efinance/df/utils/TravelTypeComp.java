package com.easycloud.jxmcc.efinance.df.utils;

import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.google.common.collect.Lists;
import lombok.experimental.UtilityClass;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 公司名称和编号转换
 */
@UtilityClass
public class TravelTypeComp {

    /**
     * 返回费用类型名称
     *
     * @return  String
     */
    public String getCompName(String travelTypeId) {
        String compName = null;
        switch (travelTypeId) {
            case "01": compName = "住宿费"; break;
            case "02": compName = "伙食费"; break;
            case "03": compName = "租车费"; break;
            case "04": compName = "城市间交通费"; break;
            case "05": compName = "师资费"; break;
            case "06": compName = "场地费"; break;
            case "07": compName = "资料费"; break;
            case "08": compName = "门票费"; break;
            case "09": compName = "讲解费"; break;
            default: compName = "其他"; break;
        }
        return compName;
    }

    /**
     * 返回费用类型ID
     *
     * @return  String
     */
    public String getCompId(String travelTypeName) {
        String compId = null;
        switch (travelTypeName) {
            case "住宿费": compId = "01"; break;
            case "伙食费": compId = "02"; break;
            case "租车费": compId = "03"; break;
            case "城市间交通费": compId = "04"; break;
            case "师资费": compId = "05"; break;
            case "场地费": compId = "06"; break;
            case "资料费": compId = "07"; break;
            case "门票费": compId = "08"; break;
            case "讲解费": compId = "09"; break;
            default: compId = "10"; break;
        }
        return compId;
    }

    /**
     * 返回费用类型所对应的单位
     *
     * @return  String
     */
    public String getC01(String travelTypeId) {
        String C01 = null;
        switch (travelTypeId) {
            case "01": C01 = "天"; break;
            case "02": C01 = "天"; break;
            case "03": C01 = "天"; break;
            case "04": C01 = ""; break;
            case "05": C01 = ""; break;
            case "06": C01 = "天"; break;
            case "07": C01 = ""; break;
            case "08": C01 = ""; break;
            case "09": C01 = ""; break;
            default: C01 = ""; break;
        }
        return C01;
    }

    /**
     * 返回费用类型所对应的单位
     *
     * @return  String
     */
    public String getC02(String travelTypeId) {
        String C02 = null;
        switch (travelTypeId) {
            case "01": C02 = "人"; break;
            case "02": C02 = "人"; break;
            case "03": C02 = "辆"; break;
            case "04": C02 = "人"; break;
            case "05": C02 = "课时"; break;
            case "06": C02 = "个场地"; break;
            case "07": C02 = "人"; break;
            case "08": C02 = "人"; break;
            case "09": C02 = "次"; break;
            default: C02 = ""; break;
        }
        return C02;
    }

    /**
     * 返回费用类型所对应的单位
     *
     * @return  String
     */
    public String getC03(String travelTypeId) {
        String C03 = null;
        switch (travelTypeId) {
            case "01": C03 = "元/人/天"; break;
            case "02": C03 = "元/人/天"; break;
            case "03": C03 = "元/辆/天"; break;
            case "04": C03 = "元/人"; break;
            case "05": C03 = "元/课时"; break;
            case "06": C03 = "元/个场地/天"; break;
            case "07": C03 = "元/人"; break;
            case "08": C03 = "元/人"; break;
            case "09": C03 = "元/次"; break;
            default: C03 = ""; break;
        }
        return C03;
    }

}
