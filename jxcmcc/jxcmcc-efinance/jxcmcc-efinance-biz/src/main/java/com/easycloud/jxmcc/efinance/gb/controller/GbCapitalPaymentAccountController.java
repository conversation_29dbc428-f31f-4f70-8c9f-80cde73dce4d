/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.util.WebUtils;
import com.easycloud.jxcmcc.common.data.builder.QueryWrapperBuilder;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.efinance.gb.entity.GbCapitalPaymentAccount;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfo;
import com.easycloud.jxmcc.efinance.gb.service.GbCapitalPaymentAccountService;
import com.easycloud.jxmcc.efinance.task.GbCapitalPaymentAccountTaskServiceImpl;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.File;
import java.io.FileOutputStream;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 国拨资金支付台账
 *
 * <AUTHOR> code efinance
 * @date 2022-10-27 09:43:07
 */
@RestController
@AllArgsConstructor
@RequestMapping("/gbcapitalpaymentaccount" )
@Api(value = "gbcapitalpaymentaccount", tags = "国拨资金支付台账管理")
public class GbCapitalPaymentAccountController {

    private final GbCapitalPaymentAccountService gbCapitalPaymentAccountService;
    private final GbCapitalPaymentAccountTaskServiceImpl gbCapitalPaymentAccountTaskService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param gbCapitalPaymentAccount 国拨资金支付台账
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询国拨资金支付台账" )
    @GetMapping("/page" )
    public R getGbCapitalPaymentAccountPage(Page page, GbCapitalPaymentAccount gbCapitalPaymentAccount) {
        return R.ok(gbCapitalPaymentAccountService.selectGbCapitalPaymentAccountByPage(page, gbCapitalPaymentAccount));
    }


    /**
     * 通过id查询国拨资金支付台账
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询国拨资金支付台账" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(gbCapitalPaymentAccountService.getById(id));
    }

    /**
     * 新增国拨资金支付台账
     * @param gbCapitalPaymentAccount 国拨资金支付台账
     * @return R
     */
    @ApiOperation(value = "新增国拨资金支付台账", notes = "新增国拨资金支付台账")
    @SysLog("新增国拨资金支付台账" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('efinance_gbcapitalpaymentaccount_add')" )
    public R save(@RequestBody GbCapitalPaymentAccount gbCapitalPaymentAccount) {
        return R.ok(gbCapitalPaymentAccountService.save(gbCapitalPaymentAccount));
    }

    /**
     * 修改国拨资金支付台账
     * @param gbCapitalPaymentAccount 国拨资金支付台账
     * @return R
     */
    @ApiOperation(value = "修改国拨资金支付台账", notes = "修改国拨资金支付台账")
    @SysLog("修改国拨资金支付台账" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('efinance_gbcapitalpaymentaccount_edit')" )
    public R updateById(@RequestBody GbCapitalPaymentAccount gbCapitalPaymentAccount) {
        return R.ok(gbCapitalPaymentAccountService.updateById(gbCapitalPaymentAccount));
    }

    /**
     * 通过id删除国拨资金支付台账
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除国拨资金支付台账", notes = "通过id删除国拨资金支付台账")
    @SysLog("通过id删除国拨资金支付台账" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('efinance_gbcapitalpaymentaccount_del')" )
    public R removeById(@PathVariable String id) {
        return R.ok(gbCapitalPaymentAccountService.removeById(id));
    }


    /**
     * 导入国拨资金支付台账
     * @return R
     */
    @ApiOperation(value = "导入国拨资金支付台账", notes = "导入国拨资金支付台账")
    @SysLog("导入国拨资金支付台账" )
    @PreAuthorize("@pms.hasPermission('efinance_gbcapitalpaymentaccount_import')" )
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return gbCapitalPaymentAccountService.importExcel(file,importParams);
    }

    /**
     * 导出国拨资金支付台账
     * @return R
     */
    @ApiOperation(value = "导出国拨资金支付台账", notes = "导出国拨资金支付台账")
    @SysLog("导出国拨资金支付台账")
    @PreAuthorize("@pms.hasPermission('efinance_gbcapitalpaymentaccount_export')")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, GbCapitalPaymentAccount gbCapitalPaymentAccount) {
        Page page = new Page();
        page.setSize(-1);
        IPage<GbCapitalPaymentAccount> iPage = gbCapitalPaymentAccountService.selectGbCapitalPaymentAccountByPage(page, gbCapitalPaymentAccount);
        List<GbCapitalPaymentAccount> gbCapitalPaymentAccountList = iPage.getRecords();
        List<GbCapitalPaymentAccount> newList = new ArrayList<>();
        A:
        for (int i = 0; i < gbCapitalPaymentAccountList.size(); i++) {
            GbCapitalPaymentAccount item = gbCapitalPaymentAccountList.get(i);
            if (null == item.getChildren() || item.getChildren().size() < 1) {
                newList.add(item);
                continue A;
            }
            B:
            for (GbCapitalPaymentAccount temp : item.getChildren()) {
                if (null == temp.getChildren() || temp.getChildren().size() < 1) {
                    newList.add(temp);
                    continue B;
                }
                C:
                for (GbCapitalPaymentAccount temp2 : temp.getChildren()) {
                    newList.add(temp2);
                }
            }
        }
        // 计算跨行
        String[] fieldArr = new String[]{"projectNo", "projectTotalAmount", "usedTotalAmount", "surplusTotalAmount", "branchCompany", "projectManager", "branchAmount", "branchUsedAmount", "branchSurplusAmount"};
        Integer pos = 0;

        Map<String, List<Integer>> spanMap = new HashMap<>();
        // 根据值是否相同来判断是否需要跨行
        for (int k = 0; k < fieldArr.length; k++) {
            List<Integer> spanList = new ArrayList<>();
            for (int i = 0; i < newList.size(); i++) {

                GbCapitalPaymentAccount item = newList.get(i);
                if (i == 0) {
                    spanList.add(1);
                    pos = 0;
                } else {
                    if (getSpanResult(item, newList.get(i - 1), k, fieldArr)) {
                        spanList.set(pos, spanList.get(pos) + 1);
                        spanList.add(0);
                    } else {
                        spanList.add(1);
                        pos = i;
                    }
                }
            }
            spanMap.put(fieldArr[k], spanList);
        }
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("国拨资金支付台账列表", "国拨资金支付台账"), GbCapitalPaymentAccount.class, newList);
            Sheet sheet = workbook.getSheet("国拨资金支付台账");
            List<int[]> rowStartEnd = new ArrayList<>();
            List<Integer> projectNo = spanMap.get("projectNo");
            List<Integer> branchCompanys = spanMap.get("branchCompany");
            List<Integer> span = new ArrayList<>();
            List<Integer> spanCompanys = new ArrayList<>();
            // 【项目编号 ~ 剩余总额度（元）】的跨行处理
            for (int i = 0; i < projectNo.size(); i++) {
                // 初始化起始行以及终止行
                int start = i + 1;
                int end = start;
                for (int j = i + 1; j < projectNo.size(); j++) {
                    Integer endIndex = projectNo.get(j);
                    //判断跨行的标识位是否为0，如果为0则需要跨行
                    if (endIndex <= 0) {
                        end++;
                        // 如果当前行为最后一行则直接添加
                        if ( j == projectNo.size() -1) {
                            span.add(start);
                            span.add(end);
                            // 直接退出循环
                            i = j;
                        }
                    } else {
                        span.add(start);
                        span.add(end);
                        i = j - 1;
                        break;
                    }
                }
            }
            //  "branchCompany", "projectManager", "branchAmount", "branchUsedAmount", "branchSurplusAmount" 跨行
            for (int i = 0; i < branchCompanys.size(); i++) {
                // 初始化起始行以及终止行
                int start = i + 1;
                int end = start;
                for (int j = i + 1; j < branchCompanys.size(); j++) {
                    Integer endIndex = branchCompanys.get(j);
                    //判断跨行的标识位是否为0，如果为0则需要跨行
                    if (endIndex <= 0) {
                        end++;
                        if ( j == branchCompanys.size() -1) {
                            spanCompanys.add(start);
                            spanCompanys.add(end);
                            // 直接退出循环
                            i = j;
                        }
                    } else {
                        spanCompanys.add(start);
                        spanCompanys.add(end);
                        // 将i回退到新的跨行起始行
                        i = j - 1;
                        break;
                    }
                }
            }
            for (int i = 0; i < span.size() - 1; ) {
                // 当起始行和终止行不一致时就进行跨行
                if (!Objects.equals(span.get(i), span.get(i + 1))) {
                    for (int j = 0; j <= 3; j++) {
                        // 跨行参数，起始行，终止行，起始列，终止列
                        CellRangeAddress cellAddresses = new CellRangeAddress(span.get(i) + 1, span.get(i + 1) + 1, j, j);
                        sheet.addMergedRegion(cellAddresses);
                    }
                }
                i = i + 2;
            }
            for (int i = 0; i < spanCompanys.size() - 1; ) {
                if (!Objects.equals(spanCompanys.get(i), spanCompanys.get(i + 1))) {
                    for (int j = 4; j <= 8; j++) {
                        CellRangeAddress cellAddresses = new CellRangeAddress(spanCompanys.get(i) + 1, spanCompanys.get(i + 1) + 1, j, j);
                        sheet.addMergedRegion(cellAddresses);
                    }
                }
                i = i + 2;
            }
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 判断字段是否相等
     * @param item1
     * @param item2
     * @param index
     * @param fieldArr
     * @return
     */
    public boolean getSpanResult(GbCapitalPaymentAccount item1, GbCapitalPaymentAccount item2, int index, String[] fieldArr){
        boolean flag = true;
        for(int k = 0; k < fieldArr.length; k++){
            String fieldCode = fieldArr[k];
            if(k > index){
                break;
            }
            try {
                String newField = "get" + fieldCode.substring(0, 1).toUpperCase() + fieldCode.substring(1, fieldCode.length());
                Class clazz1 = item1.getClass();
                Method method1 = clazz1.getDeclaredMethod(newField);
                Object field1Value = method1.invoke(item1);

                Class clazz2 = item2.getClass();
                Method method2 = clazz2.getDeclaredMethod(newField);
                Object field2Value = method2.invoke(item2);

                String fieldStr1 = null == field1Value ? "" : String.valueOf(field1Value);
                String fieldStr2 = null == field2Value ? "" : String.valueOf(field2Value);
                if(!fieldStr1.equals(fieldStr2)){
                    flag = false;
                }
            } catch (Exception e) {
                e.printStackTrace();
                flag = false;
            }
        }
        return flag;
    }

    /**
     * 导出国拨资金支付台账导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版国拨资金支付台账文件", notes = "导出导入模版国拨资金支付台账文件")
    @SysLog("导出国拨资金支付台账导入模版文件")
    @PreAuthorize("@pms.hasPermission('efinance_gbcapitalpaymentaccount_export')")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/gbCapitalPaymentAccount.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成国拨资金支付台账
     * @return R
     */
    @ApiOperation(value = "生成国拨资金支付台账", notes = "生成国拨资金支付台账")
    @SysLog("生成国拨资金支付台账")
    @GetMapping("/generateGbCapitalPaymentAccount")
    public R generateGbCapitalPaymentAccount() throws Exception {
//        return gbCapitalPaymentAccountService.generateGbCapitalPaymentAccount();
        gbCapitalPaymentAccountTaskService.execute();
        return R.ok();
    }

}
