/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 党费系统报账头信息
 *
 * <AUTHOR>
 * @date 2022-03-28 15:00:54
 */
@Data
@TableName("FT_RMBS_CLAIM_DF")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "党费系统报账头信息")
public class FtRmbsClaimDf extends Model<FtRmbsClaimDf> {
private static final long serialVersionUID = 1L;

    /**
     * ;
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String claimId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String claimNo;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String assClaimId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String assClaimNo;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String applyComId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String applyComName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String applyDeptId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String applyDeptName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String applyUserId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String applyUserName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String feeUserId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String feeUserName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal feeAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String bankAccountId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String applyUserEmail;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String applyUserTel;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String itemId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String itemName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String item2Id;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String item2Name;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String item3Id;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String item3Name;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apBandSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String apBandSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal applyAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal payAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal invoiceAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String budgetSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String payReceiveMethod;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String bankAccountName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String payTypeId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String payTypeName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer attachNumber;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String currency;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String isPriority;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String payObject;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String isSelfPay;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String applyReason;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer vendorClientId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorNo;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorRealName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorBank;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorAccountNo;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorAccountName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer vendorSiteId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorSiteCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorTypeLookupCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String relatedTradeTypeName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String relatedTradeType;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String projectName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String projectMisNo;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal contractAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String contractName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String contractNo;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal prepayVerifyAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal prepayTobeverifyAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Long batchId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String batchNo;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime submitToAcDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime misProcDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime misProcGlDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime payDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String payStatus;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String isMisInvoiceProc;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime arDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String isMisJournalProc;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String processLabel;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String processVersion;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String processInstanceId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Long processStateId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String processState;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String processStateEng;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String matierialStatus;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String matierialPos;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String matierialPosCom;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String matierialPosDept;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String matierialPosTel;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String curActorId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String curActorName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime curReceiveDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String actorsList;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String isSubmit;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer isRetrieve;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String retrieveReason;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String preAcId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String preAcName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String preProAcId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String preProAcName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String acId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String acName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String acEmployeeNo;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String isPause;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String pauseReason;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String misProcHeadDescription;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String invoiceType;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String auditDeptIds;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String auditDeptNames;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String auditUserIds;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String auditUserNames;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String custTrxTypeId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String custTrxTypeName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String arReceivablesTrxName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String arReceivablesTrxId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String liabilityAccount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String prepayAccount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String summary;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String remark;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String polist;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String assetsAccount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String reason;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String fromCom;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String toCom;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime lossDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String lossName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal disposalIncome;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal disposalCost;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal disposalNetPl;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String singleProject;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String outBack;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String outSubBack;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String outAccount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String inSubBack;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String inAccount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime applySumDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String inBack;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String c01;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String c02;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String c03;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String c04;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String c05;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer i01;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer i02;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer i03;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer i04;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer i05;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal f01;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal f02;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal f03;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal f04;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal f05;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime d01;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime d02;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime d03;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime t01;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime t02;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Long orgId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String orgName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String setOfBooksId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String setOfBooks;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String coSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String coSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String payStatusChangable;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String costSegCode;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String costSeg;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private Integer printNum;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String status;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String statusName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String erpImportStatus;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String erpImportStatusName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String bankType;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String bankLocProvince;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String bankLocCity;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal contractListAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal contractInvoiceAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal contractPayAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String applyUserNumber;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorCategory;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String contractExist;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String contractLink;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String fywdsx;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String fywdsxName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String isHasVoucher;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String vendorTaxQuality;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal priceAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String contractPayProof;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal inputTaxAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String voucherCategory;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String voucherCategoryName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String invoiceAuthState;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String importRinvStatus;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal outputTaxAmount;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal outputTaxRate;


    @TableField(exist = false)
    @Excel(name = ";")
    private Date endDate;

    @TableField(exist = false)
    @Excel(name = ";")
    private Date endTime;

    @TableField(exist = false)
    @Excel(name = ";")
    private String title;
    }
