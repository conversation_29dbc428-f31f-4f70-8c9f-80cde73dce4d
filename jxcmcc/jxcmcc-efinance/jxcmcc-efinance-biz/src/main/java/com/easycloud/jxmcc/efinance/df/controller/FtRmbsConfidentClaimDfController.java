/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.controller;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsConfidentClaimDf;
import com.easycloud.jxmcc.efinance.df.mapper.FtRmbsConfidentClaimDfMapper;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsConfidentClaimDfService;
import com.easycloud.jxmcc.efinance.vo.ClaimLineVo;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;


/**
 * 记账凭证
 *
 * <AUTHOR>
 * @date 2022-08-05 16:07:57
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ftrmbsconfidentclaimdf" )
@Api(value = "ftrmbsconfidentclaimdf", tags = "记账凭证管理")
public class FtRmbsConfidentClaimDfController {

    private final  FtRmbsConfidentClaimDfService ftRmbsConfidentClaimDfService;


    /**
     * 分页查询
     * @param page 分页对象
     * @param ftRmbsConfidentClaimDf 记账凭证
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询记账凭证" )
    @GetMapping("/page" )
    public R getFtRmbsConfidentClaimDfPage(Page page, FtRmbsConfidentClaimDf ftRmbsConfidentClaimDf) {
        return R.ok(ftRmbsConfidentClaimDfService.page(page, Wrappers.query(ftRmbsConfidentClaimDf)));
    }


    /**
     * 通过id查询记账凭证
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询记账凭证" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(ftRmbsConfidentClaimDfService.getById(id));
    }

    /**
     * 新增记账凭证
     * @param ftRmbsConfidentClaimDf 记账凭证
     * @return R
     */
    @ApiOperation(value = "新增记账凭证", notes = "新增记账凭证")
    @SysLog("新增记账凭证" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('df_ftrmbsconfidentclaimdf_add')" )
    public R save(@RequestBody FtRmbsConfidentClaimDf ftRmbsConfidentClaimDf) {
        return R.ok(ftRmbsConfidentClaimDfService.save(ftRmbsConfidentClaimDf));
    }

    /**
     * 通过id查询党费系统报账头信息
     * @param claimId id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询党费系统报账头信息" )
    @GetMapping("/setConfidentCode/{claimId}" )
    public R setConfidentCode(@PathVariable("claimId" ) String claimId) {
        ftRmbsConfidentClaimDfService.updateByClaimId(claimId);
        return R.ok();
    }

    /**
     * 通过id初始化凭证编号
     * @param claimId id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询党费系统报账头信息" )
    @GetMapping("/updateCode/{claimId}" )
    public R updateCode(@PathVariable("claimId" ) String claimId) {
        ftRmbsConfidentClaimDfService.updateCode(claimId);
        return R.ok();
    }


    /**
     * 通过claimId查询记账凭证记录总数
     * @param claimId id
     * @return R
     */
    @ApiOperation(value = "通过claimId查询记账凭证记录总数", notes = "通过claimId查询记账凭证记录总数")
    @SysLog("通过claimId查询记账凭证记录总数" )
    @GetMapping("/getCount/{claimId}" )
    public R getCount(@PathVariable("claimId" ) String claimId) {
        return R.ok(ftRmbsConfidentClaimDfService.count(new QueryWrapper<FtRmbsConfidentClaimDf>().eq("CLAIM_ID",claimId)));
    }


    /**
     * 删除记账凭证行
     * @return R
     */
    @ApiOperation(value = "删除记账凭证行", notes = "删除记账凭证行")
    @SysLog(value = "删除记账凭证行",demandId="xq_common")
    @PostMapping("/deleteConfident")
    public R deleteLine(@RequestBody ClaimLineVo claimLineVo) {
        return R.ok(ftRmbsConfidentClaimDfService.deleteConfident(claimLineVo));
    }


    /**
     * 通过claimId查询党费系统支付明细
     * @param claimId id
     * @return R
     */
    @ApiOperation(value = "通过claimId查询", notes = "通过claimId查询")
    @SysLog("通过id查询党费系统支付明细" )
    @GetMapping("/getById/{claimId}" )
    public R getByCliamId(@PathVariable("claimId") String claimId) {
        return R.ok(ftRmbsConfidentClaimDfService.getByClaimId(claimId));
    }


    /**
     * 修改记账凭证
     * @param ftRmbsConfidentClaimDf 记账凭证
     * @return R
     */
    @ApiOperation(value = "修改记账凭证", notes = "修改记账凭证")
    @SysLog("修改记账凭证" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('df_ftrmbsconfidentclaimdf_edit')" )
    public R updateById(@RequestBody FtRmbsConfidentClaimDf ftRmbsConfidentClaimDf) {
        return R.ok(ftRmbsConfidentClaimDfService.updateById(ftRmbsConfidentClaimDf));
    }

    /**
     * 通过id删除记账凭证
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除记账凭证", notes = "通过id删除记账凭证")
    @SysLog("通过id删除记账凭证" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('df_ftrmbsconfidentclaimdf_del')" )
    public R removeById(@PathVariable String id) {
        return R.ok(ftRmbsConfidentClaimDfService.removeById(id));
    }


   /**
     * 导入记账凭证
     * @return R
     */
    @ApiOperation(value = "导入记账凭证", notes = "导入记账凭证")
    @SysLog("导入记账凭证" )
    @PreAuthorize("@pms.hasPermission('df_ftrmbsconfidentclaimdf_import')" )
    @FileImportLogAnnotation(busTable = "记账凭证",busSystem = "df")
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return ftRmbsConfidentClaimDfService.importExcel(file,importParams);
    }

    /**
     * 导出记账凭证
     * @return R
     */
    @ApiOperation(value = "导出记账凭证", notes = "导出记账凭证")
    @SysLog("导出记账凭证")
    @PreAuthorize("@pms.hasPermission('df_ftrmbsconfidentclaimdf_export')")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, FtRmbsConfidentClaimDf ftRmbsConfidentClaimDf) {
        List<FtRmbsConfidentClaimDf>  ftRmbsConfidentClaimDfList = ftRmbsConfidentClaimDfService.list(Wrappers.lambdaQuery(ftRmbsConfidentClaimDf));
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("记账凭证列表", "记账凭证"),
                FtRmbsConfidentClaimDf. class,ftRmbsConfidentClaimDfList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出记账凭证导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版记账凭证文件", notes = "导出导入模版记账凭证文件")
    @SysLog("导出记账凭证导入模版文件")
    @PreAuthorize("@pms.hasPermission('df_ftrmbsconfidentclaimdf_export')")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/ftRmbsConfidentClaimDf.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
