/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.gb.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.DateUtils;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxmcc.efinance.entity.FtCmccInterfaceLog;
import com.easycloud.jxmcc.efinance.gb.entity.GbAssetDetails;
import com.easycloud.jxmcc.efinance.gb.mapper.GbAssetDetailsMapper;
import com.easycloud.jxmcc.efinance.gb.service.GbAssetDetailsService;
import com.easycloud.jxmcc.efinance.service.FtCmccInterfaceLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 资产明细表
 *
 * <AUTHOR> code generator
 * @date 2022-10-25 10:08:23
 */
@Service
@Slf4j
public class GbAssetDetailsServiceImpl extends ServiceImpl<GbAssetDetailsMapper, GbAssetDetails> implements GbAssetDetailsService {

    @Autowired
    private FtCmccInterfaceLogService ftCmccInterfaceLogService;

    /**
    * 导入excel
    * @return
    */
    @Override
    public R<Integer> importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！" );
        }
        ImportParams params = new ImportParams();
        //解析导入EXCEL数据
        List<GbAssetDetails> gbAssetDetailsList = ExcelImportUtil.importExcel(file.getInputStream(), GbAssetDetails. class,
        params);
        this.saveBatch(gbAssetDetailsList);
        return R.ok(gbAssetDetailsList.size());
    }

    /**
     * 资产明细申请数据
     * @param jsonObject
     * @return
     */
    @Override
    @Transactional
    public R handleGbAssetDetailsData(JSONObject jsonObject) {
        //判断数据是单个对象还是数组
        List<GbAssetDetails> gbAssetDetailsList = new ArrayList<>();
        if(jsonObject.get("data") instanceof ArrayList){
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            gbAssetDetailsList = jsonArray.toJavaList(GbAssetDetails.class);
        }else if(jsonObject.get("data") instanceof LinkedHashMap){
            String JSONStr = JSON.toJSONString(jsonObject.get("data"));
            GbAssetDetails data = JSON.parseObject(JSONStr, GbAssetDetails.class);
            gbAssetDetailsList.add(data);
        }

        for (GbAssetDetails gbAssetDetails : gbAssetDetailsList) {
            gbAssetDetails.setDeleteFlag("0");
            if (null != gbAssetDetails.getIsRetirements() && gbAssetDetails.getIsRetirements().equals("是")){
                gbAssetDetails.setIsRetirements("Y");
            }else{
                gbAssetDetails.setIsRetirements("N");
            }
        }

        FtCmccInterfaceLog interfaceLog = new FtCmccInterfaceLog();
        interfaceLog.setCreationDate(new Date());
        interfaceLog.setInterfaceKey("GbAssetDetailsServiceImpl");
        interfaceLog.setInterfaceName("资产明细申请数据");
        interfaceLog.setStartLastUpdate(new Date());
        interfaceLog.setEndLastUpdate(new Date());
        interfaceLog.setSyncResult("成功");
        ftCmccInterfaceLogService.save(interfaceLog);
        return R.ok(null,"接收资产明细数据成功");
    }

}
