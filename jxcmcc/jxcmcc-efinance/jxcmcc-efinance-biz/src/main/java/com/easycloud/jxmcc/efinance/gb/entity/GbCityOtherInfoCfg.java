/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 地市其它信息配置
 *
 * <AUTHOR> code generator
 * @date 2022-10-19 10:27:16
 */
@Data
@TableName("GB_CITY_OTHER_INFO_CFG")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "地市其它信息配置")
public class GbCityOtherInfoCfg extends Model<GbCityOtherInfoCfg> {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "唯一标识")
    private String id;
    /**
     * 公司编码
     */
    @ApiModelProperty(value = "公司编码")
    @Excel(name = "公司编码",width = 20)
    private String companyCode;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @Excel(name = "公司名称",width = 20)
    private String companyName;
    /**
     * 财务人员用户名
     */
    @ApiModelProperty(value = "财务人员用户名")
    @Excel(name = "财务人员用户名",width = 30)
    private String financialStaffNo;
    /**
     * 财务人员姓名
     */
    @ApiModelProperty(value = "财务人员姓名")
    @Excel(name = "财务人员姓名",width = 30)
    private String financialStaffName;
    /**
     * 工行集中账号
     */
    @ApiModelProperty(value = "工行集中账号")
    @Excel(name = "工行集中账号",width = 30)
    private String ghFocusNo;
    /**
     * 工行集中账号名称
     */
    @ApiModelProperty(value = "工行集中账号名称")
    @Excel(name = "工行集中账号名称",width = 35)
    private String ghFocusName;
    /**
     * 租户号
     */
    @ApiModelProperty(value = "租户号")
    private Integer tenantId;
    /**
     * 删除标识;删除标识(0=否,1=是)
     */
    @ApiModelProperty(value = "删除标识;删除标识(0=否,1=是)")
    private String deleteFlag;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedTime;
}
