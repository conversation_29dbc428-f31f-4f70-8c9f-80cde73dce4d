/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.vo.PartyOrgNameVo;
import com.easycloud.jxmcc.efinance.df.vo.StatisticsDfVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 党费系统报账头信息
 *
 * <AUTHOR>
 * @date 2022-03-28 15:00:54
 */
@Mapper
public interface FtRmbsClaimDfMapper extends BaseMapper<FtRmbsClaimDf> {

    /**
     * 查询党组织列表
     * @param page
     * @param partyOrgNameVo
     * @return
     */
    Page<PartyOrgNameVo> getPartyOrgNameList(Page page, @Param("param") PartyOrgNameVo partyOrgNameVo);



    /**
     * 查询党组织所有列表
     * @param page
     * @param partyOrgNameVo
     * @return
     */
    Page<PartyOrgNameVo> getPartyOrgNameListAll(Page page, @Param("param") PartyOrgNameVo partyOrgNameVo,@Param("deptId") String deptId);

    /**
     * 审批单列表
     * @param page
     * @param ftRmbsClaimDf
     * @return
     */
    Page<FtRmbsClaimDf> getProjectMisNo(Page page, @Param("param") FtRmbsClaimDf ftRmbsClaimDf);

    /**
     * 原报账单号列表
     * @param page
     * @param ftRmbsClaimDf
     * @return
     */
    Page<FtRmbsClaimDf> getPayClaimNoList(Page page, @Param("param") FtRmbsClaimDf ftRmbsClaimDf);

    /**
     * 党组织已报账列表
     * @param page
     * @param partyOrgNameVo
     * @return
     */
    Page<FtRmbsClaimDf> getPartyClaim(Page page, @Param("param") PartyOrgNameVo partyOrgNameVo);

    /**
     * 党组织已报账所有年份列表
     * @param page
     * @param partyOrgNameVo
     * @return
     */
    Page<FtRmbsClaimDf> getPartyClaimAll(Page page, @Param("param") PartyOrgNameVo partyOrgNameVo);

    /**
     * 党组织已审批未报账列表
     * @param page
     * @param partyOrgNameVo
     * @return
     */
    Page<FtRmbsClaimDf> getPartyClaimT002DF(Page page, @Param("param") PartyOrgNameVo partyOrgNameVo);


    /**
     * 党组织已审批未报账所有年份列表
     * @param page
     * @param partyOrgNameVo
     * @return
     */
    Page<FtRmbsClaimDf> getPartyClaimT002DFAll(Page page, @Param("param") PartyOrgNameVo partyOrgNameVo);


    /**
     * 我的已办分页查询
     * @param page
     * @param paramMap
     * @return
     */
    Page<FtRmbsClaimDf> getPage(Page page, @Param("entity")Map<String, String> paramMap);



    /**
     * 报账单管理/已归档分页查询报账单信息
     * @param page
     * @param paramMap
     * @return
     */
    Page<FtRmbsClaimDf> selectFtRmbsDfClaimListByPage(Page page, @Param("paramMap") Map<String,String> paramMap);

    /**
     * 党费报账统计
     * @param page
     * @param paramMap
     * @param deptId
     * @return
     */
    Page<StatisticsDfVo> getStatisticsPage(Page page, @Param("entity") Map<String, String> paramMap, @Param("deptId") String deptId);
}
