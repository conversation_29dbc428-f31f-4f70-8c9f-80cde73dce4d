/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.gb.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;

import com.easycloud.jxmcc.efinance.entity.FtCmccClaim;
import com.easycloud.jxmcc.efinance.entity.FtCmccClaimGroup;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectClaimLog;
import com.easycloud.jxmcc.efinance.gb.mapper.GbProjectClaimLogMapper;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectClaimLogService;
import com.easycloud.jxmcc.efinance.service.FtCmccClaimGroupService;
import com.easycloud.jxmcc.efinance.service.FtCmccClaimService;
import com.easycloud.jxmcc.efinance.synchrodata.entity.CmfClmClaimTypeCollocation;
import com.easycloud.jxmcc.efinance.synchrodata.mapper.CmfClmClaimTypeCollocationMapper;
import com.easycloud.jxmcc.efinance.synchrodata.service.InqueryClaimService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.easycloud.jxcmcc.common.core.util.R;
import org.springframework.web.multipart.MultipartFile;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 国拨项目报帐日志表
 *
 * <AUTHOR> code generator
 * @date 2022-10-24 09:21:00
 */
@Service
@Slf4j
public class GbProjectClaimLogServiceImpl extends ServiceImpl<GbProjectClaimLogMapper, GbProjectClaimLog> implements GbProjectClaimLogService {

    @Autowired
    private FtCmccClaimService ftCmccClaimService;
    @Autowired
    private FtCmccClaimGroupService ftCmccClaimGroupService;
    @Autowired
    private InqueryClaimService inqueryClaimService;
    @Autowired
    private CmfClmClaimTypeCollocationMapper cmfClmClaimTypeCollocationMapper;
    @Autowired
    private GbProjectClaimLogMapper gbProjectClaimLogMapper;
    /**
    * 导入excel
    * @return
    */
    @Override
    public R<Integer> importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！" );
        }
        ImportParams params = new ImportParams();
        //解析导入EXCEL数据
        List<GbProjectClaimLog> gbProjectClaimLogList = ExcelImportUtil.importExcel(file.getInputStream(), GbProjectClaimLog. class,
        params);
        this.saveBatch(gbProjectClaimLogList);
        return R.ok(gbProjectClaimLogList.size());
    }

    /**
     * 同步集团报账单号
     *
     * @param ids
     * @return
     */
    @Override
    public R syncClaimNo(List<String> ids) {
        List<GbProjectClaimLog> gbProjectClaimLogList = gbProjectClaimLogMapper.selGroupClaimNo(ids);
        this.updateBatchById(gbProjectClaimLogList);
        return R.ok();
//        GbProjectClaimLog gbProjectClaimLog = this.getById(id);
//        if (StringUtils.isNotBlank(gbProjectClaimLog.getGroupClaimNo())){
//            return R.failed("已同步集团报账单号无需同步！");
//        }else{
//            //获取报账表头数据
//            FtCmccClaim ftCmccClaim = ftCmccClaimService.getOne(Wrappers.lambdaQuery(FtCmccClaim.class)
//                    .eq(FtCmccClaim::getClaimNo,gbProjectClaimLog.getClaimNo()));
//            if("step0".equals(ftCmccClaim.getStateCode())){
//                String msg;
//                try {
//                    Map<String,Object> map = new HashMap<String, Object>();
//                    List<CmfClmClaimTypeCollocation> list=cmfClmClaimTypeCollocationMapper.getAllClaimType();
//                    Map<String,String> typeMap = list.stream().collect(Collectors.toMap(CmfClmClaimTypeCollocation::getClaimTypeName, CmfClmClaimTypeCollocation::getClaimTypeId));
//                    if("T0132".equals(gbProjectClaimLog.getClaimType())){
//                        map.put("claimTypeId", typeMap.get("工程类采购订单报账单"));
//                    }else if("T0133".equals(gbProjectClaimLog.getClaimType())){
//                        map.put("claimTypeId", typeMap.get("工程类支付申请报账单"));
//                    }
//                    map.put("accurateClaimNum", gbProjectClaimLog.getClaimNo());
//                    map.put("statusCode","COMPLETED");
//                    map.put("page", 1);
//                    inqueryClaimService.getAllClaimEndAndSave(map);
//                    FtCmccClaimGroup ftCmccClaimGroup = ftCmccClaimGroupService.getOne(Wrappers.lambdaQuery(FtCmccClaimGroup.class)
//                                    .eq(FtCmccClaimGroup::getClaimNo,gbProjectClaimLog.getClaimNo()));
//                    gbProjectClaimLog.setGroupClaimNo(ftCmccClaimGroup.getGroupClaimNo());
//                    this.updateById(gbProjectClaimLog);
//                    msg="同步更新成功";
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    msg="同步更新失败";
//                }
//                return R.ok(msg);
//            }else{
//                return R.failed("该报账单未提交，不可同步！");
//            }
//        }
    }

}
