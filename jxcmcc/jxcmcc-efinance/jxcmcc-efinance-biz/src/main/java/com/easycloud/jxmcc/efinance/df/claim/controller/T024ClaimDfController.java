package com.easycloud.jxmcc.efinance.df.claim.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.efinance.df.claim.service.T024ClaimLineDfService;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimLineDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimLineDfService;
import com.easycloud.jxmcc.efinance.df.vo.claim.T002DFClaimLineVo;
import com.easycloud.jxmcc.efinance.df.vo.claim.T024DFClaimLineVo;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("t024claimdf")
@AllArgsConstructor
public class T024ClaimDfController {

    private final FtRmbsClaimLineDfService ftRmbsClaimLineDfService;

    private final T024ClaimLineDfService t024ClaimLineDfService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param t024DFClaimLineVo 党费系统报账行明细
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog(value = "分页查询党费系统报账行明细" ,demandId="xq_T024DF")
    @GetMapping("/page" )
    public R getFtRmbsClaimLinePage(Page page, T024DFClaimLineVo t024DFClaimLineVo) {
        return R.ok(t024ClaimLineDfService.page(page,t024DFClaimLineVo));
    }


    /**
     * 通过ID查询党费系统报账行明细
     * @param id
     * @return R
     */
    @ApiOperation(value = "通过ID查询", notes = "通过ID查询")
    @SysLog(value = "通过ID查询党费系统报账行明细", demandId ="xq_T024DF")
    @GetMapping("/{id}")
    public R getById(@PathVariable("id") String id) {
        return R.ok(t024ClaimLineDfService.getById(id));
    }


    /**
     * 导入党费收入明细
     * @return R
     */
    @ApiOperation(value = "导入党费收入明细", notes = "导入党费收入明细")
    @SysLog("导入党费收入明细" )
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, String claimId) throws Exception {
        return t024ClaimLineDfService.importExcel(file,claimId);
    }


    /**
     * 党费收入明细下载模板
     */
    @ApiOperation(value = "下载模板", notes = "下载模板")
    @SysLog(value = "下载模板" ,demandId = "xq_T024DF")
    @GetMapping("/downLoadImportTemplate")
    public void downLoadImportTemplate(HttpServletRequest request, HttpServletResponse response){
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/df/T024DfClaimLineTemplate.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }




    /**
     * 导出党费收入报账单明细
     * @return
     */
    @ApiOperation(value = "导出党费收入报账单明细", notes = "导出党费收入报账单明细")
    @SysLog(value = "导出党费收入报账单明细" ,demandId="xq_T024DF")
    @GetMapping(value = "/exportClaimLine")
    public void exportClaimLine(HttpServletResponse response, T024DFClaimLineVo t024DFClaimLineVo){
        if (StringUtils.isEmpty(t024DFClaimLineVo.getClaimId())){
            throw  new BootstrapMethodError("导出失败，请联系管理员");
        };
        List<FtRmbsClaimLineDf> exportList = ftRmbsClaimLineDfService.list(Wrappers.lambdaQuery(FtRmbsClaimLineDf.class)
                .eq(FtRmbsClaimLineDf::getClaimId, t024DFClaimLineVo.getClaimId()));

        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("党费收入报账明细", "明细数据"), T024DFClaimLineVo.class,
                    exportList.stream().map(x -> {
                        T024DFClaimLineVo vo = new T024DFClaimLineVo();
                        BeanUtil.copyProperties(x, vo);
                        return vo;
                    }).collect(Collectors.toList()));
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
            oss.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

}
