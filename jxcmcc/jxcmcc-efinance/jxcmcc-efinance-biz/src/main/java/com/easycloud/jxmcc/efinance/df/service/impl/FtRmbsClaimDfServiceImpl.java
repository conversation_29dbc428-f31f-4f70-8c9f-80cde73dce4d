/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.df.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.admin.api.entity.SysDept;
import com.easycloud.jxcmcc.admin.api.feign.RemoteEfinaceSysDeptService;
import com.easycloud.jxcmcc.admin.api.feign.RemoteUserService;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.exception.BusinessException;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.util.SpringContextUtils;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.efinance.df.claim.service.impl.ClaimEventDFService;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimLineDf;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsPaylistDf;
import com.easycloud.jxmcc.efinance.df.mapper.FtRmbsClaimDfMapper;
import com.easycloud.jxmcc.efinance.df.mapper.FtRmbsClaimLineDfMapper;
import com.easycloud.jxmcc.efinance.df.mapper.FtRmbsPaylistDfMapper;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.efinance.df.vo.PartyOrgNameVo;
import com.easycloud.jxmcc.efinance.df.vo.StatisticsDfVo;
import com.easycloud.jxmcc.efinance.entity.FtCmccClaim;
import com.easycloud.jxmcc.efinance.service.FtCmccClaimService;
import com.easycloud.jxmcc.efinance.util.PermissionUtil;
import com.easycloud.jxmcc.efinance.util.TCmccClaimUtil;
import com.easycloud.jxmcc.efinance.util.TimeUtils;
import com.easycloud.jxmcc.efinance.vo.UserRightsVo;
import com.easycloud.jxmcc.eflow.feign.RemoteEflowService;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 党费系统报账头信息
 *
 * <AUTHOR>
 * @date 2022-03-28 15:00:54
 */
@Service
@AllArgsConstructor
@Slf4j
public class FtRmbsClaimDfServiceImpl extends ServiceImpl<FtRmbsClaimDfMapper, FtRmbsClaimDf> implements FtRmbsClaimDfService {


    private final FtCmccClaimService cmccClaimService;
    private final FtRmbsClaimLineDfMapper rmbsClaimLineDfMapper;
    private final FtRmbsPaylistDfMapper rmbsPaylistDfMapper;
    private final RemoteEflowService remoteEflowService;
    private final RemoteUserService remoteUserService;
    private final RemoteEfinaceSysDeptService remoteEfinaceSysDeptService;

    @Override
    public Page<PartyOrgNameVo> getPartyOrgNameList(Page page, PartyOrgNameVo partyOrgNameVo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        Date date = new Date();
        partyOrgNameVo.setYear(sdf.format(date));
        return baseMapper.getPartyOrgNameList(page, partyOrgNameVo);
    }

    @Override
    public Page<PartyOrgNameVo> getPartyOrgNameListAll(Page page, PartyOrgNameVo partyOrgNameVo) {
        JxcmccUser jxcmccUser = SecurityUtils.getUser();

        //查全省
        if ("1".equals(this.getDfPermission(jxcmccUser))){
            return baseMapper.getPartyOrgNameListAll(page,partyOrgNameVo,null);
        }else {
            //查自己所在部门
            return baseMapper.getPartyOrgNameListAll(page,partyOrgNameVo,jxcmccUser.getDept().getSysOrgCode());
        }
    }

    @Override
    public FtRmbsClaimDf initializeClaim(FtRmbsClaimDf ftRmbsClaimDf) {
        JxcmccUser user = SecurityUtils.getUser();
        FtRmbsClaimDf tRmbsClaim = new FtRmbsClaimDf();
        Map<String, String> deptCodeMap = this.getDeptCode(user.getDept().getSysOrgCode());
        tRmbsClaim.setItemId(ftRmbsClaimDf.getItemId());//报账单类型
        tRmbsClaim.setItemName(ftRmbsClaimDf.getItemName());//报账人ID
        tRmbsClaim.setApplyUserId(user.getUsername());//报账人ID
        tRmbsClaim.setApplyUserName(user.getRealname());//报账人用户名
        tRmbsClaim.setApplyComId(user.getDept().getOrgCode());//报账人所在公司ID
        tRmbsClaim.setApplyComName(user.getDept().getSysCompanyName());//报账人所在公司名称
        tRmbsClaim.setApplyDeptId(user.getDept().getSysOrgCode());//报账人所在部门ID
        if (ObjectUtils.isNotEmpty(deptCodeMap)){
            tRmbsClaim.setApplyDeptName(deptCodeMap.get("deptName"));//报账人所在部门名称
        }else {
            tRmbsClaim.setApplyDeptName(user.getDept().getName());//报账人所在部门名称
        }
        tRmbsClaim.setApplyUserEmail(user.getUser().getEmail());//报账人邮箱
        tRmbsClaim.setApplyUserTel(user.getPhone());//报账人电话
        tRmbsClaim.setApplyDate(new Date());//报账日期
        tRmbsClaim.setStatus(TCmccClaimUtil.CLAIM_STATE_0);
        tRmbsClaim.setProcessStateEng(TCmccClaimUtil.CLAIM_STEP_10_CODE);
        tRmbsClaim.setProcessState(TCmccClaimUtil.CLAIM_STEP_10_NAME);
        tRmbsClaim.setPayAmount(ftRmbsClaimDf.getPayAmount());//付款金额
        tRmbsClaim.setApplyAmount(ftRmbsClaimDf.getApplyAmount());//报账金额
        tRmbsClaim.setPriceAmount(ftRmbsClaimDf.getPriceAmount());//价款

        return tRmbsClaim;
    }

    /**
     * 根据当前登录人获取对应的部门编码 如果是科室 获取上级部门编码
     * */
    public Map<String,String> getDeptCode(String deptCode) {
        try {
            Map<String,String> map = new HashMap<>();
            SysDept deptByDeptCode = remoteEfinaceSysDeptService.getDeptByDeptCode(deptCode);
            if(deptByDeptCode!=null){
                if("303710".equals(deptByDeptCode.getOrgCode())){
                    if(!deptByDeptCode.getParentSysOrgCode().equals("00370000000000000000")){
                        SysDept parentSysOrgDept = remoteEfinaceSysDeptService.getDeptByDeptCode(deptByDeptCode.getParentSysOrgCode());
                        map.put("deptCode",parentSysOrgDept.getSysOrgCode());
                        //获取上级部门名称
                        map.put("deptName",parentSysOrgDept.getName());
                        return map;
                    }
                }
                map.put("deptCode",deptByDeptCode.getSysOrgCode());
                map.put("deptName",deptByDeptCode.getName());
                return map;
            }
            return null;
        } catch (Exception e) {
            log.error("党费报账单头信息初始化部门失败，部门编码为：{}",deptCode);
            log.error("党费报账单头信息初始化部门失败",e);
            return null;
        }
    }
    @Override
    public FtRmbsClaimDf initializeClaim() {
        return initializeClaim(new FtRmbsClaimDf());
    }

    @Override
    public FtRmbsClaimDf initializeClaimNoLine(FtRmbsClaimDf ftRmbsClaimDf) {
        JxcmccUser user = SecurityUtils.getUser();
        FtRmbsClaimDf tRmbsClaim = initializeClaim();
        Map<String, String> deptCodeMap = this.getDeptCode(user.getDept().getSysOrgCode());
        String id = IdWorker.getIdStr();
        tRmbsClaim.setClaimId(id);
        tRmbsClaim.setItemId(ftRmbsClaimDf.getItemId());//报账单类型
        tRmbsClaim.setItemName(ftRmbsClaimDf.getItemName());//报账人ID
        tRmbsClaim.setApplyUserId(user.getUsername());//报账人ID
        tRmbsClaim.setApplyUserName(user.getRealname());//报账人用户名
        tRmbsClaim.setApplyComId(user.getDept().getOrgCode());//报账人所在公司ID
        tRmbsClaim.setApplyComName(user.getDept().getSysCompanyName());//报账人所在公司名称
        tRmbsClaim.setApplyDeptId(user.getDept().getSysOrgCode());//报账人所在部门ID
        if (ObjectUtils.isNotEmpty(deptCodeMap)){
            tRmbsClaim.setApplyDeptName(deptCodeMap.get("deptName"));//报账人所在部门名称
        }else {
            tRmbsClaim.setApplyDeptName(user.getDept().getName());//报账人所在部门名称
        }
        tRmbsClaim.setApplyUserEmail(user.getUser().getEmail());//报账人邮箱
        tRmbsClaim.setApplyUserTel(user.getPhone());//报账人电话
        tRmbsClaim.setApplyDate(new Date());//报账日期
        tRmbsClaim.setStatus(TCmccClaimUtil.CLAIM_STATE_0);
        tRmbsClaim.setProcessStateEng(TCmccClaimUtil.CLAIM_STEP_10_CODE);
        tRmbsClaim.setProcessState(TCmccClaimUtil.CLAIM_STEP_10_NAME);
        tRmbsClaim.setPayAmount(ftRmbsClaimDf.getPayAmount());//付款金额
        tRmbsClaim.setApplyAmount(ftRmbsClaimDf.getApplyAmount());//报账金额
        tRmbsClaim.setPriceAmount(ftRmbsClaimDf.getPriceAmount());//价款
        return tRmbsClaim;
    }

    @Override
    @Transactional
    public FtRmbsClaimDf saveOrUpdateClaim(FtRmbsClaimDf ftRmbsClaimDf) {

        if (StringUtils.isBlank(ftRmbsClaimDf.getClaimNo())){
            String claimNo = cmccClaimService.getClaimNumber("24" + ftRmbsClaimDf.getApplyComId().substring(2));
            ftRmbsClaimDf.setClaimNo(claimNo);
        }

        System.err.println(ftRmbsClaimDf.getApplyAmount());
        if (StringUtils.isBlank(String.valueOf(ftRmbsClaimDf.getApplyAmount()))) {
            BigDecimal amount = new BigDecimal(0.00);
            ftRmbsClaimDf.setApplyAmount(amount);//报账金额
        }
        System.err.println(ftRmbsClaimDf.getPayAmount());
        if (StringUtils.isBlank(String.valueOf(ftRmbsClaimDf.getPayAmount()))) {
            BigDecimal amount = new BigDecimal(0.00);
            ftRmbsClaimDf.setPayAmount(amount);//付款金额
        }
        if (StringUtils.isBlank(String.valueOf(ftRmbsClaimDf.getPriceAmount()))) {
            BigDecimal amount = new BigDecimal(0.00);
            ftRmbsClaimDf.setPriceAmount(amount);//价款
        }


        //初始化记账凭证信息
        if ("T005DF".equals(ftRmbsClaimDf.getItemId()) || "T024DF".equals(ftRmbsClaimDf.getItemId())) {
            Calendar calendar = Calendar.getInstance();
            ftRmbsClaimDf.setC05(new SimpleDateFormat("yyyy年MM月").format(calendar.getTime())); //记账日期
        }
        super.saveOrUpdate(ftRmbsClaimDf);
        return ftRmbsClaimDf;
    }

    @Override
    public void countAmount(String claimId) {
        List<FtRmbsClaimLineDf> ftRmbsClaimDfList = rmbsClaimLineDfMapper.selectList(Wrappers.lambdaQuery(FtRmbsClaimLineDf.class).eq(FtRmbsClaimLineDf::getClaimId, claimId));
        FtRmbsClaimDf ftRmbsClaimDf = getById(claimId);
        BigDecimal applyAmount = new BigDecimal(0);
        BigDecimal priceAmount = new BigDecimal(0);
        for (FtRmbsClaimLineDf claimLineDf : ftRmbsClaimDfList) {
            applyAmount = applyAmount.add(Optional.ofNullable(claimLineDf.getApplyAmount()).orElse(BigDecimal.ZERO));
            priceAmount = priceAmount.add(Optional.ofNullable(claimLineDf.getPriceAmount()).orElse(BigDecimal.ZERO));
        }

        update(Wrappers.lambdaUpdate(FtRmbsClaimDf.class)
                .eq(FtRmbsClaimDf::getClaimId, ftRmbsClaimDf.getClaimId())
                .set(FtRmbsClaimDf::getApplyAmount, applyAmount)
                .set(FtRmbsClaimDf::getPriceAmount, priceAmount)
        );
    }

    @Override
    public void countAmountPayList(String claimId) {
        List<FtRmbsPaylistDf> ftRmbsPaylistDfList = rmbsPaylistDfMapper.selectList(Wrappers.lambdaQuery(FtRmbsPaylistDf.class).eq(FtRmbsPaylistDf::getClaimId, claimId));
        FtRmbsClaimDf ftRmbsClaimDf = getById(claimId);
        BigDecimal payAmount = new BigDecimal(0);
        for (FtRmbsPaylistDf paylistDf : ftRmbsPaylistDfList) {
            payAmount = payAmount.add(Optional.ofNullable(paylistDf.getPayAmount()).orElse(BigDecimal.ZERO));
        }
        update(Wrappers.lambdaUpdate(FtRmbsClaimDf.class)
                .eq(FtRmbsClaimDf::getClaimId, ftRmbsClaimDf.getClaimId())
                .set(FtRmbsClaimDf::getPayAmount, payAmount)
        );
    }

    /**
     * 导入excel
     *
     * @return
     */
    @Override
    public R<Integer> importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！");
        }
        try {
            ImportParams params = new ImportParams();
            //解析导入EXCEL数据
            List<FtRmbsClaimDf> ftRmbsClaimDfList = ExcelImportUtil.importExcel(file.getInputStream(), FtRmbsClaimDf.class,
                    params);
            this.saveBatch(ftRmbsClaimDfList);
            return R.ok(ftRmbsClaimDfList.size());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.failed("导入失败");
    }

    @Override
    public Page<FtRmbsClaimDf> getProjectMisNo(Page page, FtRmbsClaimDf ftRmbsClaimDf) {
        ftRmbsClaimDf.setApplyComId(SecurityUtils.getUser().getDept().getOrgCode());
        return baseMapper.getProjectMisNo(page, ftRmbsClaimDf);
    }

    @Override
    public Page<FtRmbsClaimDf> getPayClaimNoList(Page page, FtRmbsClaimDf ftRmbsClaimDf) {
        ftRmbsClaimDf.setApplyDeptId(SecurityUtils.getUser().getDept().getSysOrgCode());
        return baseMapper.getPayClaimNoList(page, ftRmbsClaimDf);
    }

    @Override
    public Page<FtRmbsClaimDf> getPartyClaim(Page page, PartyOrgNameVo partyOrgNameVo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        Date date = new Date();
        partyOrgNameVo.setYear(sdf.format(date));
        return baseMapper.getPartyClaim(page, partyOrgNameVo);
    }

    @Override
    public Page<FtRmbsClaimDf> getPartyClaimAll(Page page, PartyOrgNameVo partyOrgNameVo) {
        return baseMapper.getPartyClaimAll(page, partyOrgNameVo);
    }

    @Override
    public Page<FtRmbsClaimDf> getPartyClaimT002DF(Page page, PartyOrgNameVo partyOrgNameVo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        Date date = new Date();
        partyOrgNameVo.setYear(sdf.format(date));
        return baseMapper.getPartyClaimT002DF(page, partyOrgNameVo);
    }

    @Override
    public Page<FtRmbsClaimDf> getPartyClaimT002DFAll(Page page, PartyOrgNameVo partyOrgNameVo) {
        return baseMapper.getPartyClaimT002DFAll(page, partyOrgNameVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional
    public void deleteClaim(String claimId) {
        FtRmbsClaimDf rmbsClaimDf = getById(claimId);
        if (rmbsClaimDf == null) {
            throw new BusinessException("报账单不存在");
        }
        remoteEflowService.deleteProcess(claimId, rmbsClaimDf.getItemId());

        update(Wrappers.lambdaUpdate(FtRmbsClaimDf.class)
                .eq(FtRmbsClaimDf::getClaimId, claimId)
                .set(FtRmbsClaimDf::getStatus, TCmccClaimUtil.CLAIM_STATE_1)
        );

//        SpringContextUtils.getBean(ClaimEventDFService.class).deleteClaim(rmbsClaimDf);
    }

    @Override
    public Page<FtRmbsClaimDf> getPage(Page page, Map<String, String> paramMap) {
        return baseMapper.getPage(page,paramMap);
    }

    @Override
    public Page<StatisticsDfVo> statisticsPage(Page page, Map<String, String> paramMap) {
        JxcmccUser jxcmccUser = SecurityUtils.getUser();
        //查全省
        if ("1".equals(this.getDfPermission(jxcmccUser))){
            return baseMapper.getStatisticsPage(page,paramMap,null);
        }else {
            //查自己部门
            return baseMapper.getStatisticsPage(page,paramMap,jxcmccUser.getDept().getSysOrgCode());
        }
    }



    /**
     *获取当前登录用户权限标识
     *@param jxcmccUser
     *@retutn
     *@create 2022/2/16 14:27
     */
    @Override
    public String  getPermission( JxcmccUser jxcmccUser ){
        String  permissionCode = "";
        List<Map<String, Object>> mapList = remoteUserService.queryUserRoleCode(jxcmccUser.getUsername());
        for (Map<String, Object> map : mapList) {
            String roleCode = map.get("ROLE_CODE").toString().toLowerCase();
            //辅助系统管理员
            if (roleCode.equals("ef_admin")||roleCode.equals("ef_local_admin")){
                permissionCode = "1";
                break;
            }else {
                permissionCode = "2";
            }
        }
        return permissionCode;
    }



    /**
     *获取当前登录用户权限标识
     *@param jxcmccUser
     *@retutn
     *@create 2022/2/16 14:27
     */
    @Override
    public String  getDfPermission( JxcmccUser jxcmccUser ){
        String  permissionCode = "";
        List<Map<String, Object>> mapList = remoteUserService.queryUserRoleCode(jxcmccUser.getUsername());
        for (Map<String, Object> map : mapList) {
            String roleCode = map.get("ROLE_CODE").toString().toLowerCase();
            //辅助系统管理员
            if (roleCode.equalsIgnoreCase("DF_ADMIN")||roleCode.equalsIgnoreCase("DF_LOCAL_ADMIN")){
                permissionCode = "1";
                break;
            }else {
                permissionCode = "2";
            }
        }
        return permissionCode;
    }



    @Override
    public boolean isAdmin() {
        return getDfPermission(SecurityUtils.getUser()).equals("1");
    }

    /**
     *根据当前登录用户判断报账单权限
     *@param jxcmccUser
     *@retutn
     *@create 2022/4/3 14:27
     */
    @Override
    public UserRightsVo getUserRights(JxcmccUser jxcmccUser ){
        UserRightsVo userRightsVo = new UserRightsVo();
        //获取用户部门信息
        SysDept sysDept =  SecurityUtils.getUser().getDept();
        //判断用户是否为管理员
        Boolean permission = this.isAdmin();
        //辅助系统管理员和省公司财务部
        if (permission||("财务部".equals(sysDept.getName()) && "303710".equals(sysDept.getOrgCode()))){
            userRightsVo.setUserRights("1");
        }else if("财务部".equals(sysDept.getName()) && !"303710".equals(sysDept.getOrgCode())){
            //地市财务部
            userRightsVo.setUserRights("2");
            userRightsVo.setApplyComCode(sysDept.getSysCompanyCode());
        } else {
            //其他人员
            userRightsVo.setUserRights("3");
            userRightsVo.setApplyDeptCode(sysDept.getSysOrgCode());
        }
        return userRightsVo;
    }


    /**
     * 报账单管理分页查询报账单信息
     * @param paramMap
     * @return
     */
    @Override
    public Page<FtRmbsClaimDf> selectFtRmbsDfClaimListByPage(Page page, Map<String, String> paramMap) {
        return baseMapper.selectFtRmbsDfClaimListByPage(page, paramMap);
    }
}
