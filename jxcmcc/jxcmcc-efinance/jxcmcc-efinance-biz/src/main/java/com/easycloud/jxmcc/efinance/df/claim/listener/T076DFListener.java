package com.easycloud.jxmcc.efinance.df.claim.listener;

import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.eflow.vo.BaseEflowApiVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component

public class T076DFListener extends BaseListener {


    @Autowired
    private FtRmbsClaimDfService rmbsClaimService;

    @Override
    public String businessType() {
        return "T076DF";
    }

    @Override
    public Map<String, Object> getVariables(BaseEflowApiVo data) {
        FtRmbsClaimDf cmccClaim = rmbsClaimService.getById(data.getBusinessId());
        Map<String, Object> variables = new HashMap<>();
        variables.put("startDeptCode",cmccClaim.getApplyDeptId());
        variables.put("claimNo",cmccClaim.getClaimNo());
        variables.put("startCompCode",cmccClaim.getApplyComId());
        variables.put("itemId",cmccClaim.getItemId());
        variables.put("item2Id",cmccClaim.getItem2Id());
        variables.put("applyAmount",cmccClaim.getApplyAmount());
        return variables;
    }
}
