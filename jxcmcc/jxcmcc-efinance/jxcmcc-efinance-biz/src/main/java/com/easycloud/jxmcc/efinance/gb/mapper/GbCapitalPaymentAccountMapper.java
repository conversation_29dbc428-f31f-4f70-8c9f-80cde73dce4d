/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxmcc.efinance.gb.entity.GbCapitalPaymentAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 国拨资金支付台账
 *
 * <AUTHOR> code generator
 * @date 2022-10-27 09:43:07
 */
@Mapper
public interface GbCapitalPaymentAccountMapper extends BaseMapper<GbCapitalPaymentAccount> {

    /**
     * 查询国拨资金支付台账数据
     * @return
     */
    List<GbCapitalPaymentAccount> selectGbCapitalPaymentAccountByDate();

    /**
     * 分页查询台账数据
     * @param page
     * @param gbCapitalPaymentAccount
     * @return
     */
    IPage selectGbCapitalPaymentAccountByPage(@Param("page") Page page, @Param("pay") GbCapitalPaymentAccount gbCapitalPaymentAccount);

}
