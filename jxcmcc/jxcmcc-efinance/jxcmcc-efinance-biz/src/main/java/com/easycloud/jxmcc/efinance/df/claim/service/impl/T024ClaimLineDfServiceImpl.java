package com.easycloud.jxmcc.efinance.df.claim.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxmcc.efinance.df.claim.mapper.T024ClaimDfMapper;
import com.easycloud.jxmcc.efinance.df.claim.service.IClaimDFEvent;
import com.easycloud.jxmcc.efinance.df.claim.service.T024ClaimLineDfService;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimLineDf;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsPartyDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimLineDfService;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsPartyDfService;
import com.easycloud.jxmcc.efinance.df.vo.claim.T024DFClaimLineVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class T024ClaimLineDfServiceImpl implements T024ClaimLineDfService, IClaimDFEvent {

    private final T024ClaimDfMapper claimDfMapper;
    private final FtRmbsClaimDfService ftRmbsClaimDf;
    private final FtRmbsClaimLineDfService ftRmbsClaimLineDf;
    private final FtRmbsPartyDfService ftRmbsPartyDfService;

    public static final String RECEIVE_CLAIM_TYPE = "T024DF";
    public static final String RECEIVE_CLAIM_NAME = "党费收入报账单";

    @Override
    public String itemId() {
        return RECEIVE_CLAIM_TYPE;
    }

    @Override
    public void deleteClaim(FtRmbsClaimDf ftRmbsClaimDf) {

    }

    @Override
    public IPage<T024DFClaimLineVo> page(Page page, T024DFClaimLineVo T024DFClaimLineVo) {
        return claimDfMapper.page(page,T024DFClaimLineVo);
    }

    @Override
    public List<T024DFClaimLineVo> getById(String id) {
        return claimDfMapper.getById(id);
    }

    @Override
    public R importExcel(MultipartFile file, String claimId) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！");
        }
        try {
            List<T024DFClaimLineVo> importClaimLine;
            //解析导入EXCEL数据
            importClaimLine = EasyExcel.read(file.getInputStream())
                    .head(T024DFClaimLineVo.class)
                    .sheet(0)
                    .headRowNumber(1)
                    .doReadSync();

            if (importClaimLine.size() == 0) {
                return R.error("导入的数据为空");
            }
            int start = 1;
            for (int i = 0; i < importClaimLine.size(); i++) {
                T024DFClaimLineVo t024DFClaimLineVo = importClaimLine.get(i);
                //检查导入是否合法
                if (StringUtils.isBlank(t024DFClaimLineVo.getItem3Id())) {
                    return R.error("第" + (i + start) + "业务小类id不能为空");
                }
                if (StringUtils.isBlank(t024DFClaimLineVo.getItem3Name())) {
                    return R.error("第" + (i + start) + "业务小类名称不能为空");
                }
                if (StringUtils.isBlank(t024DFClaimLineVo.getC02())) {
                    return R.error("第" + (i + start) + "收入来源不能为空");
                }
                if (StringUtils.isBlank(t024DFClaimLineVo.getClaimLineDesc())) {
                    return R.error("第" + (i + start) + "备注不能为空");
                }
                if (StringUtils.isBlank(t024DFClaimLineVo.getApplyAmount())) {
                    return R.error("第" + (i + start) + "金额不能为空");
                } else {
                    if (!(t024DFClaimLineVo.getApplyAmount().matches("[0-9]+.?[0-9]*"))) {
                        return R.error("第" + (i + start) + "行金额只能为数字");
                    }
                }
            }
            List<FtRmbsClaimLineDf> list = importClaimLine.stream().map(t -> {
                FtRmbsClaimLineDf claimLine = new FtRmbsClaimLineDf();
                //数据类型必须保持一致，否则copy该字段为null
                BigDecimal applyAmount = new BigDecimal(t.getApplyAmount());
                claimLine.setApplyAmount(applyAmount);
                BeanUtils.copyProperties(t, claimLine);
                claimLine.setClaimId(claimId);
                return claimLine;
            }).collect(Collectors.toList());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
            Date date = new Date();
            //拿到名称匹配id
            List<FtRmbsPartyDf> ftRmbsPartyDfList = ftRmbsPartyDfService.list(Wrappers.lambdaQuery(FtRmbsPartyDf.class)
                    .eq(FtRmbsPartyDf::getYear, sdf.format(date)));
            if (!ftRmbsPartyDfList.isEmpty()){
                Map<String, String> map = ftRmbsPartyDfList.stream().collect(Collectors.toMap(FtRmbsPartyDf::getPartyName,FtRmbsPartyDf::getId));
                if (ObjectUtil.isNotEmpty(map)){
                    for (FtRmbsClaimLineDf rmbsClaimLineDf : list) {
                        if (StringUtils.isNotBlank(map.get(rmbsClaimLineDf.getC02()))){
                            rmbsClaimLineDf.setC01(map.get(rmbsClaimLineDf.getC02()));
                        }
                    }
                }
            }
            ftRmbsClaimLineDf.saveBatch(list);
            ftRmbsClaimDf.countAmount(claimId);
            return R.ok(list.size());
        } catch (Exception e) {
            return R.error("上传异常");
        }
    }
}
