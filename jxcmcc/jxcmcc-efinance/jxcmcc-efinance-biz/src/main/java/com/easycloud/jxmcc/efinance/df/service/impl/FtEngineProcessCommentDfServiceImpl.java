/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.df.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxmcc.efinance.df.entity.FtEngineProcessCommentDf;
import com.easycloud.jxmcc.efinance.df.mapper.FtEngineProcessCommentDfMapper;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxmcc.efinance.df.service.FtEngineProcessCommentDfService;
import com.easycloud.jxmcc.efinance.df.vo.ProcessVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.easycloud.jxcmcc.common.core.util.R;
import org.springframework.web.multipart.MultipartFile;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;

import java.util.List;

/**
 * 老党费审批记录表
 *
 * <AUTHOR> code generator
 * @date 2023-02-16 17:17:09
 */
@Service
@Slf4j
public class FtEngineProcessCommentDfServiceImpl extends ServiceImpl<FtEngineProcessCommentDfMapper, FtEngineProcessCommentDf> implements FtEngineProcessCommentDfService {


    /**
    * 导入excel
    * @return
    */
    @Override
    public R<Integer> importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！" );
        }
        ImportParams params = new ImportParams();
        //解析导入EXCEL数据
        List<FtEngineProcessCommentDf> ftEngineProcessCommentDfList = ExcelImportUtil.importExcel(file.getInputStream(), FtEngineProcessCommentDf. class,
        params);
        this.saveBatch(ftEngineProcessCommentDfList);
        return R.ok(ftEngineProcessCommentDfList.size());
    }

    @Override
    public IPage<ProcessVO> myPage(IPage<ProcessVO> page, FtEngineProcessCommentDf ftEngineProcessCommentDf) {
        return baseMapper.myPage(page,ftEngineProcessCommentDf);
    }
}
