/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.BeanHelperUtil;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.AppLog;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.security.annotation.Inner;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.efinance.gb.entity.GbAssetDetails;
import com.easycloud.jxmcc.efinance.gb.service.GbAssetDetailsService;
import com.easycloud.jxmcc.efinance.util.PermissionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 资产明细表
 *
 * <AUTHOR> code generator
 * @date 2022-10-25 10:08:23
 */
@RestController
@AllArgsConstructor
@RequestMapping("/gbassetdetails" )
@Api(value = "gbassetdetails", tags = "资产明细表管理")
public class GbAssetDetailsController {

    private final GbAssetDetailsService gbAssetDetailsService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param gbAssetDetails 资产明细表
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询资产明细表" )
    @GetMapping("/page" )
    public R getGbAssetDetailsPage(Page page, GbAssetDetails gbAssetDetails) {
        QueryWrapper queryWrapper = BeanHelperUtil.setQueryWrapper(gbAssetDetails);
        queryWrapper.isNull("CLAIM_ID");
        if(!PermissionUtil.isAdmin()){
            queryWrapper.apply("BOOK_TYPE_CODE LIKE '%" + SecurityUtils.getUser().getDept().getOrgCode() + "'");
        }
        return R.ok(gbAssetDetailsService.page(page, queryWrapper));
    }

    /**
     * 通过id查询资产明细表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询资产明细表" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(gbAssetDetailsService.getById(id));
    }

    /**
     * 新增资产明细表
     * @param gbAssetDetails 资产明细表
     * @return R
     */
    @ApiOperation(value = "新增资产明细表", notes = "新增资产明细表")
    @SysLog("新增资产明细表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('generator_gbassetdetails_add')" )
    public R save(@RequestBody GbAssetDetails gbAssetDetails) {
        return R.ok(gbAssetDetailsService.save(gbAssetDetails));
    }

    /**
     * 修改资产明细表
     * @param gbAssetDetails 资产明细表
     * @return R
     */
    @ApiOperation(value = "修改资产明细表", notes = "修改资产明细表")
    @SysLog("修改资产明细表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('generator_gbassetdetails_edit')" )
    public R updateById(@RequestBody GbAssetDetails gbAssetDetails) {
        return R.ok(gbAssetDetailsService.updateById(gbAssetDetails));
    }

    /**
     * 通过id删除资产明细表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除资产明细表", notes = "通过id删除资产明细表")
    @SysLog("通过id删除资产明细表" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('generator_gbassetdetails_del')" )
    public R removeById(@PathVariable String id) {
        return R.ok(gbAssetDetailsService.removeById(id));
    }


   /**
     * 导入资产明细表
     * @return R
     */
    @ApiOperation(value = "导入资产明细表", notes = "导入资产明细表")
    @SysLog("导入资产明细表" )
    @PreAuthorize("@pms.hasPermission('generator_gbassetdetails_import')" )
    @FileImportLogAnnotation(busTable = "资产明细表",busSystem = "generator")
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return gbAssetDetailsService.importExcel(file,importParams);
    }

    /**
     * 导出资产明细表
     * @return R
     */
    @ApiOperation(value = "导出资产明细表", notes = "导出资产明细表")
    @SysLog("导出资产明细表")
    @PreAuthorize("@pms.hasPermission('generator_gbassetdetails_export')")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, GbAssetDetails gbAssetDetails) {
        List<GbAssetDetails>  gbAssetDetailsList = gbAssetDetailsService.list(Wrappers.lambdaQuery(gbAssetDetails));
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("资产明细表列表", "资产明细表"),
                GbAssetDetails. class,gbAssetDetailsList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出资产明细表导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版资产明细表文件", notes = "导出导入模版资产明细表文件")
    @SysLog("导出资产明细表导入模版文件")
    @PreAuthorize("@pms.hasPermission('generator_gbassetdetails_export')")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/gbAssetDetails.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 资产明细申请数据
     * @param jsonObject
     * @return
     */
    @ApiOperation(value = "资产明细申请数据", notes = "资产明细申请数据")
    @SysLog("资产明细申请数据" )
    @AppLog(value = "GB_RECEIVE_ZC", verify = false)
    @PostMapping("/handleGbAssetDetailsData")
    public R handleGbAssetDetailsData(@RequestBody JSONObject jsonObject){
        return gbAssetDetailsService.handleGbAssetDetailsData(jsonObject);
    }

}
