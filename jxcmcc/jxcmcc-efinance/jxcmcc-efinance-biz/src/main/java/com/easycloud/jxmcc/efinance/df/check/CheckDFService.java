package com.easycloud.jxmcc.efinance.df.check;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.easycloud.jxcmcc.common.core.exception.BusinessException;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.efinance.util.TCmccClaimUtil;
import com.easycloud.jxmcc.efinance.vo.CheckVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class CheckDFService {

    private final FtRmbsClaimDfService rmbsClaimDfService;

    public void checkDf(CheckVo checkVo) {
        FtRmbsClaimDf rmbsClaimDf = rmbsClaimDfService.getById(checkVo.getClaimId());
        if (rmbsClaimDf == null) {
            throw new BusinessException("报账单不存在");
        }
        IDFCheck check = null;
        try {
            check = ReflectUtil.<IDFCheck>newInstance(StrUtil.format("com.easycloud.jxmcc.efinance.df.check.{}Check", rmbsClaimDf.getItemId()));
        } catch (Exception e) {
            log.error("获取校验类异常", e);
        }
        if (check != null&&rmbsClaimDf.getProcessStateEng()!=null) {
            switch (rmbsClaimDf.getProcessStateEng()){
                case TCmccClaimUtil.CLAIM_STEP_10_CODE:
                    check.createDf(rmbsClaimDf, checkVo.getData()).drafted();
                    break;
            }
        }else {
            throw new BusinessException("验证类不存在");
        }

    }
}
