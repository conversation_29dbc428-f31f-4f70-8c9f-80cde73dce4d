/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.df.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsConfidentClaimDf;
import com.easycloud.jxmcc.efinance.df.mapper.FtRmbsClaimLineDfMapper;
import com.easycloud.jxmcc.efinance.df.mapper.FtRmbsConfidentClaimDfMapper;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsConfidentClaimDfService;
import com.easycloud.jxmcc.efinance.df.vo.FtRmbsClaimLineDfVo;
import com.easycloud.jxmcc.efinance.service.FtRmbsClaimseqService;
import com.easycloud.jxmcc.efinance.vo.ClaimLineVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 记账凭证
 *
 * <AUTHOR>
 * @date 2022-08-05 16:07:57
 */
@Service
@Slf4j
@AllArgsConstructor
public class FtRmbsConfidentClaimDfServiceImpl extends ServiceImpl<FtRmbsConfidentClaimDfMapper, FtRmbsConfidentClaimDf> implements FtRmbsConfidentClaimDfService {

    private final FtRmbsClaimLineDfMapper rmbsClaimLineDfMapper;
    private final FtRmbsClaimDfService rmbsClaimDfService;
    private final FtRmbsClaimseqService ftRmbsClaimseqService;

    /**
    * 导入excel
    * @return
    */
    @Override
    public R<Integer> importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！" );
        }
        ImportParams params = new ImportParams();
        //解析导入EXCEL数据
        List<FtRmbsConfidentClaimDf> ftRmbsConfidentClaimDfList = ExcelImportUtil.importExcel(file.getInputStream(), FtRmbsConfidentClaimDf. class,
        params);
        this.saveBatch(ftRmbsConfidentClaimDfList);
        return R.ok(ftRmbsConfidentClaimDfList.size());
    }


    @Override
    @Transactional
    public void updateByClaimId(String claimId) {
        FtRmbsClaimDf ftRmbsClaimDf = rmbsClaimDfService.getById(claimId);
        //查询报账单行信息
        List<FtRmbsClaimLineDfVo> ftRmbsClaimLineDfs = rmbsClaimLineDfMapper.claimLineList(ftRmbsClaimDf.getClaimId());
        //组装记账凭证信息
        List<FtRmbsConfidentClaimDf> dfList = new ArrayList<FtRmbsConfidentClaimDf>();
        //借方记录
        if ("T005DF".equals(ftRmbsClaimDf.getItemId())) {
            //按业务小类汇总账户信息金额
            if (ftRmbsClaimLineDfs != null && ftRmbsClaimLineDfs.size() > 0) {
                for (int i = 0; i < ftRmbsClaimLineDfs.size(); i++) {
                    FtRmbsConfidentClaimDf confidentClaimDf = new FtRmbsConfidentClaimDf();
                    FtRmbsClaimLineDfVo ftRmbsClaimLineDfVo = ftRmbsClaimLineDfs.get(i);
                    String amount = ftRmbsClaimLineDfVo.getAmount();
                    String itemId = ftRmbsClaimLineDfVo.getItem3Id();
                    if ("1000".equals(itemId)) { //培训支出
                        confidentClaimDf.setAmountInfo("COST_PXZC");
                        confidentClaimDf.setAmountName("借：党费使用-培训支出");
                    } else if ("1010".equals(itemId)) {//资料支出
                        confidentClaimDf.setAmountInfo("COST_ZLZC");
                        confidentClaimDf.setAmountName("借：党费使用-资料支出");
                    } else if ("1020".equals(itemId)) {//表彰支出
                        confidentClaimDf.setAmountInfo("COST_BZZC");
                        confidentClaimDf.setAmountName("借：党费使用-表彰支出");
                    } else if ("1030".equals(itemId)) {//慰问支出
                        confidentClaimDf.setAmountInfo("COST_WWZC");
                        confidentClaimDf.setAmountName("借：党费使用-慰问支出");
                    } else if ("1040".equals(itemId)) {//活动支出
                        confidentClaimDf.setAmountInfo("COST_HDZC");
                        confidentClaimDf.setAmountName("借：党费使用-活动支出");
                    } else if ("1050".equals(itemId)) {//会议支出
                        confidentClaimDf.setAmountInfo("COST_HYZC");
                        confidentClaimDf.setAmountName("借：党费使用-会议支出");
                    } else if ("1060".equals(itemId)) {//支持脱贫攻坚支出-补交党费
                        confidentClaimDf.setAmountInfo("COST_ZCTPGJZC");
                        confidentClaimDf.setAmountName("借：党费使用-支持脱贫攻坚支出");
                    } else if ("1070".equals(itemId)) {//其他支出
                        confidentClaimDf.setAmountInfo("COST_QTZC");
                        confidentClaimDf.setAmountName("借：党费使用-其他支出");
                    } else if ("1071".equals(itemId)) {//捐赠支出
                        confidentClaimDf.setAmountInfo("COST_JZZC");
                        confidentClaimDf.setAmountName("借：党费使用-捐赠支出");
                    } else if ("1080".equals(itemId)) {//上交党费支出
                        confidentClaimDf.setAmountInfo("COST_SJDFZC");
                        confidentClaimDf.setAmountName("借：上交党费支出");
                    } else if ("1090".equals(itemId)) {//基础设施支出
                        confidentClaimDf.setAmountInfo("COST_JCSSZC");
                        confidentClaimDf.setAmountName("借：党费使用-基础设施支出");
                    } else {
                        confidentClaimDf.setAmountInfo("COST_DBDFZC");
                        confidentClaimDf.setAmountName("借：下拨党费支出");
                    }
                    confidentClaimDf.setConfidentCode(ftRmbsClaimDf.getC04()); //凭证编号
                    confidentClaimDf.setApplyDate(ftRmbsClaimDf.getC05()); //记账日期
                    confidentClaimDf.setClaimId(ftRmbsClaimDf.getClaimId());  //关联报账单id
                    confidentClaimDf.setClaimNo(ftRmbsClaimDf.getClaimNo());  //报账单号
                    confidentClaimDf.setSummary(ftRmbsClaimDf.getSummary());  //摘要信息
                    confidentClaimDf.setApplyAmount(amount != null ? new BigDecimal(amount) : new BigDecimal(0)); //借方金额
                    confidentClaimDf.setPayAmount(null); //贷方金额
                    confidentClaimDf.setOrderNum("1");
                    dfList.add(confidentClaimDf);
                }
            }
            FtRmbsConfidentClaimDf confidentClaimDf = new FtRmbsConfidentClaimDf();
            confidentClaimDf.setConfidentCode(ftRmbsClaimDf.getC04()); //凭证编号
            confidentClaimDf.setApplyDate(ftRmbsClaimDf.getC05()); //记账日期
            confidentClaimDf.setClaimId(ftRmbsClaimDf.getClaimId());  //关联报账单id
            confidentClaimDf.setClaimNo(ftRmbsClaimDf.getClaimNo());  //报账单号
            confidentClaimDf.setAmountInfo("CR_0140ZH");
            confidentClaimDf.setAmountName("贷：银行存款-0140账户");
            confidentClaimDf.setSummary(ftRmbsClaimDf.getClaimNo()); //报账单号
            confidentClaimDf.setApplyAmount(null);  //借方
            confidentClaimDf.setPayAmount(ftRmbsClaimDf.getApplyAmount()); //贷方
            confidentClaimDf.setOrderNum("2");
            dfList.add(confidentClaimDf);
        }

        if ("T024DF".equals(ftRmbsClaimDf.getItemId())){
            FtRmbsConfidentClaimDf confidentClaimDr = new FtRmbsConfidentClaimDf();
            confidentClaimDr.setConfidentCode(ftRmbsClaimDf.getC04()); //凭证编号
            confidentClaimDr.setApplyDate(ftRmbsClaimDf.getC05()); //记账日期
            confidentClaimDr.setClaimId(ftRmbsClaimDf.getClaimId());  //关联报账单id
            confidentClaimDr.setClaimNo(ftRmbsClaimDf.getClaimNo());  //报账单号
            confidentClaimDr.setAmountInfo("DR_0140ZH");
            confidentClaimDr.setAmountName("借：银行存款-0140账户");
            confidentClaimDr.setSummary(ftRmbsClaimDf.getClaimNo());  //报账单号
            confidentClaimDr.setApplyAmount(ftRmbsClaimDf.getApplyAmount()); //借方
            confidentClaimDr.setPayAmount(null);  //贷方
            confidentClaimDr.setOrderNum("1");
            dfList.add(confidentClaimDr);

            //按业务小类汇总账户信息金额
            if (ftRmbsClaimLineDfs != null && ftRmbsClaimLineDfs.size() > 0) {
                for (int i = 0; i < ftRmbsClaimLineDfs.size(); i++) {
                    FtRmbsConfidentClaimDf confidentClaimDf = new FtRmbsConfidentClaimDf();
                    FtRmbsClaimLineDfVo ftRmbsClaimLineDfVo = ftRmbsClaimLineDfs.get(i);
                    String amount = ftRmbsClaimLineDfVo.getAmount();
                    String itemId = ftRmbsClaimLineDfVo.getItem3Id();
                    if("1020".equals(itemId)){//基层支部上交
                        confidentClaimDf.setAmountInfo("INCOME_JCZBSJ");
                        confidentClaimDf.setAmountName("贷：基层支部上交");
                    }else if("1030".equals(itemId)){//上级党委下拨
                        confidentClaimDf.setAmountInfo("INCOME_SJDWXB");
                        confidentClaimDf.setAmountName("贷：上级党委下拨");
                    }else if("1040".equals(itemId)){//利息收入
                        confidentClaimDf.setAmountInfo("INCOME_LXSR");
                        confidentClaimDf.setAmountName("贷：利息收入");
                    }else if("1050".equals(itemId)){//其他收入
                        confidentClaimDf.setAmountInfo("INCOME_QTSR");
                        confidentClaimDf.setAmountName("贷：其他收入");
                    }else{
                        confidentClaimDf.setAmountInfo("INCOME_QTSR");
                        confidentClaimDf.setAmountName("贷：其他收入");
                    }

                    confidentClaimDf.setConfidentCode(ftRmbsClaimDf.getC04()); //凭证编号
                    confidentClaimDf.setApplyDate(ftRmbsClaimDf.getC05()); //记账日期
                    confidentClaimDf.setClaimId(ftRmbsClaimDf.getClaimId());  //关联报账单id
                    confidentClaimDf.setClaimNo(ftRmbsClaimDf.getClaimNo());  //报账单号
                    confidentClaimDf.setSummary(ftRmbsClaimDf.getSummary()); //摘要信息
                    confidentClaimDf.setApplyAmount(null);  //借方
                    confidentClaimDf.setPayAmount(amount != null ? new BigDecimal(amount) : new BigDecimal(0));  //贷方
                    confidentClaimDf.setOrderNum("2");

                    dfList.add(confidentClaimDf);
                }
            }
        }
        if (dfList!=null && dfList.size()>0){
            //先删除再插入
            baseMapper.delete(new QueryWrapper<FtRmbsConfidentClaimDf>().eq("CLAIM_ID",claimId));
            for (FtRmbsConfidentClaimDf confidentClaimDf : dfList) {
                baseMapper.insert(confidentClaimDf);
            }
        }
    }

    @Override
    public List<FtRmbsConfidentClaimDf> getByClaimId(String claimId) {
        QueryWrapper<FtRmbsConfidentClaimDf> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("CLAIM_ID",claimId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public int deleteConfident(ClaimLineVo claimLineVo) {
        List<String> idList = claimLineVo.getIdList();
        baseMapper.deleteBatchIds(idList);
        rmbsClaimDfService.countAmount(claimLineVo.getClaimId());
        return claimLineVo.getIdList().size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCode(String claimId) {
        FtRmbsClaimDf ftRmbsClaimDf = rmbsClaimDfService.getById(claimId);
        int year = DateUtil.year(new Date());
        int num = ftRmbsClaimseqService.getTbClaimNumberID(String.valueOf(year));
        String serialNumber = autoFill(num,5);
        ftRmbsClaimDf.setC04(year + serialNumber);
        // 更新凭证编号
        rmbsClaimDfService.updateById(ftRmbsClaimDf);
    }


    /**
     * 流水号补位
     *
     * @param a   传入的原数据
     * @param len 要变成的长度，若超长则返回原数据
     * @return
     */
    private static String autoFill(Integer a, int len) {
        String string = a + "";
        int num = string.length();
        if (string.length() < len) {
            for (int i = 0; i < (len - num); i++) {
                string = "0" + string;
            }
        }
        return string;
    }
}
