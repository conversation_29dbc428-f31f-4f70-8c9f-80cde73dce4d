package com.easycloud.jxmcc.efinance.df.claim.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.efinance.df.claim.service.T002ClaimLineDfService;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimLineDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimLineDfService;
import com.easycloud.jxmcc.efinance.df.vo.claim.T002DFClaimLineVo;
import com.easycloud.jxmcc.efinance.df.vo.claim.T005DFClaimLineVo;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("t002claimdf")
@AllArgsConstructor
public class T002ClaimDfController {

    private final T002ClaimLineDfService t002ClaimLineDfService;

    private final FtRmbsClaimLineDfService ftRmbsClaimLineDfService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param t002DFClaimLineVo 党费系统报账行明细
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog(value = "分页查询党费系统报账行明细", demandId ="xq_T002DF")
    @GetMapping("/page" )
    public R getFtRmbsClaimLinePage(Page page, T002DFClaimLineVo t002DFClaimLineVo) {
        return R.ok(t002ClaimLineDfService.page(page,t002DFClaimLineVo));
    }


    /**
     * 通过ID查询党费系统报账行明细
     * @param id
     * @return R
     */
    @ApiOperation(value = "通过ID查询", notes = "通过ID查询")
    @SysLog(value = "通过ID查询党费系统报账行明细", demandId ="xq_T002DF")
    @GetMapping("/{id}")
    public R getById(@PathVariable("id") String id) {
        return R.ok(t002ClaimLineDfService.getById(id));
    }



    /**
     * 导入党费审批单明细
     * @return R
     */
    @ApiOperation(value = "导入党费审批单明细", notes = "导入党费审批单明细")
    @SysLog("导入党费审批单明细" )
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, String claimId) throws Exception {
        return t002ClaimLineDfService.importExcel(file,claimId);
    }


    /**
     * 下载模板
     */
    @ApiOperation(value = "下载模板", notes = "下载模板")
    @SysLog(value = "下载模板" ,demandId = "xq_T002DF")
    @GetMapping("/downLoadImportTemplate")
    public ResponseEntity<Resource> downLoadImportTemplate(){
        ClassPathResource classPathResource = new ClassPathResource("templates/poi/df/T002DfClaimLineTemplate.xls");
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("application/octet-stream;charset=UTF-8"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;")
                .body(classPathResource);
    }


    /**
     * 导出党费审批单明细
     * @return
     */
    @ApiOperation(value = "导出党费审批单明细", notes = "导出党费审批单明细")
    @SysLog(value = "导出党费审批单明细" ,demandId="xq_T002DF")
    @GetMapping(value = "/exportClaimLine")
    public void exportClaimLine(HttpServletResponse response, T002DFClaimLineVo t002DFClaimLineVo){
        if (StringUtils.isEmpty(t002DFClaimLineVo.getClaimId())){
            throw  new BootstrapMethodError("导出失败，请联系管理员");
        };
        List<FtRmbsClaimLineDf> exportList = ftRmbsClaimLineDfService.list(Wrappers.lambdaQuery(FtRmbsClaimLineDf.class)
                .eq(FtRmbsClaimLineDf::getClaimId, t002DFClaimLineVo.getClaimId()));

        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("党费审批单明细", "明细数据"), T002DFClaimLineVo.class,
                    exportList.stream().map(x -> {
                        T002DFClaimLineVo vo = new T002DFClaimLineVo();
                        BeanUtil.copyProperties(x, vo);
                        return vo;
                    }).collect(Collectors.toList()));
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
            oss.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

}
