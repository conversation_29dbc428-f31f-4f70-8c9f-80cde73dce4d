/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.wg.osb_rbs_cmf_hq_pageinquirygridquotasrv.QUOTADETAILS;
import com.easycloud.jxcmcc.common.core.util.BeanHelperUtil;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.util.WebUtils;
import com.easycloud.jxcmcc.common.data.builder.QueryWrapperBuilder;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfo;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfoSplit;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectInfoSplitService;
import com.easycloud.jxmcc.efinance.gb.vo.GbProjectInfoSplitVo;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;


/**
 * 国拨项目信息拆分
 *
 * <AUTHOR> code generator
 * @date 2022-10-20 11:24:12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/gbprojectinfosplit" )
@Api(value = "gbprojectinfosplit", tags = "国拨项目信息拆分管理")
public class GbProjectInfoSplitController {

    private final GbProjectInfoSplitService gbProjectInfoSplitService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param gbProjectInfoSplit 国拨项目信息拆分
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询国拨项目信息拆分" )
    @GetMapping("/page" )
    public R getGbProjectInfoSplitPage(Page page, GbProjectInfoSplit gbProjectInfoSplit) {
        String reType = "";
        Map<String, String> parameterMap = WebUtils.getParameterMap();
        if(StringUtils.isNotBlank(parameterMap.get("claimNoGcl"))){
            reType = parameterMap.get("claimNoGcl");
        }
        if(StringUtils.isNotBlank(reType)){
            parameterMap.remove("claimNoGcl");
        }
        QueryWrapper build = QueryWrapperBuilder.<GbProjectInfoSplit>create(GbProjectInfoSplit.class).build(parameterMap);
        build.eq("DELETE_FLAG","0");
//        JxcmccUser jxcmccUser = SecurityUtils.getUser();
//        String orgCodeShort = jxcmccUser.getUser().getOrgCodeShort();
//        build.eq("COMPANY_CODE",orgCodeShort);
        if("T0132".equals(reType)){
            build.isNull("CLAIM_NO_GCL");
        }else if("T0133".equals(reType)){
            build.isNull("CLAIM_NO_ZF");
        }
        return R.ok(gbProjectInfoSplitService.page(page, build));
    }

    /**
     * 分页查询
     * @param page 分页对象
     * @param gbProjectInfoSplit 国拨项目信息拆分
     * @return
     */
    @ApiOperation(value = "分页查询(未报账)", notes = "分页查询(未报账)")
    @SysLog("分页查询国拨项目信息拆分(未报账)" )
    @GetMapping("/pageNoClaim" )
    public R getGbProjectInfoSplitNoClaimPage(Page page, GbProjectInfoSplit gbProjectInfoSplit) {
                JxcmccUser jxcmccUser = SecurityUtils.getUser();
//        if(SecurityUtils.isAdmin()){
//            gbProjectInfoSplit.setCompanyCode(null);
//        }else{
//            String orgCodeShort = jxcmccUser.getUser().getOrgCodeShort();
//            gbProjectInfoSplit.setCompanyCode(orgCodeShort);
//        }
        return R.ok(gbProjectInfoSplitService.selectNoClaimPage(page, gbProjectInfoSplit));
    }

    /**
     * 分页查询
     * @param page 分页对象
     * @param gbProjectInfoSplit 国拨项目信息拆分
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询国拨项目信息拆分-流程查询" )
    @GetMapping("/pageTodo" )
    public R getGbProjectInfoSplitTodoPage(Page page, GbProjectInfoSplit gbProjectInfoSplit) {
        Map<String, String> parameterMap = WebUtils.getParameterMap();
        QueryWrapper build = QueryWrapperBuilder.<GbProjectInfoSplit>create(GbProjectInfoSplit.class).build(parameterMap);
        build.eq("DELETE_FLAG","0");
        return R.ok(gbProjectInfoSplitService.page(page, build));
    }

    /**
     * 分页查询(采购报账查询)
     * @param page 分页对象
     * @param gbProjectInfoSplitVo 国拨项目信息拆分
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询国拨项目信息拆分(采购报账查询)" )
    @GetMapping("/pageClaim" )
    public R getGbProjectInfoSplitClaimPage(Page page, GbProjectInfoSplitVo gbProjectInfoSplitVo) {
        JxcmccUser jxcmccUser = SecurityUtils.getUser();
        if(SecurityUtils.isAdmin()){
            gbProjectInfoSplitVo.setCompanyCode(null);
        }else{
            String orgCodeShort = jxcmccUser.getUser().getOrgCodeShort();
            gbProjectInfoSplitVo.setCompanyCode(orgCodeShort);
        }
        return R.ok(gbProjectInfoSplitService.selectPage(page, gbProjectInfoSplitVo));
    }

    /**
     * 通过id查询国拨项目信息拆分
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询国拨项目信息拆分" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(gbProjectInfoSplitService.getById(id));
    }

    /**
     * 通过id查询国拨项目信息拆分
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询国拨项目信息拆分" )
    @GetMapping("getPro/{id}" )
    public R getPro(@PathVariable("id" ) String id) {
        return R.ok(gbProjectInfoSplitService.getPro(id));
    }
    /**
     * 新增国拨项目信息拆分
     * @param gbProjectInfoSplit 国拨项目信息拆分
     * @return R
     */
    @ApiOperation(value = "新增国拨项目信息拆分", notes = "新增国拨项目信息拆分")
    @SysLog("新增国拨项目信息拆分" )
    @PostMapping
    public R save(@RequestBody GbProjectInfoSplit gbProjectInfoSplit) {
        return gbProjectInfoSplitService.saveGbProjectSplit(gbProjectInfoSplit,"gbParentProject");
    }

    /**
     * 新增国拨变更拆分
     * @param gbProjectInfoSplit 新增国拨变更拆分
     * @return R
     */
    @ApiOperation(value = "新增国拨变更拆分", notes = "新增国拨变更拆分")
    @SysLog("新增国拨变更拆分" )
    @PostMapping("/saveSplit")
    public R saveSplit(@RequestBody GbProjectInfoSplit gbProjectInfoSplit) {
        return gbProjectInfoSplitService.saveGbProjectSplit(gbProjectInfoSplit,"gbSplitParentProject");
    }

    /**
     * 修改国拨项目信息拆分
     * @param gbProjectInfoSplit 国拨项目信息拆分
     * @return R
     */
    @ApiOperation(value = "修改国拨项目信息拆分", notes = "修改国拨项目信息拆分")
    @SysLog("修改国拨项目信息拆分" )
    @PutMapping
    public R updateById(@RequestBody GbProjectInfoSplit gbProjectInfoSplit) {
        return gbProjectInfoSplitService.updateByGbProjectSplit(gbProjectInfoSplit,"gbParentProject");
    }

    /**
     * 修改国拨变更拆分
     * @param gbProjectInfoSplit 修改国拨变更拆分
     * @return R
     */
    @ApiOperation(value = "修改国拨变更拆分", notes = "修改国拨变更拆分")
    @SysLog("修改国拨变更拆分" )
    @PutMapping("/updateSplit")
    public R updateSplitById(@RequestBody GbProjectInfoSplit gbProjectInfoSplit) {
        return gbProjectInfoSplitService.updateByGbProjectSplit(gbProjectInfoSplit,"gbSplitParentProject");
    }

    /**
     * 通过id删除国拨项目信息拆分
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除国拨项目信息拆分", notes = "通过id删除国拨项目信息拆分")
    @SysLog("通过id删除国拨项目信息拆分" )
    @DeleteMapping("/{id}" )
    public R removeById(@PathVariable String id) {
        return R.ok(gbProjectInfoSplitService.reById(id));
    }


   /**
     * 导入国拨项目信息拆分
     * @return R
     */
    @ApiOperation(value = "导入国拨项目信息拆分", notes = "导入国拨项目信息拆分")
    @SysLog("导入国拨项目信息拆分" )
    @FileImportLogAnnotation(busTable = "国拨项目信息拆分",busSystem = "generator")
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return gbProjectInfoSplitService.importExcel(file,importParams);
    }

    /**
     * 导出国拨项目信息拆分
     * @return R
     */
    @ApiOperation(value = "导出国拨项目信息拆分", notes = "导出国拨项目信息拆分")
    @SysLog("导出国拨项目信息拆分")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, GbProjectInfoSplit gbProjectInfoSplit) {
        List<GbProjectInfoSplit>  gbProjectInfoSplitList = gbProjectInfoSplitService.list(Wrappers.lambdaQuery(gbProjectInfoSplit));
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("国拨项目信息拆分列表", "国拨项目信息拆分"),
                GbProjectInfoSplit. class,gbProjectInfoSplitList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出国拨项目信息拆分导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版国拨项目信息拆分文件", notes = "导出导入模版国拨项目信息拆分文件")
    @SysLog("导出国拨项目信息拆分导入模版文件")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/gbProjectInfoSplit.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 分页查询已审批结束的国拨项目
     * @param page 分页对象
     * @param gbProjectInfoSplit 国拨项目信息拆分管理
     * @return
     */
    @ApiOperation(value = "分页查询已审批结束的国拨项目", notes = "分页查询已审批结束的国拨项目")
    @SysLog("分页查询已审批结束的国拨项目" )
    @GetMapping("/selectProjectList" )
    public R selectProjectList(Page page, GbProjectInfoSplit gbProjectInfoSplit) {
        String claimType = WebUtils.getParameterMap().get("claimType");
        JxcmccUser jxcmccUser = SecurityUtils.getUser();
        String orgCodeShort = jxcmccUser.getUser().getOrgCodeShort();
        QueryWrapper queryWrapper = BeanHelperUtil.setQueryWrapper(gbProjectInfoSplit);
        if(!SecurityUtils.isAdmin()) {
            queryWrapper.eq("COMPANY_CODE", orgCodeShort);
        }
        queryWrapper.eq("DELETE_FLAG","0");
        queryWrapper.eq("PROCESS_STATUS","2");
        if("T0131".equals(claimType)){
            queryWrapper.apply("NVL(COLLECTION_CLAIM_AMOUNT,0) < SPLIT_AMOUNT");
        }
        return R.ok(gbProjectInfoSplitService.page(page, queryWrapper));
    }
}
