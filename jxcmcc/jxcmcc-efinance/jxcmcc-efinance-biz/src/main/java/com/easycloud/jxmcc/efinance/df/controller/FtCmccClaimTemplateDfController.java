/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.efinance.df.entity.FtCmccClaimTemplateDf;
import com.easycloud.jxmcc.efinance.df.service.FtCmccClaimTemplateDfService;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;


/**
 * 党费业务大小类对应表
 *
 * <AUTHOR>
 * @date 2022-08-12 12:04:12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ftcmccclaimtemplatedf" )
@Api(value = "ftcmccclaimtemplatedf", tags = "党费业务大小类对应表管理")
public class FtCmccClaimTemplateDfController {

    private final  FtCmccClaimTemplateDfService ftCmccClaimTemplateDfService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param ftCmccClaimTemplateDf 党费业务大小类对应表
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询党费业务大小类对应表" )
    @GetMapping("/page" )
    public R getFtCmccClaimTemplateDfPage(Page page, FtCmccClaimTemplateDf ftCmccClaimTemplateDf) {
        return R.ok(ftCmccClaimTemplateDfService.page(page, Wrappers.query(ftCmccClaimTemplateDf)));
    }

    /**
     *    获取所有报账单列表
     * @return R
     */
    @SysLog("获取所有报账单列表" )
    @ApiOperation(value = "获取所有报账单列表", notes = "获取所有报账单列表")
    @GetMapping(value = "/getAllActivity")
    public R getAllActivity() {
        return  R.ok(ftCmccClaimTemplateDfService.getAllActivity());
    }




    /**
     * 通过id查询党费业务大小类对应表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询党费业务大小类对应表" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(ftCmccClaimTemplateDfService.getById(id));
    }

    /**
     * 新增党费业务大小类对应表
     * @param ftCmccClaimTemplateDf 党费业务大小类对应表
     * @return R
     */
    @ApiOperation(value = "新增党费业务大小类对应表", notes = "新增党费业务大小类对应表")
    @SysLog("新增党费业务大小类对应表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('df_ftcmccclaimtemplatedf_add')" )
    public R save(@RequestBody FtCmccClaimTemplateDf ftCmccClaimTemplateDf) {
        return R.ok(ftCmccClaimTemplateDfService.save(ftCmccClaimTemplateDf));
    }

    /**
     * 修改党费业务大小类对应表
     * @param ftCmccClaimTemplateDf 党费业务大小类对应表
     * @return R
     */
    @ApiOperation(value = "修改党费业务大小类对应表", notes = "修改党费业务大小类对应表")
    @SysLog("修改党费业务大小类对应表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('df_ftcmccclaimtemplatedf_edit')" )
    public R updateById(@RequestBody FtCmccClaimTemplateDf ftCmccClaimTemplateDf) {
        return R.ok(ftCmccClaimTemplateDfService.updateById(ftCmccClaimTemplateDf));
    }

    /**
     * 通过id删除党费业务大小类对应表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除党费业务大小类对应表", notes = "通过id删除党费业务大小类对应表")
    @SysLog("通过id删除党费业务大小类对应表" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('df_ftcmccclaimtemplatedf_del')" )
    public R removeById(@PathVariable String id) {
        return R.ok(ftCmccClaimTemplateDfService.removeById(id));
    }


   /**
     * 导入党费业务大小类对应表
     * @return R
     */
    @ApiOperation(value = "导入党费业务大小类对应表", notes = "导入党费业务大小类对应表")
    @SysLog("导入党费业务大小类对应表" )
    @PreAuthorize("@pms.hasPermission('df_ftcmccclaimtemplatedf_import')" )
    @FileImportLogAnnotation(busTable = "党费业务大小类对应表",busSystem = "df")
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return ftCmccClaimTemplateDfService.importExcel(file,importParams);
    }

    /**
     * 导出党费业务大小类对应表
     * @return R
     */
    @ApiOperation(value = "导出党费业务大小类对应表", notes = "导出党费业务大小类对应表")
    @SysLog("导出党费业务大小类对应表")
    @PreAuthorize("@pms.hasPermission('df_ftcmccclaimtemplatedf_export')")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, FtCmccClaimTemplateDf ftCmccClaimTemplateDf) {
        List<FtCmccClaimTemplateDf>  ftCmccClaimTemplateDfList = ftCmccClaimTemplateDfService.list(Wrappers.lambdaQuery(ftCmccClaimTemplateDf));
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("党费业务大小类对应表列表", "党费业务大小类对应表"),
                FtCmccClaimTemplateDf. class,ftCmccClaimTemplateDfList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出党费业务大小类对应表导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版党费业务大小类对应表文件", notes = "导出导入模版党费业务大小类对应表文件")
    @SysLog("导出党费业务大小类对应表导入模版文件")
    @PreAuthorize("@pms.hasPermission('df_ftcmccclaimtemplatedf_export')")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/ftCmccClaimTemplateDf.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
