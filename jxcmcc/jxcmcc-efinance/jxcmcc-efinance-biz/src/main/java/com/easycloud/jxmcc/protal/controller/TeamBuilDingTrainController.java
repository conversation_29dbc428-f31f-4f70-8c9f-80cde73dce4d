package com.easycloud.jxmcc.protal.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.util.WebUtils;
import com.easycloud.jxcmcc.common.data.builder.QueryWrapperBuilder;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.efinance.service.FTacctBaseServiceI;
import com.easycloud.jxmcc.efinance.util.TCmccClaimUtil;
import com.easycloud.jxmcc.protal.entity.teambuildingentity.*;
import com.easycloud.jxmcc.protal.service.teambuildingservice.*;
import com.easycloud.jxmcc.protal.util.BeanHelperUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@RestController
@AllArgsConstructor
@RequestMapping("/teamBuilDingFinanceTrainController" )
@Api(value = "teamBuilDingFinanceTrainController", tags = "团队建设培训学习信息")
public class TeamBuilDingTrainController {

    private final FtProtalVplusPointsInfoService ftProtalVplusPointsInfoService;
    private final FtProtalTeachingActivInfoService ftProtalTeachingActivInfoService;
    private final FtProtalOtherTrainInfoService ftProtalOtherTrainInfoService;
    private final FtProtalTrainInfoService ftProtalTrainInfoServicel;
    private final FTacctBaseServiceI fTacctBaseServiceI;
    private final FtProtalTrainStudentInfoService ftProtalTrainStudentInfoService;
    private final FtProtalTrainTeacherInfoService ftProtalTrainTeacherInfoService;

    /**
     * V+分模块
     * */
    @ApiOperation(value = "根据id查询V+分信息", notes = "根据id查询V+分信息")
    @SysLog("根据id查询V+分信息" )
    @GetMapping("/getVplusById")
    public R getVplusById(@RequestParam("id") String id){
        return R.ok(ftProtalVplusPointsInfoService.getById(id));
    }

    @ApiOperation(value = "根据id查询V+分信息", notes = "根据id查询V+分信息")
    @SysLog("根据id查询V+分信息" )
    @GetMapping("/removeVplusById")
    @PreAuthorize("@pms.hasPermission('portal_v_plus_del')" )
    public R removeVplusById(@RequestParam("id") String id){
        return R.ok(ftProtalVplusPointsInfoService.removeById(id));
    }

    /*
     * 教研活动模块
     * */
    @ApiOperation(value = "添加教研活动模块信息", notes = "添加教研活动模块信息")
    @SysLog("添加教研活动模块信息" )
    @RequestMapping("/saveResearch")
    @PreAuthorize("@pms.hasPermission('portal_teaching_activities_add')" )
    public R saveResearch(@RequestBody FtProtalTeachingActivInfo ftProtalTeachingActivInfo){
        if (StringUtils.isNotBlank(ftProtalTeachingActivInfo.getTeacherOrgCode())){
            ftProtalTeachingActivInfo.setTeacherOrgName(ftProtalTeachingActivInfo.getTeacherOrgCode());
            ftProtalTeachingActivInfo.setTeacherOrgCode(TCmccClaimUtil.COMPANYNAME_MAP.get(ftProtalTeachingActivInfo.getTeacherOrgCode()));
        }
        return R.ok(ftProtalTeachingActivInfoService.save(ftProtalTeachingActivInfo));
    }

    @ApiOperation(value = "根据id查询教研活动信息", notes = "根据id查询教研活动信息")
    @SysLog("根据id查询教研活动信息" )
    @GetMapping("/getResearchById")
    public R getResearchById(@RequestParam("id") String id){
        return R.ok(ftProtalTeachingActivInfoService.getById(id));
    }

    @ApiOperation(value = "修改教研活动信息", notes = "修改教研活动信息")
    @SysLog("修改教研活动信息" )
    @PostMapping("/updateResearch")
    @PreAuthorize("@pms.hasPermission('portal_teaching_activities_edit')" )
    public R updateResearch(@RequestBody FtProtalTeachingActivInfo ftProtalTeachingActivInfo){
        return R.ok(ftProtalTeachingActivInfoService.updateById(ftProtalTeachingActivInfo));
    }

    @ApiOperation(value = "删除教研活动信息", notes = "删除教研活动信息")
    @SysLog("删除教研活动信息" )
    @GetMapping("/deleteResearch")
    @PreAuthorize("@pms.hasPermission('portal_teaching_activities_del')" )
    public R deleteResearch(@RequestParam("id") String id){
        return R.ok(ftProtalTeachingActivInfoService.removeById(id));
    }

    /**
     * 财务服务支撑导出
     * @return R
     */
    @ApiOperation(value = "教研活动导出", notes = "教研活动导出")
    @SysLog("教研活动导出")
    @GetMapping(value = "/exportJY")
    public void exportExcelJY(HttpServletRequest request, HttpServletResponse response, FtProtalTeachingActivInfo ftProtalTeachingActivInfo) {
        List<FtProtalTeachingActivInfo> ftProtalTeachingActivInfoList=new ArrayList<>();   //导出Excel
        try {
            Map<String,Object> map = new HashMap();
            map.put("list",ftProtalTeachingActivInfoList);
            TemplateExportParams params = new TemplateExportParams("templates/poi/robot/TeachingActiv.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * v+分导出模板
     * @return R
     */
    @ApiOperation(value = "v+分导出模板", notes = "v+分导出模板")
    @SysLog("v+分导出模板")
    @GetMapping(value = "/exportV")
    public void exportJY(HttpServletRequest request, HttpServletResponse response, FtProtalVplusPointsInfo ftProtalVplusPointsInfo) {
        List<FtProtalVplusPointsInfo> ftProtalVplusPointsInfoArrayList=new ArrayList<>();   //导出Excel
        try {
            Map<String,Object> map = new HashMap();
            map.put("list",ftProtalVplusPointsInfoArrayList);
            TemplateExportParams params = new TemplateExportParams("templates/poi/robot/VplusPointsInfo.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 员工培训时长统计
     * @return R
     */
    @ApiOperation(value = "员工培训时长统计", notes = "员工培训时长统计")
    @SysLog("员工培训时长统计")
    @GetMapping(value = "/exportKSC")
    public void exportKSC(HttpServletRequest request, HttpServletResponse response, FtProtalOtherTrainInfo ftProtalOtherTrainInfo) {
        List<FtProtalOtherTrainInfo> ftProtalOtherTrainInfoList=new ArrayList<>();   //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("员工培训时长统计","员工培训时长统计"),
                    FtProtalOtherTrainInfo.class,ftProtalOtherTrainInfoList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 培训时长统计模块
     * */
    @ApiOperation(value = "添加培训时长信息", notes = "添加培训时长信息")
    @SysLog("添加培训时长信息" )
    @RequestMapping("/saveDuration")
    @PreAuthorize("@pms.hasPermission('portal_training_duration_add')" )
    public R saveDuration(@RequestBody FtProtalOtherTrainInfo ftProtalOtherTrainInfo){
        JxcmccUser user = SecurityUtils.getUser();
        ftProtalOtherTrainInfo.setUpdateEmployeeName(user.getRealname());
        ftProtalOtherTrainInfo.setUpdateEmployeeCode(user.getUser().getWorkNo());
        if (StringUtils.isNotBlank(ftProtalOtherTrainInfo.getOrgCode())){
            ftProtalOtherTrainInfo.setOrgName(TCmccClaimUtil.COMPANY_MAP.get(ftProtalOtherTrainInfo.getOrgCode()));
        }
        return R.ok(ftProtalOtherTrainInfoService.save(ftProtalOtherTrainInfo));
    }

    @ApiOperation(value = "删除培训时长信息", notes = "删除培训时长信息")
    @SysLog("删除培训时长信息" )
    @GetMapping("/deleteDurationById")
    @PreAuthorize("@pms.hasPermission('portal_training_duration_del')" )
    public R deleteDurationById(@RequestParam("id") String id){
        return R.ok(ftProtalOtherTrainInfoService.removeById(id));
    }

    /**
     * 培训时长统计导出
     * @return R
     */
    @ApiOperation(value = "培训时长统计导出", notes = "培训时长统计导出")
    @SysLog("培训时长统计导出")
    @GetMapping(value = "/exportPXSC")
    @PreAuthorize("@pms.hasPermission('portal_training_duration_export')" )
    public void exportExcelPXSC(HttpServletRequest request, HttpServletResponse response, FtProtalOtherTrainInfo ftProtalOtherTrainInfo) {
        JxcmccUser user = SecurityUtils.getUser();
        //导出
        List<FtProtalOtherTrainInfo> ftProtalOtherTrainInfoList=ftProtalOtherTrainInfoService.list(Wrappers.lambdaQuery(FtProtalOtherTrainInfo.class)
                .eq(!"303710".equals(user.getOrgCode()),FtProtalOtherTrainInfo::getOrgCode,user.getOrgCode())
                .eq(StringUtils.isNotBlank(ftProtalOtherTrainInfo.getOrgCode()) && "303710".equals(user.getOrgCode()),FtProtalOtherTrainInfo::getOrgCode,ftProtalOtherTrainInfo.getOrgCode())
                .like(StringUtils.isNotBlank(ftProtalOtherTrainInfo.getStudentName()),FtProtalOtherTrainInfo::getStudentName,ftProtalOtherTrainInfo.getStudentName())
                .ge(ObjectUtil.isNotEmpty(ftProtalOtherTrainInfo.getTrainBeginTime()),FtProtalOtherTrainInfo::getTrainBeginTime,ftProtalOtherTrainInfo.getTrainBeginTime())
                .le(ObjectUtil.isNotEmpty(ftProtalOtherTrainInfo.getTrainEndTime()),FtProtalOtherTrainInfo::getTrainEndTime,ftProtalOtherTrainInfo.getTrainEndTime()));   //导出Excel
        try {
            ExportParams exportParams = new ExportParams();
            exportParams.setTitle("员工培训时长明细");
            exportParams.setSecondTitle("导出时间:"+new DateTime());
            exportParams.setSheetName("培训时长统计");
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams,
                    FtProtalOtherTrainInfo.class,ftProtalOtherTrainInfoList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /*
     * 创建培训项目模块
     * */
    @ApiOperation(value = "添加创建培训项目", notes = "添加创建培训项目信息")
    @SysLog("添加创建培训项目信息" )
    @RequestMapping("/saveTrainS")
    @PreAuthorize("@pms.hasPermission('portal_training_program_add')" )
    public R saveTrainS(@RequestBody FtProtalTrainInfo ftProtalTrainInfo) throws Exception {
        JxcmccUser user = SecurityUtils.getUser();
        ftProtalTrainInfo.setLastUpdateTime(new Date());
        ftProtalTrainInfo.setStatus("step10");
        ftProtalTrainInfo.setUpdateEmployeeName(user.getRealname());
        if("计划外(无项目编号)".equals(ftProtalTrainInfo.getProjectType())&& StringUtils.isBlank(ftProtalTrainInfo.getTrainProjectNum())){
            ftProtalTrainInfo.setTrainProjectNum(fTacctBaseServiceI.getTacctNumber("TR"));
        }
        if (StringUtils.isNotBlank(ftProtalTrainInfo.getOrgCode())){
            ftProtalTrainInfo.setOrgName(TCmccClaimUtil.COMPANY_MAP.get(ftProtalTrainInfo.getOrgCode()));
        }
        return R.ok(ftProtalTrainInfoServicel.save(ftProtalTrainInfo));
    }

    @ApiOperation(value = "根据id查询创建培训项目信息", notes = "根据id查询创建培训项目信息")
    @SysLog("根据id查询创建培训项目信息" )
    @GetMapping("/getTrainSById")
    public R getTrainSById(@RequestParam("id") String id){
        return R.ok(ftProtalTrainInfoServicel.getById(id));
    }

    @ApiOperation(value = "修改创建培训项目信息", notes = "修改创建培训项目信息")
    @SysLog("修改创建培训项目信息" )
    @PostMapping("/updateTrainS")
    @PreAuthorize("@pms.hasPermission('portal_training_program_edit')" )
    public R updateTrainS(@RequestBody FtProtalTrainInfo ftProtalTrainInfo) throws Exception {
        if("计划外(无项目编号)".equals(ftProtalTrainInfo.getProjectType())&& StringUtils.isBlank(ftProtalTrainInfo.getTrainProjectNum())){
            ftProtalTrainInfo.setTrainProjectNum(fTacctBaseServiceI.getTacctNumber("TR"));
        }
        return R.ok(ftProtalTrainInfoServicel.updateById(ftProtalTrainInfo));
    }

    @ApiOperation(value = "删除创建培训项目信息", notes = "删除创建培训项目信息")
    @SysLog("删除创建培训项目信息" )
    @GetMapping("/deleteTrainS")
    public R deleteTrainS(@RequestParam("id") String id){
        return R.ok(ftProtalTrainInfoServicel.removeById(id));
    }

    /**
     * 财务服务支撑导出
     * @return R
     */
    @ApiOperation(value = "创建培训项目导出", notes = "创建培训项目导出")
    @SysLog("创建培训项目导出")
    @GetMapping(value = "/exportXM")
    @PreAuthorize("@pms.hasPermission('portal_training_program_export')" )
    public void exportExcelXM(HttpServletRequest request, HttpServletResponse response, FtProtalTrainInfo ftProtalTrainInfo) {
        QueryWrapper build = BeanHelperUtil.setQueryWrapper(ftProtalTrainInfo);
        JxcmccUser user = SecurityUtils.getUser();
        if(!"303710".equals(user.getOrgCode())){
            build.eq("org_code",user.getOrgCode());
        }
        List<FtProtalTrainInfo> ftProtalTrainInfoList=ftProtalTrainInfoServicel.list(build);   //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("创建培训项目", "创建培训项目"),
                    FtProtalTrainInfo.class,ftProtalTrainInfoList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     *  培训项目文档录入
     * */
    @ApiOperation(value = "培训项目文档培训学生", notes = "培训项目文档培训学生")
    @SysLog("培训项目文档培训学生" )
    @GetMapping(value ="/getLecturerList" )
    public R  getLecturerList(Page page,FtProtalTrainStudentInfo ftProtalTrainStudentInfo){
        QueryWrapper build = QueryWrapperBuilder.create(FtProtalTrainStudentInfo.class).build(WebUtils.getParameterMap());
        return R.ok(ftProtalTrainStudentInfoService.page(page,build));
    }

    @ApiOperation(value = "培训项目文档录入", notes = "培训项目文档录入")
    @SysLog("培训项目文档录入" )
    @RequestMapping("/addLecturer")
    public R addLecturer(@RequestBody FtProtalTrainStudentInfo ftProtalTrainStudentInfo) throws Exception {
        if (StringUtils.isNotBlank(ftProtalTrainStudentInfo.getStudentCode())){
            ftProtalTrainStudentInfo.setStudentOrgName(TCmccClaimUtil.COMPANY_MAP.get(ftProtalTrainStudentInfo.getStudentOrgCode()));
        }
        return R.ok(ftProtalTrainStudentInfoService.save(ftProtalTrainStudentInfo));
    }

    @ApiOperation(value = "根据id查询培训项目文档培训学生信息", notes = "根据id查询培训项目文档培训学生信息")
    @SysLog("根据id查询培训项目文档培训学生信息" )
    @GetMapping("/getLecturerById")
    public R getLecturerById(@RequestParam("id") String id){
        return R.ok(ftProtalTrainStudentInfoService.getById(id));
    }

    @ApiOperation(value = "修改培训项目文档培训学生信息", notes = "修改培训项目文档培训学生信息")
    @SysLog("修改培训项目文档培训学生信息" )
    @PostMapping("/updateLecturer")
    public R updateLecturer(@RequestBody FtProtalTrainStudentInfo ftProtalTrainStudentInfo) throws Exception {
        return R.ok(ftProtalTrainStudentInfoService.updateById(ftProtalTrainStudentInfo));
    }

    @ApiOperation(value = "删除培训项目文档培训学生信息", notes = "删除培训项目文档培训学生信息")
    @SysLog("删除培训项目文档培训学生信息" )
    @GetMapping("/deleteLecturer")
    public R deleteLecturer(@RequestParam("id") String id){
        return R.ok(ftProtalTrainStudentInfoService.removeById(id));
    }

    @ApiOperation(value = "创建培训项目培训学员导出模板", notes = "创建培训项目培训学员导出模板")
    @SysLog("创建培训项目培训学员导出模板")
    @GetMapping(value = "/exportXS")
    public void exportXS(HttpServletRequest request, HttpServletResponse response, FtProtalTrainStudentInfo ftProtalTrainStudentInfo) {
        List<FtProtalTrainStudentInfo> ftProtalTrainStudentInfoList=new ArrayList<>();   //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("培训项目文档培训学生信息导出", "培训项目文档培训学生信息导出"),
                    FtProtalTrainStudentInfo.class,ftProtalTrainStudentInfoList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     *  培训项目文档录入 教师
     * */
    @ApiOperation(value = "培训项目文档培训教师", notes = "培训项目文档培训教师")
    @SysLog("培训项目文档培训教师" )
    @GetMapping(value ="/getTeacherList" )
    public R  getTeacherList(Page page, FtProtalTrainTeacherInfo ftProtalTrainTeacherInfo){
        QueryWrapper build = QueryWrapperBuilder.create(FtProtalTrainStudentInfo.class).build(WebUtils.getParameterMap());
        return R.ok(ftProtalTrainTeacherInfoService.page(page,build));
    }

    @ApiOperation(value = "培训项目文档录入教师", notes = "培训项目文档录入教师")
    @SysLog("培训项目文档录入教师" )
    @RequestMapping("/addTeacher")
    public R addTeacher(@RequestBody FtProtalTrainTeacherInfo ftProtalTrainTeacherInfo) throws Exception {
        if (StringUtils.isNotBlank(ftProtalTrainTeacherInfo.getTeacherOrgCode())){
            ftProtalTrainTeacherInfo.setTeacherOrgName(TCmccClaimUtil.COMPANY_MAP.get(ftProtalTrainTeacherInfo.getTeacherOrgCode()));
        }
        return R.ok(ftProtalTrainTeacherInfoService.save(ftProtalTrainTeacherInfo));
    }

    @ApiOperation(value = "根据id查询培训项目文档培训教师信息", notes = "根据id查询培训项目文档培训教师信息")
    @SysLog("根据id查询培训项目文档培训教师信息" )
    @GetMapping("/getTeacheryId")
    public R getTeacheryId(@RequestParam("id") String id){
        return R.ok(ftProtalTrainTeacherInfoService.getById(id));
    }

    @ApiOperation(value = "修改培训项目文档培训教师信息", notes = "修改培训项目文档培训教师信息")
    @SysLog("修改培训项目文档培训教师信息" )
    @PostMapping("/updateTeacher")
    public R updateTeacher(@RequestBody FtProtalTrainTeacherInfo ftProtalTrainTeacherInfo) throws Exception {
        return R.ok(ftProtalTrainTeacherInfoService.updateById(ftProtalTrainTeacherInfo));
    }

    @ApiOperation(value = "删除培训项目文档培训教师信息", notes = "删除培训项目文档培训教师信息")
    @SysLog("删除培训项目文档培训教师信息" )
    @GetMapping("/deleteTeacher")
    public R deleteTeacher(@RequestParam("id") String id){
        return R.ok(ftProtalTrainTeacherInfoService.removeById(id));
    }

    @ApiOperation(value = "培训项目文档培训教师信息导出模板", notes = "培训项目文档培训教师信息导出模板")
    @SysLog("培训项目文档培训教师信息导出模板")
    @GetMapping(value = "/exportLS")
    public void exportLS(HttpServletRequest request, HttpServletResponse response, FtProtalTrainTeacherInfo ftProtalTrainTeacherInfo) {
        List<FtProtalTrainTeacherInfo> ftProtalTrainTeacherInfoList=new ArrayList<>();   //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("培训项目文档培训教师信息", "课时不用填写,根据培训日期自动算出(此行保留)","培训项目文档培训教师信息"),
                    FtProtalTrainTeacherInfo.class,ftProtalTrainTeacherInfoList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导入V+分信息", notes = "导入V+分信息")
    @SysLog("导入V+分信息" )
    @PostMapping("/importExcelV")
    @PreAuthorize("@pms.hasPermission('portal_v_plus_import')" )
    public R importExcelV(MultipartFile file, String period) throws Exception {
        return R.ok(ftProtalVplusPointsInfoService.importExcel(file,period));
    }

    @ApiOperation(value = "导入教研活动信息", notes = "导入教研活动信息")
    @SysLog("导入教研活动信息" )
    @PostMapping("/importExcelJY")
    @PreAuthorize("@pms.hasPermission('portal_teaching_activities_import')" )
    public R importExcelJY(MultipartFile file, String activType) throws Exception {
        return R.ok(ftProtalTeachingActivInfoService.importExcel(file,activType));
    }

    @ApiOperation(value = "员工时长导入", notes = "员工时长导入")
    @SysLog("员工时长导入" )
    @PostMapping("/importExcelSC")
    @PreAuthorize("@pms.hasPermission('portal_training_duration_import')" )
    public R importExcelSC(MultipartFile file, String trainYear) throws Exception {
        return R.ok(ftProtalOtherTrainInfoService.importExcel(file,trainYear));
    }


    @ApiOperation(value = "培训项目补充信息导入培训教师", notes = "培训项目补充信息导入培训教师")
    @SysLog("培训项目补充信息导入培训教师" )
    @PostMapping("/importExcelPXJS")
    public R importExcelPXJS(MultipartFile file,String trainId) throws Exception {
        return R.ok(ftProtalTrainTeacherInfoService.importExcel(file,trainId));
    }

    @ApiOperation(value = "培训项目补充信息导入培训学员", notes = "培训项目补充信息导入培训学员")
    @SysLog("培训项目补充信息导入培训学员" )
    @PostMapping("/importExcelPXXY")
    public R importExcelPXXY(MultipartFile file,String trainId) throws Exception {
        return R.ok(ftProtalTrainStudentInfoService.importExcel(file,trainId));
    }

    @ApiOperation(value = "获取当前用户人信息", notes = "获取当前用户人信息")
    @SysLog("获取当前用户人信息" )
    @GetMapping(value ="/getCPLoginperson" )
    public R  getCPLoginperson(){
        JxcmccUser user = SecurityUtils.getUser();
        QueryWrapper build = QueryWrapperBuilder.<FtProtalTrainInfo>create(FtProtalTrainInfo.class).build(WebUtils.getParameterMap());
        build.eq("project_Employee_Name",user.getRealname());
        return R.ok(ftProtalTrainInfoServicel.list(build));
    }

}
