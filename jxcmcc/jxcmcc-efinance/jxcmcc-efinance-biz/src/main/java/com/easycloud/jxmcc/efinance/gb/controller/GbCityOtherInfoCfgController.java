/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.BeanHelperUtil;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.util.WebUtils;
import com.easycloud.jxcmcc.common.data.builder.QueryWrapperBuilder;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.efinance.gb.entity.GbCityOtherInfoCfg;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfo;
import com.easycloud.jxmcc.efinance.gb.service.GbCityOtherInfoCfgService;
import com.easycloud.jxmcc.efinance.util.TCmccClaimUtil;
import io.seata.common.util.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.HashMap;
import java.util.Map;
import java.util.List;


/**
 * 地市其它信息配置
 *
 * <AUTHOR> code efinance
 * @date 2022-10-19 10:27:16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/gbcityotherinfocfg" )
@Api(value = "gbcityotherinfocfg", tags = "地市其它信息配置管理")
public class GbCityOtherInfoCfgController {

    private final GbCityOtherInfoCfgService gbCityOtherInfoCfgService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param gbCityOtherInfoCfg 地市其它信息配置
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询地市其它信息配置" )
    @GetMapping("/page" )
    public R getGbCityOtherInfoCfgPage(Page page, GbCityOtherInfoCfg gbCityOtherInfoCfg) {
        if("303700".equals(gbCityOtherInfoCfg.getCompanyCode())){
            gbCityOtherInfoCfg.setCompanyCode("");
        }
        QueryWrapper queryWrapper = BeanHelperUtil.setQueryWrapper(gbCityOtherInfoCfg);
        queryWrapper.eq("DELETE_FLAG","0");
        queryWrapper.orderByDesc("CREATED_TIME");
        return R.ok(gbCityOtherInfoCfgService.page(page, queryWrapper));
    }


    /**
     * 通过id查询地市其它信息配置
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询地市其它信息配置" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(gbCityOtherInfoCfgService.getById(id));
    }

    /**
     * 新增地市其它信息配置
     * @param gbCityOtherInfoCfg 地市其它信息配置
     * @return R
     */
    @ApiOperation(value = "新增地市其它信息配置", notes = "新增地市其它信息配置")
    @SysLog("新增地市其它信息配置" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('efinance_gbcityotherinfocfg_add')" )
    public R save(@RequestBody GbCityOtherInfoCfg gbCityOtherInfoCfg) {
        gbCityOtherInfoCfg.setDeleteFlag("0");
        gbCityOtherInfoCfg.setId(null);
        gbCityOtherInfoCfg.setCompanyName(BeanHelperUtil.companyMap.get(gbCityOtherInfoCfg.getCompanyCode()));
        return R.ok(gbCityOtherInfoCfgService.save(gbCityOtherInfoCfg));
    }

    /**
     * 修改地市其它信息配置
     * @param gbCityOtherInfoCfg 地市其它信息配置
     * @return R
     */
    @ApiOperation(value = "修改地市其它信息配置", notes = "修改地市其它信息配置")
    @SysLog("修改地市其它信息配置" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('efinance_gbcityotherinfocfg_edit')" )
    public R updateById(@RequestBody GbCityOtherInfoCfg gbCityOtherInfoCfg) {
        gbCityOtherInfoCfg.setCompanyName(BeanHelperUtil.companyMap.get(gbCityOtherInfoCfg.getCompanyCode()));
        return R.ok(gbCityOtherInfoCfgService.updateById(gbCityOtherInfoCfg));
    }

    /**
     * 通过id删除地市其它信息配置
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除地市其它信息配置", notes = "通过id删除地市其它信息配置")
    @SysLog("通过id删除地市其它信息配置" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('efinance_gbcityotherinfocfg_del')" )
    public R removeById(@PathVariable String id) {
        GbCityOtherInfoCfg gbCityOtherInfoCfg = gbCityOtherInfoCfgService.getById(id); 
        gbCityOtherInfoCfg.setDeleteFlag("1");
        return R.ok(gbCityOtherInfoCfgService.updateById(gbCityOtherInfoCfg));
    }


   /**
     * 导入地市其它信息配置
     * @return R
     */
    @ApiOperation(value = "导入地市其它信息配置", notes = "导入地市其它信息配置")
    @SysLog("导入地市其它信息配置" )
    @PreAuthorize("@pms.hasPermission('efinance_gbcityotherinfocfg_import')" )
    @PostMapping(value = "/importExcel")
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return gbCityOtherInfoCfgService.importExcel(file,importParams);
    }

    /**
     * 导出地市其它信息配置
     * @return R
     */
    @ApiOperation(value = "导出地市其它信息配置", notes = "导出地市其它信息配置")
    @SysLog("导出地市其它信息配置")
    @PreAuthorize("@pms.hasPermission('efinance_gbcityotherinfocfg_export')")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, GbCityOtherInfoCfg gbCityOtherInfoCfg) {
        QueryWrapper queryWrapper = BeanHelperUtil.setQueryWrapper(gbCityOtherInfoCfg);
        queryWrapper.eq("DELETE_FLAG","0");
        queryWrapper.orderByDesc("CREATED_TIME");
        List<GbCityOtherInfoCfg>  gbCityOtherInfoCfgList = gbCityOtherInfoCfgService.list(queryWrapper);
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("地市其它信息配置列表", "地市其它信息配置"),
                GbCityOtherInfoCfg. class,gbCityOtherInfoCfgList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出地市其它信息配置导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版地市其它信息配置文件", notes = "导出导入模版地市其它信息配置文件")
    @SysLog("导出地市其它信息配置导入模版文件")
    @PreAuthorize("@pms.hasPermission('efinance_gbcityotherinfocfg_export')")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/gbCityOtherInfoTemplate.xlsx", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
