/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.gb.service.impl;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectClaimLog;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfoSplit;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectInfo;
import com.easycloud.jxmcc.efinance.gb.mapper.GbProjectInfoSplitMapper;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectClaimLogService;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectInfoService;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectInfoSplitService;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxmcc.efinance.gb.vo.GbProjectInfoSplitVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 国拨项目信息拆分
 *
 * <AUTHOR> code generator
 * @date 2022-10-20 11:24:12
 */
@Service
@Slf4j
public class GbProjectInfoSplitServiceImpl extends ServiceImpl<GbProjectInfoSplitMapper, GbProjectInfoSplit> implements GbProjectInfoSplitService {

    @Autowired
    private GbProjectInfoService gbProjectInfoService;
    @Autowired
    private GbProjectClaimLogService gbProjectClaimLogService;
    /**
    * 导入excel
    * @return
    */
    @Override
    public R<Integer> importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！" );
        }
        ImportParams params = new ImportParams();
        //解析导入EXCEL数据
        List<GbProjectInfoSplit> gbProjectInfoSplitList = ExcelImportUtil.importExcel(file.getInputStream(), GbProjectInfoSplit. class,
        params);
        this.saveBatch(gbProjectInfoSplitList);
        return R.ok(gbProjectInfoSplitList.size());
    }

    /**
     * 新增拆分项目
     *
     * @param gbProjectInfoSplit
     * @return
     */
    @Override
    public R saveGbProjectSplit(GbProjectInfoSplit gbProjectInfoSplit,String reType) {
        //公司编码不可为空
        if(StringUtils.isNotEmpty(gbProjectInfoSplit.getCompanyCode())){
            Boolean bool = this.findCodeVerify(gbProjectInfoSplit.getCompanyCode(),gbProjectInfoSplit.getId(),"add","0");
            if(bool){
                BigDecimal bigDecimal = this.splitProjectOneVerify(gbProjectInfoSplit.getId(),"add","0",reType);
                BigDecimal addBig = gbProjectInfoSplit.getSplitAmount();
                if(bigDecimal.compareTo(BigDecimal.ZERO) > 0) {
                    if (bigDecimal.compareTo(addBig) != -1) {
                        //获取项目信息
                        GbProjectInfo gbProjectInfo = gbProjectInfoService.getById(gbProjectInfoSplit.getId());
                        gbProjectInfo.setId(null);
                        gbProjectInfo.setCompanyCode(gbProjectInfoSplit.getCompanyCode());
                        gbProjectInfo.setCompanyName(gbProjectInfoSplit.getCompanyName());
                        GbProjectInfoSplit gbProSplit = new GbProjectInfoSplit();
                        BeanUtils.copyProperties(gbProjectInfo,gbProSplit);
                        gbProSplit.setClaimAmount(BigDecimal.ZERO);
                        gbProSplit.setSplitAmount(addBig);
                        gbProSplit.setClaimAmount(BigDecimal.ZERO);
                        gbProSplit.setClaimAmountZf(BigDecimal.ZERO);
                        gbProSplit.setCollectionClaimAmount(BigDecimal.ZERO);
                        return R.ok(this.save(gbProSplit));
                    }else{
                        return R.failed("拆分金额不可大于"+bigDecimal);
                    }
                }else{
                    return R.failed("项目批复金额已拆分完,不可再拆分!");
                }
            }
            return R.failed("该公司已存在，不可重复！");
        }

        return R.failed("公司编码不可为空！");
    }

    /**
     * 修改拆分项目
     *
     * @param gbProjectInfoSplit
     * @param reType  请求类型
     * @return
     */
    @Override
    public R updateByGbProjectSplit(GbProjectInfoSplit gbProjectInfoSplit,String reType) {
        //公司编码不可为空
        if(StringUtils.isNotEmpty(gbProjectInfoSplit.getCompanyCode())){
            Boolean bool = this.findCodeVerify(gbProjectInfoSplit.getCompanyCode(),gbProjectInfoSplit.getProcessId(),"update",gbProjectInfoSplit.getId());
            if(bool){
                BigDecimal bigDecimal = this.splitProjectOneVerify(gbProjectInfoSplit.getProcessId(),"update",gbProjectInfoSplit.getId(),reType);
                if(bigDecimal.compareTo(BigDecimal.ZERO) >= 0) {
                    if (bigDecimal.compareTo(gbProjectInfoSplit.getSplitAmount()) >= 0) {
                        if(gbProjectInfoSplit.getSplitAmount().compareTo(gbProjectInfoSplit.getClaimAmount()) >= 0){
                            return R.ok(this.updateById(gbProjectInfoSplit));
                        }else{
                            return R.failed("该公司已发起报账，拆分金额不可小于报账金额，不可小于:"+gbProjectInfoSplit.getClaimAmount().abs());
                        }
                    }else{
                        return R.failed("拆分金额不可大于"+bigDecimal);
                    }
                }else{
                    return R.failed("批复金额已用完,不可拆分!");
                }
            }
            return R.failed("该公司已存在，不可重复！");
        }
        return R.failed("公司编码不可为空！");
    }

    /**
     * 校验拆分金额
     * @param id  项目信息id
     * @param type  请求类型
     * @param upId  修改数据id(修改校验)
     * @param reType  校验类型（流程key）
     * @return
     */
    public BigDecimal splitProjectOneVerify(String id ,String type,String upId,String reType){
        BigDecimal bigDecimal = new BigDecimal(0);
        //获取项目信息
        GbProjectInfo gbProjectInfo = gbProjectInfoService.getById(id);
        bigDecimal = gbProjectInfo.getApprovedAmount();
        List<GbProjectInfoSplit> splits = new ArrayList<>();
        if("add".equals(type)){
            //获取所有拆分信息
            splits = this.list(Wrappers.lambdaQuery(GbProjectInfoSplit.class).eq(GbProjectInfoSplit::getProcessId,id));
        }else{
            //获取所有拆分信息
            splits = this.list(Wrappers.lambdaQuery(GbProjectInfoSplit.class).eq(GbProjectInfoSplit::getProcessId,id).ne(GbProjectInfoSplit::getId,upId));
        }
        BigDecimal bigSplitSum = new BigDecimal(0);
        for (GbProjectInfoSplit item:splits){
            bigSplitSum = bigSplitSum.add(item.getSplitAmount());
        }
        //判断是否我变更拆分
//        if ("gbSplitParentProject".equals(reType)){
//            bigSplitSum = BigDecimal.ZERO;
//            List<GbProjectClaimLog> gbProjectClaimLogList = gbProjectClaimLogService.list(Wrappers.lambdaQuery(GbProjectClaimLog.class)
//                    .eq(GbProjectClaimLog::getProjectNo,gbProjectInfo.getProjectNo())
//                    .isNull(GbProjectClaimLog::getIsClaim));
//            for (GbProjectClaimLog item:gbProjectClaimLogList){ //取报账金额
//                bigSplitSum = bigSplitSum.add(item.getClaimAmount());
//            }
//        }
        return bigDecimal.subtract(bigSplitSum);
    }

    /**
     * 校验地区编码是否可新增
     *
     * @param companyCode 地区编码
     * @param id          项目id
     * @param type        添加add/修改update类型
     * @param upId        修改数据id
     * @return
     */
    @Override
    public Boolean findCodeVerify(String companyCode, String id, String type, String upId) {
        List<GbProjectInfoSplit> splits = new ArrayList<>();
        if("add".equals(type)){
            //获取所有拆分信息
            splits = this.list(Wrappers.lambdaQuery(GbProjectInfoSplit.class).eq(GbProjectInfoSplit::getProcessId,id)
                    .eq(GbProjectInfoSplit::getCompanyCode,companyCode));
        }else{
            //获取所有拆分信息
            splits = this.list(Wrappers.lambdaQuery(GbProjectInfoSplit.class).eq(GbProjectInfoSplit::getProcessId,id)
                    .ne(GbProjectInfoSplit::getId,upId)
                    .eq(GbProjectInfoSplit::getCompanyCode,companyCode));
        }
        if (splits.size() > 0){
            return false;
        }
        return true;
    }

    /**
     * 校验报账金额
     *
     * @param gbProjectInfoSplit 拆分项目对象
     * @param applyAmount        本次报账金额
     * @return
     */
    @Override
    public BigDecimal checkSplitProjectClaim(GbProjectInfoSplit gbProjectInfoSplit, BigDecimal applyAmount) {
        List<GbProjectClaimLog> gbProjectClaimLogList = gbProjectClaimLogService.list(Wrappers.lambdaQuery(GbProjectClaimLog.class)
                .eq(GbProjectClaimLog::getProjectNo,gbProjectInfoSplit.getProjectNo())
                .isNull(GbProjectClaimLog::getIsClaim));
        //总报账金额
        BigDecimal sum = new BigDecimal(0);
        for (GbProjectClaimLog gbProjectClaimLog : gbProjectClaimLogList) {
            BigDecimal claimAmount = gbProjectClaimLog.getClaimAmount();
            sum = sum.add(claimAmount);
        }
        BigDecimal sumAmout = sum;
        //剩余可报账金额
        BigDecimal suttleAmout = gbProjectInfoSplit.getApprovedAmount().subtract(sumAmout);

        return suttleAmout.subtract(applyAmount);
    }

    /**
     * 分页查询拆分报账信息
     *
     * @param page
     * @param Vo
     * @return
     */
    @Override
    public Page<GbProjectInfoSplitVo> selectPage(IPage<GbProjectInfoSplitVo> page, GbProjectInfoSplitVo Vo) {
        if("T0132".equals(Vo.getRequestType())){
            return baseMapper.page(page, Vo);
        }else{
            return baseMapper.pagezf(page, Vo);
        }
    }

    /**
     * 分页查询拆分报账信息(未报账)
     *
     * @param page
     * @param gbProjectInfoSplit
     * @return
     */
    @Override
    public IPage<GbProjectInfoSplitVo> selectNoClaimPage(IPage<GbProjectInfoSplitVo> page, GbProjectInfoSplit gbProjectInfoSplit) {
        return baseMapper.pageNoClaim(page, gbProjectInfoSplit);
    }

    /**
     * 根据id删除拆分项目数据
     *
     * @param id
     * @return
     */
    @Override
    public R reById(String id) {
        int count = gbProjectClaimLogService.count(Wrappers.lambdaQuery(GbProjectClaimLog.class)
                .eq(GbProjectClaimLog::getDataId,id));
        //判断是否发起报账
        if(count > 0){
            return R.error("该公司已发起报账，不可删除！");
        }
        GbProjectInfoSplit gbProjectInfoSplit = this.getById(id);
        if("303710".equals(gbProjectInfoSplit.getCompanyCode())){
            return R.error("省公司拆分信息不可删除！");
        }
        return R.ok(this.removeById(id));
    }

    /**
     * 获取项目信息
     *
     * @param id
     * @return
     */
    @Override
    public GbProjectInfo getPro(String id) {
        GbProjectInfo gbProjectInfo = gbProjectInfoService.getById(id);
        List<GbProjectInfoSplit> gbProjectInfoSplitList = this.list(Wrappers.lambdaQuery(GbProjectInfoSplit.class)
                .eq(GbProjectInfoSplit::getProcessId,id));
        BigDecimal sumSplit = BigDecimal.ZERO;
        if(gbProjectInfoSplitList != null && gbProjectInfoSplitList.size() > 0){
            for (GbProjectInfoSplit item:gbProjectInfoSplitList){
                sumSplit = sumSplit.add(item.getSplitAmount());
            }
        }
        gbProjectInfo.setClaimAmount(gbProjectInfo.getApprovedAmount().subtract(sumSplit));
        return gbProjectInfo;
    }
}
