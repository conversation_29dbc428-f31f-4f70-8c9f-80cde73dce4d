/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 国拨项目信息拆分
 *
 * <AUTHOR> code generator
 * @date 2022-10-20 11:24:12
 */
@Data
@TableName("GB_PROJECT_INFO_SPLIT")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "国拨项目信息拆分")
public class GbProjectInfoSplit extends Model<GbProjectInfoSplit> {
private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "唯一标识")
    @Excel(name = "唯一标识")
    private String id;
    /**
     * 辅助报账单号
     */
    @ApiModelProperty(value = "辅助报账单号")
    @Excel(name = "辅助报账单号")
    private String claimNo;
    /**
     * 辅助报账单号
     */
    @ApiModelProperty(value = "辅助报账单号(工程类)")
    @Excel(name = "辅助报账单号（工程类报销）")
    private String claimNoGcl;
    /**
     * 辅助报账单号
     */
    @ApiModelProperty(value = "辅助报账单号(工程类支付)")
    @Excel(name = "辅助报账单号（工程类支付）")
    private String claimNoZf;
    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @Excel(name = "项目编号")
    private String projectNo;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @Excel(name = "项目名称")
    private String projectName;
    /**
     * 批复金额
     */
    @ApiModelProperty(value = "批复金额")
    @Excel(name = "批复金额")
    private BigDecimal approvedAmount;
    /**
     * 批复金额
     */
    @ApiModelProperty(value = "拆分金额")
    @Excel(name = "拆分金额")
    private BigDecimal splitAmount;
    /**
     * 地市编码
     */
    @ApiModelProperty(value = "地市编码")
    @Excel(name = "地市编码")
    private String companyCode;
    /**
     * 地市名称
     */
    @ApiModelProperty(value = "地市名称")
    @Excel(name = "地市名称")
    private String companyName;
    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门编码")
    @Excel(name = "部门编码")
    private String responsibleDeptCode;
    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @Excel(name = "部门名称")
    private String responsibleDeptName;
    /**
     * 责任人账号
     */
    @ApiModelProperty(value = "责任人账号")
    @Excel(name = "责任人账号")
    private String personLiableNo;
    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @Excel(name = "责任人")
    private String personLiable;
    /**
     * 项目类型;无线或传输
     */
    @ApiModelProperty(value = "项目类型;无线或传输")
    @Excel(name = "项目类型;无线或传输")
    private String projectType;
    /**
     * 流程ID
     */
    @ApiModelProperty(value = "流程ID")
    @Excel(name = "流程ID")
    private String processId;
    /**
     * 流程审核状态;(0=未发起,1=审批中,2=审批通过,3=审批未通过)
     */
    @ApiModelProperty(value = "流程审核状态;(0=未发起,1=审批中,2=审批通过,3=审批未通过)")
    @Excel(name = "流程审核状态;(0=未发起,1=审批中,2=审批通过,3=审批未通过)")
    private String processStatus;
    /**
     * 报账金额
     */
    @ApiModelProperty(value = "报账金额")
    @Excel(name = "报账金额")
    private BigDecimal claimAmount;
    /**
     * 支付报账金额
     */
    @ApiModelProperty(value = "支付报账金额")
    @Excel(name = "支付报账金额")
    private BigDecimal claimAmountZf;
    /**
     * 收款报账金额
     */
    @ApiModelProperty(value = "收款报账金额")
    @Excel(name = "收款报账金额")
    private BigDecimal collectionClaimAmount;
    /**
     * 预留字段1
     */
    @ApiModelProperty(value = "预留字段1-拆分流程ID")
    private String reserveField1;
    /**
     * 预留字段2
     */
    @ApiModelProperty(value = "预留字段2-拆分流程状态")
    private String reserveField2;
    /**
     * 预留字段3
     */
    @ApiModelProperty(value = "预留字段3")
    private String reserveField3;
    /**
     * 租户号
     */
    @ApiModelProperty(value = "租户号")
    private Integer tenantId;
    /**
     * 删除标识;删除标识(0=否,1=是)
     */
    @ApiModelProperty(value = "删除标识;删除标识(0=否,1=是)")
    private String deleteFlag;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updatedTime;
    }
