/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easycloud.jxmcc.efinance.gb.entity.GbCityOtherInfoCfg;
import org.apache.ibatis.annotations.Mapper;

/**
 * 地市其它信息配置
 *
 * <AUTHOR> code generator
 * @date 2022-10-19 10:27:16
 */
@Mapper
public interface GbCityOtherInfoCfgMapper extends BaseMapper<GbCityOtherInfoCfg> {

}
