/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 国拨资金支付台账
 *
 * <AUTHOR> code generator
 * @date 2022-10-27 09:43:07
 */
@Data
@TableName("GB_CAPITAL_PAYMENT_ACCOUNT")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "国拨资金支付台账")
public class GbCapitalPaymentAccount extends Model<GbCapitalPaymentAccount> {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "唯一标识")
    private String id;
    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @Excel(name = "项目编号" , width = 20)
    private String projectNo;
    /**
     * 项目总金额
     */
    @ApiModelProperty(value = "项目总金额")
    @Excel(name = "项目总金额" , width = 15)
    private String projectTotalAmount;
    /**
     * 已使用总额度（元）
     */
    @ApiModelProperty(value = "已使用总额度（元）")
    @Excel(name = "已使用总额度（元）" , width = 25)
    private String usedTotalAmount;
    /**
     * 剩余总额度（元）
     */
    @ApiModelProperty(value = "剩余总额度（元）")
    @Excel(name = "剩余总额度（元）" , width = 25)
    private String surplusTotalAmount;
    /**
     * 分配单位
     */
    @ApiModelProperty(value = "分配单位")
    @Excel(name = "分配单位" , width = 15)
    private String branchCompany;
    /**
     * 分配单位编码
     */
    @ApiModelProperty(value = "分配单位编码")
    private String branchCompanyCode;
    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    @Excel(name = "项目经理" , width = 15)
    private String projectManager;
    /**
     * 项目经理账号
     */
    @ApiModelProperty(value = "项目经理账号")
    private String projectManagerNo;
    /**
     * 分配额度（元）
     */
    @ApiModelProperty(value = "分配额度（元）")
    @Excel(name = "分配额度（元）" , width = 20)
    private String branchAmount;
    /**
     * 分配已使用额度（元）
     */
    @ApiModelProperty(value = "分配已使用额度（元）")
    @Excel(name = "分配已使用额度（元）" , width = 30)
    private String branchUsedAmount;
    /**
     * 分配剩余额度（元）
     */
    @ApiModelProperty(value = "分配剩余额度（元）")
    @Excel(name = "分配剩余额度（元）" , width = 25)
    private String branchSurplusAmount;
    /**
     * 报账单号
     */
    @ApiModelProperty(value = "报账单号")
    @Excel(name = "报账单号" , width = 30)
    private String claimNo;
    /**
     * 集团报账单号
     */
    @ApiModelProperty(value = "集团报账单号")
    @Excel(name = "集团报账单号" , width = 30)
    private String groupClaimNo;

    /**
     * 辅助报账单状态
     */
    @ApiModelProperty(value = "辅助报账单状态")
    @Excel(name = "辅助报账单状态" , width = 20)
    private String stateName;

    /**
     * 集团报账单状态（COMPLETED=已结束，NEW=新建....）
     */
    @ApiModelProperty(value = "集团报账单状态")
    @Excel(name = "集团报账单状态" , width = 20)
    private String groupStatus;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    @Excel(name = "供应商" , width = 30)
    private String supplier;
    /**
     * 本次支付（元）
     */
    @ApiModelProperty(value = "本次支付（元）")
    @Excel(name = "本次支付（元）" , width = 20)
    private String thisPayment;
    /**
     * 期间（月）
     */
    @ApiModelProperty(value = "期间（月）")
    @Excel(name = "期间（月）" , width = 15)
    private String period;
    /**
     * 账期
     */
    @ApiModelProperty(value = "账期")
    private String accountPeriod;

    /**
     * 辅助报账单状态编码（step10=起草，step0=流程结束）
     */
    @ApiModelProperty(value = "辅助报账单状态编码")
    private String stateCode;
    /**
     * 租户号
     */
    @ApiModelProperty(value = "租户号")
    private Integer tenantId;
    /**
     * 删除标识;删除标识(0=否,1=是)
     */
    @ApiModelProperty(value = "删除标识;删除标识(0=否,1=是)")
    private String deleteFlag;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedTime;

    @TableField(exist = false)
    private List<GbCapitalPaymentAccount> children;

}
