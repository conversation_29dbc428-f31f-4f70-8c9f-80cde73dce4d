package com.easycloud.jxmcc.efinance.df.claim.listener;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.easycloud.jxmcc.efinance.claim.listener.EmisTodoListener;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.efinance.util.TCmccClaimUtil;
import com.easycloud.jxmcc.eflow.vo.BaseEflowApiVo;
import com.easycloud.jxmcc.eflow.vo.ProcessEflowApiVo;
import com.easycloud.jxmcc.eflow.vo.TaskEflowApiVo;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class BaseListener extends EmisTodoDfListener {

    @Autowired
    private FtRmbsClaimDfService rmbsClaimService;

    @Override
    public Integer getFrom() {
        return 11;
    }

    @Override
    public String getAppId( ){return "CMCCTZ";}

    @Override
    public Map<String, Object> getVariables(BaseEflowApiVo data) {
        FtRmbsClaimDf cmccClaim = rmbsClaimService.getById(data.getBusinessId());
        Map<String, Object> map = new HashMap<>();
        map.put("startDeptCode",cmccClaim.getApplyDeptId());
        map.put("claimNo",cmccClaim.getClaimNo());
        map.put("startCompCode",cmccClaim.getApplyComId());
        map.put("itemId",cmccClaim.getItemId());
        return map;
    }

    @Override
    public String getTitle(TaskEflowApiVo data) {
        FtRmbsClaimDf cmccClaim = rmbsClaimService.getById(data.getBusinessId());
        if (cmccClaim.getItemId().contains("T002")){
            return StrUtil.format("{}关于[{}]的申请[报账金额={}]"
                    ,cmccClaim.getApplyUserName()
                    ,cmccClaim.getItemName()
                    ,cmccClaim.getApplyAmount()
                    ,cmccClaim.getPayAmount()
            );
        }else {
            return StrUtil.format("{}关于[{}]的申请[报账金额={};付款金额={}]"
                    ,cmccClaim.getApplyUserName()
                    ,cmccClaim.getItemName()
                    ,cmccClaim.getApplyAmount()
                    ,cmccClaim.getPayAmount()
            );
        }
    }

    @Override
    public void start(ProcessEflowApiVo data) {
        rmbsClaimService.update(Wrappers.lambdaUpdate(FtRmbsClaimDf.class)
                .eq(FtRmbsClaimDf::getClaimId,data.getBusinessId())
                .set(FtRmbsClaimDf::getProcessStateEng, TCmccClaimUtil.CLAIM_STEP_20_CODE)
                .set(FtRmbsClaimDf::getProcessState, TCmccClaimUtil.CLAIM_STEP_20_NAME)
        );
    }

    @Override
    public void end(ProcessEflowApiVo data) {
        rmbsClaimService.update(Wrappers.lambdaUpdate(FtRmbsClaimDf.class)
                .eq(FtRmbsClaimDf::getClaimId,data.getBusinessId())
                .set(FtRmbsClaimDf::getProcessStateEng, TCmccClaimUtil.STATE_CODE_0)
                .set(FtRmbsClaimDf::getProcessState, TCmccClaimUtil.CLAIM_STEP_0_NAME)
        );
//        //上传集团
//        rmbsClaimService.importGroup(cmccClaim);
    }

    @Override
    public void completed(TaskEflowApiVo data) {
        rmbsClaimService.update(Wrappers.lambdaUpdate(FtRmbsClaimDf.class)
                .eq(FtRmbsClaimDf::getClaimId,data.getBusinessId())
                .set(FtRmbsClaimDf::getProcessStateEng,TCmccClaimUtil.CLAIM_STEP_20_CODE)
                .set(FtRmbsClaimDf::getProcessState, TCmccClaimUtil.CLAIM_STEP_20_NAME)
        );
    }



    @Override
    public void rejected(TaskEflowApiVo data) {
        rmbsClaimService.update(Wrappers.lambdaUpdate(FtRmbsClaimDf.class)
                .eq(FtRmbsClaimDf::getClaimId,data.getBusinessId())
                .set(FtRmbsClaimDf::getProcessStateEng, TCmccClaimUtil.CLAIM_STEP_30_CODE)
                .set(FtRmbsClaimDf::getProcessState, TCmccClaimUtil.CLAIM_STEP_30_NAME)
        );
    }

    @Override
    public void processDelete(ProcessEflowApiVo data) {
        toDraft(data);
    }

    @Override
    public void deleteProcessHistory(ProcessEflowApiVo data) {
        toDraft(data);
    }

    private void toDraft(ProcessEflowApiVo data) {
        rmbsClaimService.update(Wrappers.lambdaUpdate(FtRmbsClaimDf.class)
                .eq(FtRmbsClaimDf::getClaimId, data.getBusinessId())
                .set(FtRmbsClaimDf::getProcessStateEng, TCmccClaimUtil.CLAIM_STEP_10_CODE)
                .set(FtRmbsClaimDf::getProcessState, TCmccClaimUtil.CLAIM_STEP_10_NAME)
        );
    }
}
