/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.util.WebUtils;
import com.easycloud.jxcmcc.common.data.builder.QueryWrapperBuilder;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxcmcc.common.security.annotation.Inner;
import com.easycloud.jxmcc.efinance.df.entity.FtAttachmentDf;
import com.easycloud.jxmcc.efinance.df.service.FtAttachmentDfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 老党费附件表
 *
 * <AUTHOR> code generator
 * @date 2023-02-16 14:40:12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ftattachmentdf" )
@Api(value = "ftattachmentdf", tags = "老党费附件表管理")
public class FtAttachmentDfController {

    private final  FtAttachmentDfService ftAttachmentDfService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param ftAttachmentDf 老党费附件表
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询老党费附件表" )
    @GetMapping("/page" )
    public R getFtAttachmentDfPage(Page page, FtAttachmentDf ftAttachmentDf) {
        QueryWrapper<FtAttachmentDf> build = QueryWrapperBuilder.<FtAttachmentDf>create(FtAttachmentDf.class).build(WebUtils.getParameterMap());
        return R.ok(ftAttachmentDfService.page(page, build));
    }


    /**
     * 通过id查询老党费附件表
     * @param attachId id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询老党费附件表" )
    @GetMapping("/{attachId}" )
    public R getById(@PathVariable("attachId" ) Long attachId) {
        return R.ok(ftAttachmentDfService.getById(attachId));
    }

    /**
     * 获取文件
     *
     * @param id       附件ID
     * @param response
     * @return
     */
    @SysLog(value = "获取文件", demandId = "")
    @Inner(false)
    @GetMapping("/down/{id}")
    public void downloadById(@PathVariable String id, HttpServletResponse response) {
        FtAttachmentDf ftAttachmentDf = ftAttachmentDfService.getById(id);
        String downloadUrl = String.format("http://10.183.33.13:8445/%s", ftAttachmentDf.getAttachPath());
        byte[] buffer = HttpUtil.downloadBytes(downloadUrl);
        try {
            response.reset();
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(ftAttachmentDf.getAttachName(), "UTF-8"));
            response.addHeader("Content-Length", "" + buffer.length);
            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            toClient.write(buffer);
            toClient.flush();
            toClient.close();
        } catch (IOException ex) {
            System.out.println(ex.getMessage());
        }
    }

    /**
     * 新增老党费附件表
     * @param ftAttachmentDf 老党费附件表
     * @return R
     */
    @ApiOperation(value = "新增老党费附件表", notes = "新增老党费附件表")
    @SysLog("新增老党费附件表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('efinance.df_ftattachmentdf_add')" )
    public R save(@RequestBody FtAttachmentDf ftAttachmentDf) {
        return R.ok(ftAttachmentDfService.save(ftAttachmentDf));
    }

    /**
     * 修改老党费附件表
     * @param ftAttachmentDf 老党费附件表
     * @return R
     */
    @ApiOperation(value = "修改老党费附件表", notes = "修改老党费附件表")
    @SysLog("修改老党费附件表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('efinance.df_ftattachmentdf_edit')" )
    public R updateById(@RequestBody FtAttachmentDf ftAttachmentDf) {
        return R.ok(ftAttachmentDfService.updateById(ftAttachmentDf));
    }

    /**
     * 通过id删除老党费附件表
     * @param attachId id
     * @return R
     */
    @ApiOperation(value = "通过id删除老党费附件表", notes = "通过id删除老党费附件表")
    @SysLog("通过id删除老党费附件表" )
    @DeleteMapping("/{attachId}" )
    @PreAuthorize("@pms.hasPermission('efinance.df_ftattachmentdf_del')" )
    public R removeById(@PathVariable Long attachId) {
        return R.ok(ftAttachmentDfService.removeById(attachId));
    }


   /**
     * 导入老党费附件表
     * @return R
     */
    @ApiOperation(value = "导入老党费附件表", notes = "导入老党费附件表")
    @SysLog("导入老党费附件表" )
    @PreAuthorize("@pms.hasPermission('efinance.df_ftattachmentdf_import')" )
    @FileImportLogAnnotation(busTable = "老党费附件表",busSystem = "efinance.df")
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return ftAttachmentDfService.importExcel(file,importParams);
    }

    /**
     * 导出老党费附件表
     * @return R
     */
    @ApiOperation(value = "导出老党费附件表", notes = "导出老党费附件表")
    @SysLog("导出老党费附件表")
    @PreAuthorize("@pms.hasPermission('efinance.df_ftattachmentdf_export')")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, FtAttachmentDf ftAttachmentDf) {
        List<FtAttachmentDf>  ftAttachmentDfList = ftAttachmentDfService.list(Wrappers.lambdaQuery(ftAttachmentDf));
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("老党费附件表列表", "老党费附件表"),
                FtAttachmentDf. class,ftAttachmentDfList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出老党费附件表导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版老党费附件表文件", notes = "导出导入模版老党费附件表文件")
    @SysLog("导出老党费附件表导入模版文件")
    @PreAuthorize("@pms.hasPermission('efinance.df_ftattachmentdf_export')")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/ftAttachmentDf.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
