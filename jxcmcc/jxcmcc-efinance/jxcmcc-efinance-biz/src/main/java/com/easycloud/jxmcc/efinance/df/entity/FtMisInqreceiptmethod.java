/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @date 2022-04-08 09:28:48
 */
@Data
@TableName("FT_MIS_INQRECEIPTMETHOD")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "")
public class FtMisInqreceiptmethod extends Model<FtMisInqreceiptmethod> {
private static final long serialVersionUID = 1L;

    /**
     * ;
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String id;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal receiptId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String receiptMethod;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private LocalDateTime methodStartDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private LocalDateTime methodEndDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private BigDecimal orgId;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String orgName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String bankName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String bankBranchName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String bankAccountName;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String bankAccountNum;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private LocalDateTime startDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private LocalDateTime endDate;
    /**
     * ;
     */
    @ApiModelProperty(value=";")
    @Excel(name = ";")
    private String stateValue;
    }
