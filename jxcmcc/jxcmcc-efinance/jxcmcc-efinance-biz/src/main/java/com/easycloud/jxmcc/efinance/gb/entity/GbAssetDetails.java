/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 资产明细表
 *
 * <AUTHOR> code generator
 * @date 2022-10-25 10:08:23
 */
@Data
@TableName("GB_ASSET_DETAILS")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "资产明细表")
public class GbAssetDetails extends Model<GbAssetDetails> {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "唯一标识")
    @Excel(name = "唯一标识")
    private String id;
    /**
     * 账簿编号
     */
    @ApiModelProperty(value = "账簿编号")
    @Excel(name = "账簿编号")
    private String bookTypeCode;
    /**
     * 账簿名称(判断地市）
     */
    @ApiModelProperty(value = "账簿名称(判断地市）")
    @Excel(name = "账簿名称(判断地市）")
    private String bookTypeName;
    /**
     * 资产编号
     */
    @ApiModelProperty(value = "资产编号")
    @Excel(name = "资产编号")
    private String assetId;
    /**
     * 资产标签号
     */
    @ApiModelProperty(value = "资产标签号")
    @Excel(name = "资产标签号")
    private String tagNumber;
    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    @Excel(name = "资产名称")
    private String itemName;
    /**
     * 资产类别
     */
    @ApiModelProperty(value = "资产类别")
    @Excel(name = "资产类别")
    private String contentCode;
    /**
     * 资产类别描述
     */
    @ApiModelProperty(value = "资产类别描述")
    @Excel(name = "资产类别描述")
    private String contentName;
    /**
     * 资产关键字
     */
    @ApiModelProperty(value = "资产关键字")
    @Excel(name = "资产关键字")
    private String assetKey;
    /**
     * 厂商
     */
    @ApiModelProperty(value = "厂商")
    @Excel(name = "厂商")
    private String manufacturerName;
    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @Excel(name = "规格型号")
    private String spec;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @Excel(name = "数量")
    private String amount;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Excel(name = "单位")
    private String unit;
    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    @Excel(name = "创建日期")
    @JSONField(format="yyyy-MM-dd")
    private Date createdDate;
    /**
     * 启用日期
     */
    @ApiModelProperty(value = "启用日期")
    @Excel(name = "启用日期")
    @JSONField(format="yyyy-MM-dd")
    private Date startDate;
    /**
     * 折旧方法
     */
    @ApiModelProperty(value = "折旧方法")
    @Excel(name = "折旧方法")
    private String deprnMethod;
    /**
     * 使用月份
     */
    @ApiModelProperty(value = "使用月份")
    @Excel(name = "使用月份")
    private String lifeInYears;
    /**
     * 剩余月数
     */
    @ApiModelProperty(value = "剩余月数")
    @Excel(name = "剩余月数")
    private String deprnLeftMonth;
    /**
     * 折旧账户
     */
    @ApiModelProperty(value = "折旧账户")
    @Excel(name = "折旧账户")
    private String depreciationAccount;
    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号")
    @Excel(name = "员工编号")
    private String responsibilityUser;
    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "员工姓名")
    @Excel(name = "员工姓名")
    private String responsibilityName;
    /**
     * 地点
     */
    @ApiModelProperty(value = "地点")
    @Excel(name = "地点")
    private String locCode;
    /**
     * 地点说明
     */
    @ApiModelProperty(value = "地点说明")
    @Excel(name = "地点说明")
    private String locDesc;
    /**
     * 原资产标签号
     */
    @ApiModelProperty(value = "原资产标签号")
    @Excel(name = "原资产标签号")
    private String oldTagNumber;
    /**
     * 建设状态
     */
    @ApiModelProperty(value = "建设状态")
    @Excel(name = "建设状态")
    private String constructionStatus;
    /**
     * 业务平台
     */
    @ApiModelProperty(value = "业务平台")
    @Excel(name = "业务平台")
    private String opeCode;
    /**
     * 网络层次
     */
    @ApiModelProperty(value = "网络层次")
    @Excel(name = "网络层次")
    private String lneCode;
    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @Excel(name = "项目编号")
    private String projectNumber;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @Excel(name = "项目名称")
    private String projectName;
    /**
     * 是否共建
     */
    @ApiModelProperty(value = "是否共建")
    @Excel(name = "是否共建")
    private String constructStatus;
    /**
     * 实际数量
     */
    @ApiModelProperty(value = "实际数量")
    @Excel(name = "实际数量")
    private String itemQty;
    /**
     * 成本
     */
    @ApiModelProperty(value = "成本")
    @Excel(name = "成本")
    private String cost;
    /**
     * 残值
     */
    @ApiModelProperty(value = "残值")
    @Excel(name = "残值")
    private String scrapValue;
    /**
     * 本期折旧额
     */
    @ApiModelProperty(value = "本期折旧额")
    @Excel(name = "本期折旧额")
    private String deprnAmount;
    /**
     * 本年折旧额
     */
    @ApiModelProperty(value = "本年折旧额")
    @Excel(name = "本年折旧额")
    private String ytdDeprn;
    /**
     * 累计折旧额（当月）
     */
    @ApiModelProperty(value = "累计折旧额（当月）")
    @Excel(name = "累计折旧额（当月）")
    private String deprnReserve;
    /**
     * 资产净值
     */
    @ApiModelProperty(value = "资产净值")
    @Excel(name = "资产净值")
    private String netAssetValue;
    /**
     * 资产净额
     */
    @ApiModelProperty(value = "资产净额")
    @Excel(name = "资产净额")
    private String deprnCost;
    /**
     * 本期减值准备
     */
    @ApiModelProperty(value = "本期减值准备")
    @Excel(name = "本期减值准备")
    private String impairAmount;
    /**
     * 本年减值准备
     */
    @ApiModelProperty(value = "本年减值准备")
    @Excel(name = "本年减值准备")
    private String ytdImpairment;
    /**
     * 累计减值准备
     */
    @ApiModelProperty(value = "累计减值准备")
    @Excel(name = "累计减值准备")
    private String impairReserve;
    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号")
    @Excel(name = "任务编号")
    private String taskNumber;
    /**
     * 任务名
     */
    @ApiModelProperty(value = "任务名")
    @Excel(name = "任务名")
    private String taskName;
    /**
     * 其他备注信息
     */
    @ApiModelProperty(value = "其他备注信息")
    @Excel(name = "其他备注信息")
    private String remark;
    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    @Excel(name = "成本中心")
    private String costCenterCode;
    /**
     * 是否报废
     */
    @ApiModelProperty(value = "是否报废")
    @Excel(name = "是否报废")
    private String isRetirements;
    /**
     * 成本中心说明
     */
    @ApiModelProperty(value = "成本中心说明")
    @Excel(name = "成本中心说明")
    private String assetKeyDesc;
    /**
     * 期间
     */
    @ApiModelProperty(value = "期间")
    @Excel(name = "期间")
    private String periodName;
    /**
     * 报账单ID
     */
    @ApiModelProperty(value = "报账单ID")
    @Excel(name = "报账单ID")
    private String claimId;
    /**
     * 报账单编号
     */
    @ApiModelProperty(value = "报账单编号")
    @Excel(name = "报账单编号")
    private String claimNo;
    /**
     * 报账单行明细ID
     */
    @ApiModelProperty(value = "报账单行明细ID")
    @Excel(name = "报账单行明细ID")
    private String claimLineId;
    /**
     * 租户号
     */
    @ApiModelProperty(value = "租户号")
    @Excel(name = "租户号")
    private Integer tenantId;
    /**
     * 删除标识;删除标识(0=否,1=是)
     */
    @ApiModelProperty(value = "删除标识;删除标识(0=否,1=是)")
    @Excel(name = "删除标识;删除标识(0=否,1=是)")
    private String deleteFlag;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @Excel(name = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @Excel(name = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @Excel(name = "更新人")
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @Excel(name = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedTime;

}
