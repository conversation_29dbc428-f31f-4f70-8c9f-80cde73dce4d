/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easycloud.jxmcc.efinance.df.entity.FtAttachmentDf;
import org.apache.ibatis.annotations.Mapper;

/**
 * 老党费附件表
 *
 * <AUTHOR> code generator
 * @date 2023-02-16 14:40:12
 */
@Mapper
public interface FtAttachmentDfMapper extends BaseMapper<FtAttachmentDf> {

}
