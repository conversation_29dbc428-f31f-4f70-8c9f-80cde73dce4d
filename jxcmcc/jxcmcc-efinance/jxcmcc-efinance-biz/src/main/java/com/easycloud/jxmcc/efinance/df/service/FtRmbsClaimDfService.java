/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.vo.PartyOrgNameVo;
import com.easycloud.jxmcc.efinance.df.vo.StatisticsDfVo;
import com.easycloud.jxmcc.efinance.entity.FtCmccClaim;
import com.easycloud.jxmcc.efinance.vo.UserRightsVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 党费系统报账头信息
 *
 * <AUTHOR>
 * @date 2022-03-28 15:00:54
 */
public interface FtRmbsClaimDfService extends IService<FtRmbsClaimDf> {

    /**
     * 查询党组织列表
     * @param page
     * @param partyOrgNameVo
     * @return
     */
    Page<PartyOrgNameVo> getPartyOrgNameList(Page page,PartyOrgNameVo partyOrgNameVo);


    /**
     * 查询党组织所有年份列表
     * @param page
     * @param partyOrgNameVo
     * @return
     */
    Page<PartyOrgNameVo> getPartyOrgNameListAll(Page page,PartyOrgNameVo partyOrgNameVo);

    FtRmbsClaimDf initializeClaim(FtRmbsClaimDf ftRmbsClaimDf);
    FtRmbsClaimDf initializeClaim();

    /**
     * 生成头信息(无行信息)
     * @param ftRmbsClaimDf
     * @return
     */
    FtRmbsClaimDf initializeClaimNoLine(FtRmbsClaimDf ftRmbsClaimDf);

    FtRmbsClaimDf saveOrUpdateClaim(FtRmbsClaimDf ftRmbsClaimDf);

    void countAmount(String claimId);

    void countAmountPayList(String claimId);

    /**
    * 导入excel
    * @return
    */
    R<Integer>  importExcel(MultipartFile file,JxcmccImportParams importParams) throws Exception ;

    /**
     * 审批单列表
     * @param page
     * @param ftRmbsClaimDf
     * @return
     */
    Page<FtRmbsClaimDf> getProjectMisNo(Page page,FtRmbsClaimDf ftRmbsClaimDf);

    /**
     * 原报账单号列表
     * @param page
     * @param ftRmbsClaimDf
     * @return
     */
    Page<FtRmbsClaimDf> getPayClaimNoList(Page page,FtRmbsClaimDf ftRmbsClaimDf);

    /**
     * 党组织已报账列表
     * @param page
     * @param partyOrgNameVo
     * @return
     */
    Page<FtRmbsClaimDf> getPartyClaim(Page page, PartyOrgNameVo partyOrgNameVo);

    /**
     * 党组织已报账所有年份列表
     * @param page
     * @param partyOrgNameVo
     * @return
     */
    Page<FtRmbsClaimDf> getPartyClaimAll(Page page, PartyOrgNameVo partyOrgNameVo);

    /**
     * 党组织已审批未报账列表
     * @param page
     * @param partyOrgNameVo
     * @return
     */
    Page<FtRmbsClaimDf> getPartyClaimT002DF(Page page, PartyOrgNameVo partyOrgNameVo);

    /**
     * 党组织已审批未报账所有年份列表
     * @param page
     * @param partyOrgNameVo
     * @return
     */
    Page<FtRmbsClaimDf> getPartyClaimT002DFAll(Page page, PartyOrgNameVo partyOrgNameVo);

    void deleteClaim(String claimId);

    Page<FtRmbsClaimDf> getPage(Page page, Map<String, String> paramMap);

    Page<StatisticsDfVo> statisticsPage(Page page, Map<String, String> paramMap);

    //获取当前登录用户标识
    String  getPermission( JxcmccUser jxcmccUser );

    //获取当前登录用户标识
    String  getDfPermission( JxcmccUser jxcmccUser );

    //获取当前登录用户标识
    boolean  isAdmin( );

    //根据当前登录用户判断报账单权限
    UserRightsVo getUserRights(JxcmccUser jxcmccUser );

    /**
     * 报账单管理/已归档分页查询报账单信息
     * @param page
     * @param paramMap
     * @return
     */
    Page<FtRmbsClaimDf> selectFtRmbsDfClaimListByPage(Page page, Map<String,String> paramMap);
}
