/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.df.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.admin.api.entity.SysUser;
import com.easycloud.jxcmcc.admin.api.feign.RemoteUserService;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.exception.BusinessException;
import com.easycloud.jxmcc.efinance.assistVat.utils.UserComp;
import com.easycloud.jxmcc.efinance.df.entity.FtMisInqreceiptmethod;
import com.easycloud.jxmcc.efinance.df.mapper.FtMisInqreceiptmethodMapper;
import com.easycloud.jxmcc.efinance.df.service.FtMisInqreceiptmethodService;
import com.easycloud.jxmcc.efinance.gh.entity.FtMisInqreceiptmethodGh;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.easycloud.jxcmcc.common.core.util.R;
import org.springframework.web.multipart.MultipartFile;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;

import java.util.ArrayList;
import java.util.List;
/**
 * 
 *
 * <AUTHOR>
 * @date 2022-04-08 09:28:48
 */
@Service
@AllArgsConstructor
public class FtMisInqreceiptmethodServiceImpl extends ServiceImpl<FtMisInqreceiptmethodMapper, FtMisInqreceiptmethod> implements FtMisInqreceiptmethodService {

    private final RemoteUserService userService;

    @Override
    public Page<FtMisInqreceiptmethod> page(IPage<FtMisInqreceiptmethod> page, FtMisInqreceiptmethod ftMisInqreceiptmethod) {
        return baseMapper.page(page,ftMisInqreceiptmethod);
    }

    /**
    * 导入excel
    * @return
    */
    @Override
   public R<Integer>  importExcel(MultipartFile file,JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！");
        }
        try {
            ImportParams params = new ImportParams();
            //解析导入EXCEL数据
            List<FtMisInqreceiptmethod> ftMisInqreceiptmethodList = ExcelImportUtil.importExcel(file.getInputStream(), FtMisInqreceiptmethod.class,
            params);
            this.saveBatch(ftMisInqreceiptmethodList);
              return R.ok(ftMisInqreceiptmethodList.size());
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return R.failed("导入失败");
    }

    @Override
    public R getBankAccountName(String applyUserId) {
        List<String> list = new ArrayList<>();
        list.add(applyUserId);
        List<SysUser> userByUserNames = userService.getUserByUserNames(list);
        if (userByUserNames.size() != 1){
            return R.failed(null);
        }
        String shortCode = UserComp.getShortId(userByUserNames.get(0).getOrgCodeShort());
        if (shortCode.equals("0")){
            return R.failed(null);
        }
        return R.ok(list(Wrappers.lambdaQuery(FtMisInqreceiptmethod.class)
                .eq(FtMisInqreceiptmethod::getOrgId,shortCode)));
    }
}
