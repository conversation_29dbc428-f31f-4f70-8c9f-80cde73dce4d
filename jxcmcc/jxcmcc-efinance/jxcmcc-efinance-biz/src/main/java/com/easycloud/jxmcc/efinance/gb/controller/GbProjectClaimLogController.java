/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectClaimLog;
import com.easycloud.jxmcc.efinance.gb.service.GbProjectClaimLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;


/**
 * 国拨项目报帐日志表
 *
 * <AUTHOR> code generator
 * @date 2022-10-24 09:21:00
 */
@RestController
@AllArgsConstructor
@RequestMapping("/gbprojectclaimlog" )
@Api(value = "gbprojectclaimlog", tags = "国拨项目报帐日志表管理")
public class GbProjectClaimLogController {

    private final GbProjectClaimLogService gbProjectClaimLogService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param gbProjectClaimLog 国拨项目报帐日志表
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询国拨项目报帐日志表" )
    @GetMapping("/page" )
    public R getGbProjectClaimLogPage(Page page, GbProjectClaimLog gbProjectClaimLog) {
        return R.ok(gbProjectClaimLogService.page(page, Wrappers.query(gbProjectClaimLog)));
    }


    /**
     * 通过id查询国拨项目报帐日志表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询国拨项目报帐日志表" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) String id) {
        return R.ok(gbProjectClaimLogService.getById(id));
    }

    /**
     * 新增国拨项目报帐日志表
     * @param gbProjectClaimLog 国拨项目报帐日志表
     * @return R
     */
    @ApiOperation(value = "新增国拨项目报帐日志表", notes = "新增国拨项目报帐日志表")
    @SysLog("新增国拨项目报帐日志表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('generator_gbprojectclaimlog_add')" )
    public R save(@RequestBody GbProjectClaimLog gbProjectClaimLog) {
        return R.ok(gbProjectClaimLogService.save(gbProjectClaimLog));
    }

    /**
     * 修改国拨项目报帐日志表
     * @param gbProjectClaimLog 国拨项目报帐日志表
     * @return R
     */
    @ApiOperation(value = "修改国拨项目报帐日志表", notes = "修改国拨项目报帐日志表")
    @SysLog("修改国拨项目报帐日志表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('generator_gbprojectclaimlog_edit')" )
    public R updateById(@RequestBody GbProjectClaimLog gbProjectClaimLog) {
        return R.ok(gbProjectClaimLogService.updateById(gbProjectClaimLog));
    }

    /**
     * 通过id删除国拨项目报帐日志表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除国拨项目报帐日志表", notes = "通过id删除国拨项目报帐日志表")
    @SysLog("通过id删除国拨项目报帐日志表" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('generator_gbprojectclaimlog_del')" )
    public R removeById(@PathVariable String id) {
        return R.ok(gbProjectClaimLogService.removeById(id));
    }


   /**
     * 导入国拨项目报帐日志表
     * @return R
     */
    @ApiOperation(value = "导入国拨项目报帐日志表", notes = "导入国拨项目报帐日志表")
    @SysLog("导入国拨项目报帐日志表" )
    @PreAuthorize("@pms.hasPermission('generator_gbprojectclaimlog_import')" )
    @FileImportLogAnnotation(busTable = "国拨项目报帐日志表",busSystem = "generator")
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        return gbProjectClaimLogService.importExcel(file,importParams);
    }

    /**
     * 导出国拨项目报帐日志表
     * @return R
     */
    @ApiOperation(value = "导出国拨项目报帐日志表", notes = "导出国拨项目报帐日志表")
    @SysLog("导出国拨项目报帐日志表")
    @PreAuthorize("@pms.hasPermission('generator_gbprojectclaimlog_export')")
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, GbProjectClaimLog gbProjectClaimLog) {
        List<GbProjectClaimLog>  gbProjectClaimLogList = gbProjectClaimLogService.list(Wrappers.lambdaQuery(gbProjectClaimLog));
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("国拨项目报帐日志表列表", "国拨项目报帐日志表"),
                GbProjectClaimLog. class,gbProjectClaimLogList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出国拨项目报帐日志表导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版国拨项目报帐日志表文件", notes = "导出导入模版国拨项目报帐日志表文件")
    @SysLog("导出国拨项目报帐日志表导入模版文件")
    @PreAuthorize("@pms.hasPermission('generator_gbprojectclaimlog_export')")
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/gbProjectClaimLog.xls", true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 同步集团单号
     * @param ids  业务id
     * @return
     */
    @ApiOperation(value = "国拨-同步集团单号", notes = "国拨-同步集团单号")
    @SysLog("国拨-同步集团单号" )
    @PostMapping("/syncClaimNo" )
    public R syncClaimNo(@RequestBody List<String> ids){
        return gbProjectClaimLogService.syncClaimNo(ids);
    }
}
