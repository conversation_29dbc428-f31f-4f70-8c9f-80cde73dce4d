package com.easycloud.jxmcc.efinance.df.claim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxmcc.efinance.claim.importVo.T063ImportClaimLineVo;
import com.easycloud.jxmcc.efinance.df.claim.mapper.T002ClaimDfMapper;
import com.easycloud.jxmcc.efinance.df.claim.service.IClaimDFEvent;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.claim.service.T002ClaimLineDfService;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimLineDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimLineDfService;
import com.easycloud.jxmcc.efinance.df.utils.TravelTypeComp;
import com.easycloud.jxmcc.efinance.df.vo.claim.T002DFClaimLineVo;
import com.easycloud.jxmcc.efinance.entity.FtCmccClaimLine;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class T002ClaimLineDfServiceImpl  implements T002ClaimLineDfService, IClaimDFEvent {

    private final T002ClaimDfMapper claimDfMapper;
    public static final String RECEIVE_CLAIM_TYPE = "T002DF";
    public static final String RECEIVE_CLAIM_NAME = "党费审批单";
    private final FtRmbsClaimLineDfService rmbsClaimLineDfService;
    private final FtRmbsClaimDfService rmbsClaimDfService;

    @Override
    public String itemId() {
        return RECEIVE_CLAIM_TYPE;
    }

    @Override
    public void deleteClaim(FtRmbsClaimDf ftRmbsClaimDf) {

    }

    @Override
    public IPage<T002DFClaimLineVo> page(Page page, T002DFClaimLineVo t002DFClaimLineVo) {
        return claimDfMapper.page(page,t002DFClaimLineVo);
    }

    @Override
    public List<T002DFClaimLineVo> getById(String id) {
        return claimDfMapper.getById(id);
    }

    @Override
    public R importExcel(MultipartFile file, String claimId) throws Exception{
        if (null == file) {
            throw new Exception("文件对象为空，请检查！");
        }
        try{
            List<T002DFClaimLineVo> importClaimLine;
            //解析导入EXCEL数据
            importClaimLine = EasyExcel.read(file.getInputStream())
                    .head(T002DFClaimLineVo.class)
                    .sheet(0)
                    .headRowNumber(3)
                    .doReadSync();

            if (importClaimLine.size() ==0){
              return R.error("导入的数据为空");
            }

            int start = 4;
            for (int i = 0; i < importClaimLine.size(); i++) {
                T002DFClaimLineVo t002DFClaimLineVo = importClaimLine.get(i);
                //检查导入是否合法
                if (StringUtils.isBlank(t002DFClaimLineVo.getTravelTypeName())){
                    return R.error("第"+ (i+start)+"行费用类型不能为空");
                }
                if (StringUtils.isBlank(t002DFClaimLineVo.getF03())){
                    return R.error("第"+ (i+start)+"行单价/标准不能为空");
                }else {
                    if (!(t002DFClaimLineVo.getF03().matches("[0-9]+.?[0-9]*"))){
                        return R.error("第"+ (i+start)+"行单价/标准只能为数字");
                    }
                }
                if (StringUtils.isBlank(t002DFClaimLineVo.getF02())){
                    return R.error("第"+ (i+start)+"行数量不能为空");
                }else {
                    if (!(t002DFClaimLineVo.getF02().matches("[0-9]+.?[0-9]*"))){
                        return R.error("第"+ (i+start)+"行数量只能为数字");
                    }
                }
                if (StringUtils.isBlank(t002DFClaimLineVo.getF01())){
                    return R.error("第"+ (i+start)+"行天数/次数不能为空");
                }else {
                    if (!(t002DFClaimLineVo.getF01().matches("[0-9]+.?[0-9]*"))){
                        return R.error("第"+ (i+start)+"行天数/次数只能为数字");
                    }
                }
                if (StringUtils.isBlank(t002DFClaimLineVo.getClaimLineDesc())){
                    return R.error("第"+ (i+start)+"行备注不能为空");
                }
            }

            List<FtRmbsClaimLineDf> list = importClaimLine.stream().map(t -> {
                FtRmbsClaimLineDf claimLine = new FtRmbsClaimLineDf();
                //将String类型的f01、f02、f03转换成BigDecimal
                BigDecimal f01 = new BigDecimal(t.getF01());
                BigDecimal f02 = new BigDecimal(t.getF02());
                BigDecimal f03 = new BigDecimal(t.getF03());
                claimLine.setF01(f01);
                claimLine.setF02(f02);
                claimLine.setF03(f03);
                //数据类型必须保持一致，否则copy该字段为null
                BeanUtils.copyProperties(t, claimLine);
                return claimLine;
            }).collect(Collectors.toList());
            List<FtRmbsClaimLineDf> saveList=new ArrayList<>();

            for(int i=0; i<list.size(); i++){
                FtRmbsClaimLineDf ftRmbsClaimLineDf = list.get(i);
                ftRmbsClaimLineDf.setClaimId(claimId);

                //将名称转换为id
                ftRmbsClaimLineDf.setTravelTypeId(TravelTypeComp.getCompId(ftRmbsClaimLineDf.getTravelTypeName()));

                //拼接单位
                ftRmbsClaimLineDf.setC01(TravelTypeComp.getC01(ftRmbsClaimLineDf.getTravelTypeId()));
                ftRmbsClaimLineDf.setC02(TravelTypeComp.getC02(ftRmbsClaimLineDf.getTravelTypeId()));
                ftRmbsClaimLineDf.setC03(TravelTypeComp.getC03(ftRmbsClaimLineDf.getTravelTypeId()));

                //小计
                ftRmbsClaimLineDf.setApplyAmount(ftRmbsClaimLineDf.getF01().multiply(ftRmbsClaimLineDf.getF02().multiply(ftRmbsClaimLineDf.getF03())));

                saveList.add(ftRmbsClaimLineDf);
            }
            this.rmbsClaimLineDfService.saveBatch(saveList);
            this.rmbsClaimDfService.countAmount(claimId);
            return R.ok(list.size());
        }catch (Exception e){
            return R.error("上传异常");
        }
    }
}
