/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.gb.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.core.util.DateUtils;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.efinance.gb.entity.GbCapitalPaymentAccount;
import com.easycloud.jxmcc.efinance.gb.mapper.GbCapitalPaymentAccountMapper;
import com.easycloud.jxmcc.efinance.gb.service.GbCapitalPaymentAccountService;
import com.easycloud.jxmcc.efinance.service.IEmisTodoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 国拨资金支付台账
 *
 * <AUTHOR> code generator
 * @date 2022-10-27 09:43:07
 */
@Service
@Slf4j
public class GbCapitalPaymentAccountServiceImpl extends ServiceImpl<GbCapitalPaymentAccountMapper, GbCapitalPaymentAccount> implements GbCapitalPaymentAccountService {

    @Autowired
    private IEmisTodoService iEmisTodoService;

    /**
     * 导入excel
     * @return
     */
    @Override
    public R<Integer> importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！" );
        }
        ImportParams params = new ImportParams();
        //解析导入EXCEL数据
        List<GbCapitalPaymentAccount> gbCapitalPaymentAccountList = ExcelImportUtil.importExcel(file.getInputStream(), GbCapitalPaymentAccount. class,
                params);
        this.saveBatch(gbCapitalPaymentAccountList);
        return R.ok(gbCapitalPaymentAccountList.size());
    }

    /**
     * 生成国拨资金支付台账
     * @return R
     */
    @Override
    public R generateGbCapitalPaymentAccount() throws Exception {
        // 支付台账数据(全量,项目总金额与项目拆分金额为重复数据,不可直接使用)
        List<GbCapitalPaymentAccount> payAccount = baseMapper.selectGbCapitalPaymentAccountByDate();

        if(null == payAccount || payAccount.size() < 1){
            return R.failed(false, "支付台账数据为空，定时推送数据失败");
        }

        // 设置默认值和金额计算
        JxcmccUser jxcmccUser = SecurityUtils.getUser();
        String accountPeriod = DateUtils.formatDate("yyyy-MM-dd");
        payAccount.stream().forEach(x -> {
            x.setAccountPeriod(accountPeriod);
            x.setDeleteFlag("0");
            x.setCreatedBy(jxcmccUser.getUsername());
            x.setCreatedTime(DateUtils.getDate());

            BigDecimal projectTotalAmount = BigDecimal.ZERO;
            if(null != x.getProjectTotalAmount() && !"".equals(x.getProjectTotalAmount())){
                projectTotalAmount = new BigDecimal(x.getProjectTotalAmount());
            }
            BigDecimal usedTotalAmount = BigDecimal.ZERO;
            List<String> usedTotalAmountList = payAccount.stream().filter(item -> item.getProjectNo().equals(x.getProjectNo())).map(item -> item.getThisPayment()).collect(Collectors.toList());
            if(null != usedTotalAmountList && usedTotalAmountList.size() > 0){
                for(String str : usedTotalAmountList){
                    if(null == str || "".equals(str)){
                        continue;
                    }
                    usedTotalAmount = usedTotalAmount.add(new BigDecimal(str));
                }
            }
            x.setUsedTotalAmount(String.valueOf(usedTotalAmount));
            x.setSurplusTotalAmount(String.valueOf(projectTotalAmount.subtract(usedTotalAmount)));

            BigDecimal branchAmount = BigDecimal.ZERO;
            if(null != x.getBranchAmount() && !"".equals(x.getBranchAmount())){
                branchAmount = new BigDecimal(x.getBranchAmount());
            }
            BigDecimal branchUsedAmount = BigDecimal.ZERO;
            List<String> branchUsedAmountList = payAccount.stream().filter(item -> item.getProjectNo().equals(x.getProjectNo()) && compareStr(item.getBranchCompanyCode(), x.getBranchCompanyCode())).map(item -> item.getThisPayment()).collect(Collectors.toList());
            if(null != branchUsedAmountList && branchUsedAmountList.size() > 0){
                for(String str : branchUsedAmountList){
                    if(null == str || "".equals(str)){
                        continue;
                    }
                    branchUsedAmount = branchUsedAmount.add(new BigDecimal(str));
                }
            }
            x.setBranchUsedAmount(String.valueOf(branchUsedAmount));
            x.setBranchSurplusAmount(String.valueOf(branchAmount.subtract(branchUsedAmount)));
        });

        // 更新原台账数据为删除状态
        this.update(Wrappers.lambdaUpdate(GbCapitalPaymentAccount.class).set(GbCapitalPaymentAccount::getDeleteFlag, "1"));

        // 保存台账数据
        this.saveBatch(payAccount);

        // 代办推送到项目经理
        String title = "国拨资金支付台账";
        List<String> projectManagerList = payAccount.stream().map(x -> x.getProjectManagerNo()).distinct().collect(Collectors.toList());
        for (String projectManager : projectManagerList) {
            if(null == projectManager){
                continue;
            }
            String idStr = IdWorker.getIdStr();
            String uuid = idStr;
            String businessKey = idStr;
            String link = StrUtil.indexedFormat("&component={0}&businessId={1}&from={2}&taskId=1&activityId=1",
                    "gbcapitalpaymentaccount", businessKey, 6);
            iEmisTodoService.sendEmis("major", title, uuid, projectManager, businessKey, link, title, 6);
        }
        return R.ok(true, "台账生成并推送数据成功");
    }

    /**
     * 字符串比较
     * @param str1
     * @param str2
     * @return
     */
    public boolean compareStr(String str1, String str2){
        String newStr1 = null == str1 ? "" : str1;
        String newStr2 = null == str2 ? "" : str2;
        return newStr1.equals(newStr2);
    }

    /**
     * 分页查询台账数据
     * @param page
     * @param gbCapitalPaymentAccount
     * @return
     */
    @Override
    public IPage selectGbCapitalPaymentAccountByPage(Page page, GbCapitalPaymentAccount gbCapitalPaymentAccount) {
        IPage<GbCapitalPaymentAccount> tempPage = baseMapper.selectGbCapitalPaymentAccountByPage(page, gbCapitalPaymentAccount);
        return tempPage;
    }

}
