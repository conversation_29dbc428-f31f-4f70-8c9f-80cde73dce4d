/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.gb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxmcc.efinance.gb.entity.GbProjectClaimLog;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 国拨项目报帐日志表
 *
 * <AUTHOR> code generator
 * @date 2022-10-24 09:21:00
 */
public interface GbProjectClaimLogService extends IService<GbProjectClaimLog> {


    /**
    * 导入excel
    * @return
    */
    R<Integer>  importExcel(MultipartFile file,JxcmccImportParams importParams) throws Exception ;

    /**
     * 同步集团报账单号
     * @param ids
     * @return
     */
    R syncClaimNo(List<String> ids);
}
