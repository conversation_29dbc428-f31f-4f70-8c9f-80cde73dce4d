/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 记账凭证
 *
 * <AUTHOR>
 * @date 2022-08-05 16:07:57
 */
@Data
@TableName("FT_RMBS_CONFIDENT_CLAIM_DF")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "记账凭证")
public class FtRmbsConfidentClaimDf extends Model<FtRmbsConfidentClaimDf> {
    private static final long serialVersionUID = 1L;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    @Excel(name = "凭证编号")
    private String confidentCode;
    /**
     * 报账单ID
     */
    @ApiModelProperty(value = "报账单ID")
    @Excel(name = "报账单ID")
    private String claimId;
    /**
     * 报账单号
     */
    @ApiModelProperty(value = "报账单号")
    @Excel(name = "报账单号")
    private String claimNo;
    /**
     * 贷方金额
     */
    @ApiModelProperty(value = "贷方金额")
    @Excel(name = "贷方金额")
    private BigDecimal payAmount;
    /**
     * 借方金额
     */
    @ApiModelProperty(value = "借方金额")
    @Excel(name = "借方金额")
    private BigDecimal applyAmount;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    @Excel(name = "摘要")
    private String summary;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String processStateEng;
    /**
     * 记账日期
     */
    @ApiModelProperty(value = "记账日期")
    @Excel(name = "记账日期")
    private String applyDate;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String amountInfo;
    /**
     *
     */
    @ApiModelProperty(value = "科目信息")
    @Excel(name = "科目信息")
    private String amountName;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @Excel(name = "")
    private String orderNum;
}
