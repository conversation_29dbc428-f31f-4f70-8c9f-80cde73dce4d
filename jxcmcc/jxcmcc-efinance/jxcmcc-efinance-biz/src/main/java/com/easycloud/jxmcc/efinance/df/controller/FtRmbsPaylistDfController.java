/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.efinance.df.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.log.annotation.FileImportLogAnnotation;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxcmcc.common.log.annotation.SysLog;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsPaylistDf;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsPaylistDfService;
import com.easycloud.jxmcc.efinance.df.vo.PaylistDfVo;
import com.easycloud.jxmcc.efinance.gh.entity.FtRmbsPaylistGh;
import com.easycloud.jxmcc.efinance.vo.ClaimLineVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.HashMap;
import java.util.Map;
import java.util.List;


/**
 * 党费系统支付明细
 *
 * <AUTHOR>
 * @date 2022-03-28 15:00:54
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ftrmbspaylistdf" )
@Api(value = "ftrmbspaylistdf", tags = "党费系统支付明细管理")
public class FtRmbsPaylistDfController {

    private final FtRmbsPaylistDfService ftRmbsPaylistDfService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param ftRmbsPaylistDf 党费系统支付明细
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @SysLog("分页查询党费系统支付明细" )
    @GetMapping("/page" )
    public R getFtRmbsPaylistPage(Page page, FtRmbsPaylistDf ftRmbsPaylistDf) {
        return R.ok(ftRmbsPaylistDfService.page(page, Wrappers.query(ftRmbsPaylistDf)));
    }


    /**
     * 保存或更新明细行
     * @return R
     */
    @ApiOperation(value = "保存或更新明细行", notes = "保存或更新明细行")
    @SysLog(value = "保存或更新明细行")
    @PostMapping("/saveOrUpdateLine")
    public R saveOrUpdateLine(@RequestBody FtRmbsPaylistDf ftRmbsPaylistDf) {
        return R.ok(ftRmbsPaylistDfService.saveOrUpdateLine(ftRmbsPaylistDf));
    }

    /**
     * 删除明细行
     * @return R
     */
    @ApiOperation(value = "删除明细行", notes = "删除明细行")
    @SysLog(value = "删除明细行",demandId="xq_common")
    @PostMapping("deleteLine")
    public R deleteLine(@RequestBody ClaimLineVo claimLineVo) {
        return R.ok(ftRmbsPaylistDfService.deleteLine(claimLineVo));
    }


    /**
     * 通过id查询党费系统支付明细
     * @param payLineId id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @SysLog("通过id查询党费系统支付明细" )
    @GetMapping("/{payLineId}" )
    public R getById(@PathVariable("payLineId" ) String payLineId) {
        return R.ok(ftRmbsPaylistDfService.getById(payLineId));
    }


    /**
     * 通过claimId查询党费系统支付明细
     * @param claimId id
     * @return R
     */
    @ApiOperation(value = "通过claimId查询", notes = "通过claimId查询")
    @SysLog("通过id查询党费系统支付明细" )
    @GetMapping("/getById/{claimId}" )
    public R getByCliamId(@PathVariable("claimId") String claimId) {
        return R.ok(ftRmbsPaylistDfService.getByClaimId(claimId));
    }



    /**
     * 新增党费系统支付明细
     * @param ftRmbsPaylistDf 党费系统支付明细
     * @return R
     */
    @ApiOperation(value = "新增党费系统支付明细", notes = "新增党费系统支付明细")
    @SysLog("新增党费系统支付明细" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('generator_ftrmbspaylist_add')" )
    public R save(@RequestBody FtRmbsPaylistDf ftRmbsPaylistDf) {
        return R.ok(ftRmbsPaylistDfService.save(ftRmbsPaylistDf));
    }

    /**
     * 修改党费系统支付明细
     * @param ftRmbsPaylistDf 党费系统支付明细
     * @return R
     */
    @ApiOperation(value = "修改党费系统支付明细", notes = "修改党费系统支付明细")
    @SysLog("修改党费系统支付明细" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('generator_ftrmbspaylist_edit')" )
    public R updateById(@RequestBody FtRmbsPaylistDf ftRmbsPaylistDf) {
        return R.ok(ftRmbsPaylistDfService.updateById(ftRmbsPaylistDf));
    }

    /**
     * 通过id删除党费系统支付明细
     * @param payLineId id
     * @return R
     */
    @ApiOperation(value = "通过id删除党费系统支付明细", notes = "通过id删除党费系统支付明细")
    @SysLog("通过id删除党费系统支付明细" )
    @DeleteMapping("/{payLineId}" )
    @PreAuthorize("@pms.hasPermission('generator_ftrmbspaylist_del')" )
    public R removeById(@PathVariable String payLineId) {
        return R.ok(ftRmbsPaylistDfService.removeById(payLineId));
    }


   /**
     * 导入党费系统支付明细
     * @return R
     */
    @ApiOperation(value = "导入党费系统支付明细", notes = "导入党费系统支付明细")
    @SysLog("导入党费系统支付明细" )
    @FileImportLogAnnotation(busTable = "党费系统支付明细",busSystem = "generator")
    @RequestMapping(value = "/importExcel",method = RequestMethod.POST)
    public R importExcel(MultipartFile file, PaylistDfVo paylistDfVo) throws Exception {
        return ftRmbsPaylistDfService.importExcel(file,paylistDfVo);
    }

       /**
     * 导出党费系统支付明细
     * @return R
     */
    @ApiOperation(value = "导出党费系统支付明细", notes = "导出党费系统支付明细")
    @SysLog("导出党费系统支付明细" )
    @GetMapping(value = "/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, FtRmbsPaylistDf ftRmbsPaylistDf) {
        if (StringUtils.isEmpty(ftRmbsPaylistDf.getClaimId())){
            throw  new BootstrapMethodError("导出失败，请联系管理员");
        };
        List<FtRmbsPaylistDf> exportList = ftRmbsPaylistDfService.list(Wrappers.lambdaQuery(FtRmbsPaylistDf.class)
                .eq(FtRmbsPaylistDf::getClaimId, ftRmbsPaylistDf.getClaimId()));
        //导出Excel
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("党费系统支付明细列表", "党费系统支付明细"),
                    FtRmbsPaylistDf.class, exportList);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出党费系统支付明细导入模版
     * @return R
     */
    @ApiOperation(value = "导出导入模版党费系统支付明细文件", notes = "导出导入模版党费系统支付明细文件")
    @SysLog("导出党费系统支付明细导入模版文件" )
    @GetMapping(value = "/exportTemplateFile")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        //导出Excel
        try {
            Map<String,Object> map = new HashMap<String, Object>();
            TemplateExportParams params = new TemplateExportParams("templates/poi/df/T005DfClaimPayTemplate.xls",true);
            Workbook workbook= ExcelExportUtil.exportExcel(params,map);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            ServletOutputStream oss = null;
            oss = response.getOutputStream();
            workbook.write(oss);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
