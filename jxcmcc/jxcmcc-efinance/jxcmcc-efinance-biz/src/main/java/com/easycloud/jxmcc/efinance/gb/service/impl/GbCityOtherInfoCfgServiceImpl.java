/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.gb.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.common.core.util.BeanHelperUtil;
import com.easycloud.jxmcc.efinance.gb.entity.GbCityOtherInfoCfg;
import com.easycloud.jxmcc.efinance.gb.mapper.GbCityOtherInfoCfgMapper;
import com.easycloud.jxmcc.efinance.gb.service.GbCityOtherInfoCfgService;
import com.easycloud.jxcmcc.common.core.entity.vo.JxcmccImportParams;
import com.easycloud.jxmcc.efinance.util.CheckObjectIsNullUtils;
import com.easycloud.jxmcc.efinance.util.TCmccClaimUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.easycloud.jxcmcc.common.core.util.R;
import org.springframework.web.multipart.MultipartFile;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;

import java.util.ArrayList;
import java.util.List;

/**
 * 地市其它信息配置
 *
 * <AUTHOR> code generator
 * @date 2022-10-19 10:27:16
 */
@Service
@Slf4j
public class GbCityOtherInfoCfgServiceImpl extends ServiceImpl<GbCityOtherInfoCfgMapper, GbCityOtherInfoCfg> implements GbCityOtherInfoCfgService {


    /**
    * 导入excel
    * @return
    */
    @Override
    public R<Integer> importExcel(MultipartFile file, JxcmccImportParams importParams) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！" );
        }
        //导入类型，0：覆盖导入，1：增量导入
        String type = importParams.getType();

        ImportParams params = new ImportParams();
        params.setTitleRows(1);//标题总行数
        //解析导入EXCEL数据
        List<GbCityOtherInfoCfg> gbCityOtherInfoCfgList = ExcelImportUtil.importExcel(file.getInputStream(), GbCityOtherInfoCfg.class, params);
        // 清除空白行
        gbCityOtherInfoCfgList.removeIf(item -> CheckObjectIsNullUtils.objCheckIsNull(item));
        // 判断是否有数据
        if(null == gbCityOtherInfoCfgList || gbCityOtherInfoCfgList.size() < 1){
            return R.ok(0, "文件解析成功，数据行为0");
        }

        // 校验数据
        List<String> errorMsgList = new ArrayList<>();
        StringBuffer sb = null;
        int index = 1;

        for (GbCityOtherInfoCfg gbCityOtherInfoCfg : gbCityOtherInfoCfgList) {
            sb = new StringBuffer();
            String companyName = null == gbCityOtherInfoCfg.getCompanyName() ? "" : gbCityOtherInfoCfg.getCompanyName();
            if(companyName.equals("")){
                sb.append("公司名称未填写|");
            }
            if(null == gbCityOtherInfoCfg.getFinancialStaffName() || gbCityOtherInfoCfg.getFinancialStaffName().equals("")){
                sb.append("财务人员姓名未填写|");
            }
            if(null == gbCityOtherInfoCfg.getFinancialStaffNo() || gbCityOtherInfoCfg.getFinancialStaffNo().equals("")){
                sb.append("财务人员用户名未填写|");
            }
            if(null == gbCityOtherInfoCfg.getGhFocusNo() || gbCityOtherInfoCfg.getGhFocusNo().equals("")){
                sb.append("工行集中账号未填写|");
            }
            if(null == gbCityOtherInfoCfg.getGhFocusName() || gbCityOtherInfoCfg.getGhFocusName().equals("")){
                sb.append("工行集中账号名称未填写|");
            }

            gbCityOtherInfoCfg.setDeleteFlag("0");
            gbCityOtherInfoCfg.setCompanyCode(BeanHelperUtil.companyMapName.get(companyName));

            // 拼接错误信息
            if(sb.length() > 0){
                errorMsgList.add(String.format("第%s行数据：%s", index, sb.toString()));
            }

            // 行下标增加
            index += 1;
        }
        // 判断是否有错误信息
        if(null != errorMsgList && errorMsgList.size() > 0){
            return R.failed(0, String.join(",", errorMsgList));
        }

        // 覆盖导入删除数据
        if(type.equals("0")){
            this.remove(Wrappers.lambdaQuery(GbCityOtherInfoCfg.class));
        }
        // 更新数据
        long start = System.currentTimeMillis();
        this.saveBatch(gbCityOtherInfoCfgList);
        long end = System.currentTimeMillis();
        log.warn("耗时：{}m", (end - start) / 1000);
        return R.ok(gbCityOtherInfoCfgList.size(),"导入成功(耗时：" + (end - start) / 1000 + "m)");
    }

}
