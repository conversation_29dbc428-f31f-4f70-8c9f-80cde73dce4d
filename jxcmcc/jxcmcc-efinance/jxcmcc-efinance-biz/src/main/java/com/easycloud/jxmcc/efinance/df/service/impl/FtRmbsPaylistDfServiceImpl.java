/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.efinance.df.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimDf;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsClaimLineDf;
import com.easycloud.jxmcc.efinance.df.entity.FtRmbsPaylistDf;
import com.easycloud.jxmcc.efinance.df.mapper.FtRmbsClaimLineDfMapper;
import com.easycloud.jxmcc.efinance.df.mapper.FtRmbsPaylistDfMapper;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsClaimDfService;
import com.easycloud.jxmcc.efinance.df.service.FtRmbsPaylistDfService;
import com.easycloud.jxmcc.efinance.df.vo.PaylistDfVo;
import com.easycloud.jxmcc.efinance.gh.entity.FtRmbsClaimGh;
import com.easycloud.jxmcc.efinance.vo.ClaimLineVo;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.easycloud.jxcmcc.common.core.util.R;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 党费系统支付明细
 *
 * <AUTHOR>
 * @date 2022-03-28 15:00:54
 */
@Service
@AllArgsConstructor
@Slf4j
public class FtRmbsPaylistDfServiceImpl extends ServiceImpl<FtRmbsPaylistDfMapper, FtRmbsPaylistDf> implements FtRmbsPaylistDfService {

    private final FtRmbsPaylistDfMapper rmbsPaylistDfMapper;

    private final FtRmbsClaimDfService rmbsClaimDfService;
    /**
    * 导入excel
    * @return
    */
    @Override
   public R importExcel(MultipartFile file,PaylistDfVo paylistDfVo) throws Exception {
        if (null == file) {
            throw new Exception("文件对象为空，请检查！");
        }

        FtRmbsClaimDf ftRmbsClaimDf = rmbsClaimDfService.getById(paylistDfVo.getClaimId());
        if (ObjectUtil.isEmpty(ftRmbsClaimDf)){
            return R.error("报账单不存在");
        }

        try {
            List<PaylistDfVo> importClaimLine;
            //解析导入EXCEL数据
            importClaimLine = EasyExcel.read(file.getInputStream())
                    .head(PaylistDfVo.class)
                    .sheet(0)
                    .headRowNumber(3)
                    .doReadSync();

            if (importClaimLine.size() == 0) {
                return R.error("导入数据为空");
            }

            int start = 4; // 明细从第3行开始
            for (int i = 0; i < importClaimLine.size(); i++) {
                PaylistDfVo vo = importClaimLine.get(i);
                //检查导入数据是否合法
                if (StringUtils.isBlank(vo.getAccountName())) {
                    return R.error("第" + (i + start) + "行供应商开户名不能为空");
                }
                if (StringUtils.isBlank(vo.getBankName())) {
                    return R.error("第" + (i + start) + "行供应商开户行不能为空");
                }
                if (StringUtils.isBlank(vo.getBankAccount())) {
                    return R.error("第" + (i + start) + "行供应商账号不能为空");
                }
                if (StringUtils.isBlank(vo.getPayAmount())) {
                    return R.error("第" + (i + start) + "行付款金额不能为空");
                } else {
                    if (vo.getPayAmount().matches("[0-9]+.?[0-9]*")){
                        BigDecimal payAmount = new BigDecimal(vo.getPayAmount());
                        if (payAmount.compareTo(BigDecimal.valueOf(0)) == -1) {
                            return R.error("第" + (i + start) + "行付款金额必须大于0");
                        }
                    }
                }
            }
            List<FtRmbsPaylistDf> list = importClaimLine.stream().map(t -> {
                FtRmbsPaylistDf ftRmbsPaylistDf = new FtRmbsPaylistDf();
                BigDecimal payAmount = new BigDecimal(t.getPayAmount());
                ftRmbsPaylistDf.setPayAmount(payAmount);
                //数据类型必须保持一致，否则copy该字段为null
                BeanUtils.copyProperties(t, ftRmbsPaylistDf);
                return ftRmbsPaylistDf;
            }).collect(Collectors.toList());

            List<FtRmbsPaylistDf> saveList = new ArrayList<>();

            for (int i = 0; i < list.size(); i++) {
                FtRmbsPaylistDf ftRmbsPaylistDf = list.get(i);
                ftRmbsPaylistDf.setClaimId(paylistDfVo.getClaimId());
                ftRmbsPaylistDf.setClaimNo(ftRmbsClaimDf.getClaimNo());
                saveList.add(ftRmbsPaylistDf);
            }
            this.saveBatch(saveList);
            this.rmbsClaimDfService.countAmountPayList(paylistDfVo.getClaimId());
            return R.ok(list.size());
        }catch (Exception e){
            return R.error("导入失败，请联系管理员");
        }
    }

    @Override
    public int saveOrUpdateLine(FtRmbsPaylistDf ftRmbsPaylistDf) {
        boolean update = StrUtil.isNotBlank(ftRmbsPaylistDf.getPayLineId());
        if(update){
            updateById(ftRmbsPaylistDf);
        }else {
            save(ftRmbsPaylistDf);
        }
        rmbsClaimDfService.countAmountPayList(ftRmbsPaylistDf.getClaimId());
        return 1;
    }

    @Override
    public int deleteLine(ClaimLineVo claimLineVo) {
        removeByIds(claimLineVo.getIdList());
        rmbsClaimDfService.countAmountPayList(claimLineVo.getClaimId());
        return claimLineVo.getIdList().size();
    }

    @Override
    public List<FtRmbsPaylistDf> getByClaimId(String claimId) {
        return rmbsPaylistDfMapper.selectList(Wrappers.lambdaQuery(FtRmbsPaylistDf.class).eq(FtRmbsPaylistDf::getClaimId,claimId));
    }
}
