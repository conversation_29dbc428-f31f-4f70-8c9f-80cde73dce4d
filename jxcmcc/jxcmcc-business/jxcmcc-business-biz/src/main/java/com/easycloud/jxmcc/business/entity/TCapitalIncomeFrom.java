/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxmcc.business.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 资金营收表周/月报
 *
 * <AUTHOR>
 * @date 2022-05-12 16:01:24
 */
@Data
@TableName("T_CAPITAL_INCOME_FROM")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "资金营收表周/月报")
public class TCapitalIncomeFrom extends Model<TCapitalIncomeFrom> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private String id;
    /**
     * 截止营业时间
     */
    @ApiModelProperty(value = "截止营业时间")
    @Excel(name = "截止营业时间")
    private String businessTime;
    /**
     * 周期
     */
    @ApiModelProperty(value = "周期")
    @Excel(name = "周期")
    private String cycle;
    /**
     * 地市编码
     */
    @ApiModelProperty(value = "地市编码")
    @Excel(name = "地市编码")
    private String regionCode;
    /**
     * 地市名称
     */
    @ApiModelProperty(value = "地市名称")
    @Excel(name = "地市名称")
    private String regionName;
    /**
     * 营收描述
     */
    @ApiModelProperty(value = "营收描述")
    @Excel(name = "营收描述")
    private String content;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @Excel(name = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;

    @TableField(exist = false)
    private List<TCapitalIncomeFromLine> businessList;
}
