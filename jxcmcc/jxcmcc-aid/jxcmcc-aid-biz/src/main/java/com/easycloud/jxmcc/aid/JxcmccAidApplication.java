package com.easycloud.jxmcc.aid;

import cn.hutool.core.thread.ExecutorBuilder;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.baomidou.mybatisplus.extension.incrementer.OracleKeyGenerator;
import com.easycloud.jxcmcc.common.datasource.annotation.EnableDynamicDataSource;
import com.easycloud.jxcmcc.common.feign.annotation.EnableJxcmccFeignClients;
import com.easycloud.jxcmcc.common.security.annotation.EnableJxcmccResourceServer;
import com.easycloud.jxcmcc.common.swagger.annotation.EnableJxcmccSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.ExecutorService;

/**
 * @CLASSNAME JxcmccAidApplication
 * @Description
 * @Auther JanHezz
 * @BLOG www.luckyhe.com
 * @DATE 2021/3/11 14:24
 */
@EnableJxcmccSwagger2
@SpringCloudApplication
@EnableJxcmccFeignClients
@EnableJxcmccResourceServer
@EnableDynamicDataSource()
@EnableAutoConfiguration(exclude = {DruidDataSourceAutoConfigure.class})
@EnableAsync
public class JxcmccAidApplication {
    	public static void main(String[] args) {
		SpringApplication.run(JxcmccAidApplication.class, args);
	}

	@Bean
	public OracleKeyGenerator oracleKeyGenerator(){
		return new OracleKeyGenerator();
	}

		/**
	 * 创建线程池
	 * @return
	 */
	@Bean
	public ExecutorService executor(){
		ExecutorService executor = ExecutorBuilder.create()
				.setCorePoolSize(4)
				.setMaxPoolSize(20)
				.build();
		return executor;
	}
}
