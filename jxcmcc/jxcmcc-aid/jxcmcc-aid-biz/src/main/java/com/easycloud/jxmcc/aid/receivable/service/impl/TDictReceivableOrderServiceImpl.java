/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */
package com.easycloud.jxmcc.aid.receivable.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easycloud.jxcmcc.act.api.e.ToDoConfEnum;
import com.easycloud.jxcmcc.act.api.feign.RemoteProcessService;
import com.easycloud.jxcmcc.admin.api.feign.RemoteBulletinService;
import com.easycloud.jxcmcc.common.core.util.R;
import com.easycloud.jxcmcc.common.security.service.JxcmccUser;
import com.easycloud.jxcmcc.common.security.util.SecurityUtils;
import com.easycloud.jxmcc.aid.receivable.entity.TDictReceivableOrder;
import com.easycloud.jxmcc.aid.receivable.entity.TDictReceivableResponsibility;
import com.easycloud.jxmcc.aid.receivable.mapper.TDictReceivableOrderMapper;
import com.easycloud.jxmcc.aid.receivable.mapper.TDictReceivableResponsibilityMapper;
import com.easycloud.jxmcc.aid.receivable.service.TDictReceivableOrderService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DICT管理回款工单表
 *
 * <AUTHOR> code generator
 * @date 2021-12-03 10:49:45
 */
@Slf4j
@Service
@AllArgsConstructor
public class TDictReceivableOrderServiceImpl extends ServiceImpl<TDictReceivableOrderMapper, TDictReceivableOrder> implements TDictReceivableOrderService {

    private final RemoteProcessService processService;

    private final TDictReceivableResponsibilityMapper dictReceivableResponsibilityMapper;

    private final RemoteBulletinService remoteBulletinService;

    private final static String FLOW_CODE = "receivable";


    @Override
    public R submitApply(String times) {

        List<TDictReceivableOrder> orderList = list(Wrappers.lambdaQuery(TDictReceivableOrder.class)
                .eq(TDictReceivableOrder::getStatus, 1)
                .eq(TDictReceivableOrder::getTimes, times)
        );
        //判断该月是否有流程未结束
        if (orderList.size() > 0) {
            return R.error("当前时间存在流程在流转中！！！");
        }

        //查询数据
        List<TDictReceivableResponsibility> headList = dictReceivableResponsibilityMapper.selectList(Wrappers.lambdaQuery(TDictReceivableResponsibility.class)
                .eq(TDictReceivableResponsibility::getTimes, times)
        );
        //如果不存在数据，则无法发起流程
        if (headList.size() == 0) {
            return R.error("该账期无数据，请重新选择发起时间，或联系管理员！！！");
        }

        try {
            String date = DateUtil.format(headList.get(0).getCreatedTime(), "yyyy年MM月dd日");
            String title = "DICT项目回款通报（截至日期:" + date + "）";
            TDictReceivableOrder order = new TDictReceivableOrder();
            order.setTimes(times);
            order.setStatus("0");
            order.setCreatedBy(SecurityUtils.getUser().getUsername());
            order.setCreatedTime(DateUtil.date());
            save(order);

            Map<String, Object> params = new HashMap<>(8);
            params.put("businessKey", order.getId());
            params.put("title", title);
            params.put("times", times);
            params.put("assent", order.getCreatedBy());
            //启动流程
            String instanceId = processService.startWithVariables(order.getId(), FLOW_CODE, title, params).getData().toString();
            order.setInstanceId(instanceId);
            order.setStatus("1");
            updateById(order);
        } catch (Exception ex) {
            ex.printStackTrace();
            return R.error("发起失败，请联系管理员");
        }

        return R.ok().success("流程发起成功！！！");
    }

    @Override
    public R sendsPortal(String times, String userIds) {
        JxcmccUser jxcmccUser = SecurityUtils.getUser();
        String[] strings = times.split("-");
        String title = "DICT项目回款通报(截止日期" + strings[0]+"年"+strings[1]+"月)";
        String businessId = FLOW_CODE + "_" + times;
        for (String userId : userIds.split(",")) {
            String preUnid = ToDoConfEnum.COMMON_TODO.getUnid()+ "_" + FLOW_CODE + "_" + userId + "_" + times;
            Map<String, Object> maps = new HashMap<>(8);
            maps.put("title", title);
            maps.put("times", times);
            maps.put("unid", preUnid);
            String openUrl = "&openUrl=receivableView&params=" + URLEncoder.encode(Base64.encode(JSONUtil.toJsonStr(maps), "UTF-8"));
            R<Boolean> b = remoteBulletinService.sendsEmisApproval(
                    ToDoConfEnum.COMMON_TODO.getProName(),
                    ToDoConfEnum.COMMON_TODO.getProId(),
                    preUnid,
                    businessId,
                    userId,
                    null,
                    title,
                    ToDoConfEnum.COMMON_TODO.getUnid(),
                    jxcmccUser.getRealname(),
                    jxcmccUser.getUsername(),
                    DateUtil.format(DateUtil.date(), "yyyy-MM-dd"),
                    FLOW_CODE,
                    openUrl,
                    null);
            if (!b.getData()) {
                return R.ok(false);
            }
        }
        return R.ok(true);
    }

    @Override
    public R updateEmisToDo(String unid) {
        JxcmccUser user = SecurityUtils.getUser();
        return remoteBulletinService.updateEmis(
                ToDoConfEnum.COMMON_TODO.getProName(),
                ToDoConfEnum.COMMON_TODO.getProId(),
                unid+"_"+user.getUsername(),
                user.getUsername(),
                ToDoConfEnum.COMMON_TODO.getUnid());
    }
}
