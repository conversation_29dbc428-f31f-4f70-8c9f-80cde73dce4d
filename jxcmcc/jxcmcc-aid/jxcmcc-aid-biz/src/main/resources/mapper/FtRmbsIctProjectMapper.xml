<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, jxcmcc All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: jxcmcc
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.easycloud.jxmcc.aid.dict.mapper.FtRmbsIctProjectMapper">

  <resultMap id="ftRmbsIctProjectMap" type="com.easycloud.jxmcc.aid.dict.entity.FtRmbsIctProject">
                  <id property="id" column="ID"/>
                        <result property="ictNumber" column="ICT_NUMBER"/>
                        <result property="contractNo" column="CONTRACT_NO"/>
                        <result property="contractName" column="CONTRACT_NAME"/>
                        <result property="contractUser" column="CONTRACT_USER"/>
                        <result property="contractTime" column="CONTRACT_TIME"/>
                        <result property="contractAmount" column="CONTRACT_AMOUNT"/>
                        <result property="customerNumber" column="CUSTOMER_NUMBER"/>
                        <result property="customerName" column="CUSTOMER_NAME"/>
                        <result property="customerUser" column="CUSTOMER_USER"/>
                        <result property="signTime" column="SIGN_TIME"/>
                        <result property="customerAmount" column="CUSTOMER_AMOUNT"/>
                        <result property="beforeAmount" column="BEFORE_AMOUNT"/>
                        <result property="afterAmount" column="AFTER_AMOUNT"/>
                        <result property="totelAmount" column="TOTEL_AMOUNT"/>
                        <result property="applyUserId" column="APPLY_USER_ID"/>
                        <result property="applyUserName" column="APPLY_USER_NAME"/>
                        <result property="applyDeptId" column="APPLY_DEPT_ID"/>
                        <result property="applyDeptName" column="APPLY_DEPT_NAME"/>
                        <result property="applyComId" column="APPLY_COM_ID"/>
                        <result property="applyComName" column="APPLY_COM_NAME"/>
                        <result property="isState" column="IS_STATE"/>
                        <result property="commitMessage" column="COMMIT_MESSAGE"/>
                        <result property="ictNumberName" column="ICT_NUMBER_NAME"/>
                        <result property="approvalTime" column="APPROVAL_TIME"/>
                        <result property="ictMainRevenueAmount" column="ICT_MAIN_REVENUE_AMOUNT"/>
                        <result property="ictOtherBusinessRevenueAmount" column="ICT_OTHER_BUSINESS_REVENUE_AMOUNT"/>
                        <result property="informationRevenueAmount" column="INFORMATION_REVENUE_AMOUNT"/>
                        <result property="ictMainCostAmount" column="ICT_MAIN_COST_AMOUNT"/>
                        <result property="ictOtherBusinessCostAmount" column="ICT_OTHER_BUSINESS_COST_AMOUNT"/>
                        <result property="informationCostAmount" column="INFORMATION_COST_AMOUNT"/>
                        <result property="county" column="COUNTY"/>
                        <result property="agreementTime" column="AGREEMENT_TIME"/>
                        <result property="ictType" column="ICT_TYPE"/>
                        <result property="agreementEndTime" column="AGREEMENT_END_TIME"/>
                        <result property="isStatus" column="IS_STATUS"/>
                        <result property="updateStatus" column="UPDATE_STATUS"/>
                        <result property="expireTime" column="EXPIRE_TIME"/>
                        <result property="isFiled" column="IS_FILED"/>
                        <result property="statusSign" column="STATUS_SIGN"/>
                        <result property="projectType" column="PROJECT_TYPE"/>
                        <result property="createDate" column="CREATE_DATE"/>
                        <result property="acceptanceTime" column="ACCEPTANCE_TIME"/>
                        <result property="applicationNumber" column="APPLICATION_NUMBER"/>
                        <result property="applicationName" column="APPLICATION_NAME"/>
                        <result property="isAcceptance" column="IS_ACCEPTANCE"/>
                        <result property="realAcceptanceTime" column="REAL_ACCEPTANCE_TIME"/>
                        <result property="isAcceptance1" column="IS_ACCEPTANCE1"/>
                        <result property="newIctNumber" column="NEW_ICT_NUMBER"/>
                        <result property="isImport" column="IS_IMPORT"/>
                        <result property="isImport1" column="IS_IMPORT1"/>
                        <result property="parentProjectCode" column="PARENT_PROJECT_CODE"/>
                        <result property="parentProjectName" column="PARENT_PROJECT_NAME"/>
                        <result property="isAdvanceMoney" column="IS_ADVANCE_MONEY"/>
                        <result property="advanceAmount" column="ADVANCE_AMOUNT"/>
                        <result property="isSupplement" column="IS_SUPPLEMENT"/>
                        <result property="itemId" column="ITEM_ID"/>
                        <result property="itemName" column="ITEM_NAME"/>
                        <result property="instanceId" column="INSTANCE_ID"/>
                        <result property="stateCode" column="STATE_CODE"/>
                        <result property="stateName" column="STATE_NAME"/>
                          <result property="isExternal" column="IS_EXTERNAL"/>
                          <result property="saleOppCode" column="SALE_OPP_CODE"/>
                          <result property="saleOppName" column="SALE_OPP_NAME"/>
            </resultMap>

      <select id="selectProjectList" resultType="com.easycloud.jxmcc.aid.dict.entity.FtRmbsIctProject">
            select
            id,
            ICT_NUMBER as ictNumber,
            ICT_NUMBER_NAME as ictNumberName,
            NEW_ICT_NUMBER as newIctNumber,
            CONTRACT_NO as contractNo,
            CONTRACT_NAME as contractName,
            CONTRACT_USER as contractUser,
            CONTRACT_TIME as contractTime,
            CUSTOMER_NUMBER as customerNumber,
            CUSTOMER_NAME as customerName,
            CUSTOMER_USER as customerUser,
            SIGN_TIME as signTime
            from FT_RMBS_ICT_PROJECT
            where
                IS_STATE = '1'
            AND IS_IMPORT='Y'
            AND IS_IMPORT1='Y'
            AND (EXPIRE_TIME is null or(EXPIRE_TIME is not null and SYSDATE &lt; EXPIRE_TIME))
            <if test="ftRmbsIctProjectVo.ictNumber != null and ftRmbsIctProjectVo.ictNumber != ''">
                  and ICT_NUMBER like CONCAT(CONCAT(#{ftRmbsIctProjectVo.ictNumber}, '%'), '%')
            </if>
            <if test="ftRmbsIctProjectVo.newIctNumber!=null and ftRmbsIctProjectVo.newIctNumber!=''">
                  and NEW_ICT_NUMBER like CONCAT(CONCAT(#{ftRmbsIctProjectVo.newIctNumber}, '%'), '%')
            </if>
            <if test="ftRmbsIctProjectVo.contractNo !=null and ftRmbsIctProjectVo.contractNo!=''">
                  and CONTRACT_NO like  CONCAT(CONCAT(#{ftRmbsIctProjectVo.contractNo}, '%'), '%')
            </if>

            <if test="ftRmbsIctProjectVo.customerNumber !=null and ftRmbsIctProjectVo.customerNumber!=''">
                  and CUSTOMER_NUMBER like  CONCAT(CONCAT(#{ftRmbsIctProjectVo.customerNumber}, '%'), '%')
            </if>
            <if test="ftRmbsIctProjectVo.compid !=null and ftRmbsIctProjectVo.compid!=''">
                  and (APPLY_COM_ID =#{ftRmbsIctProjectVo.compid})
            </if>

      </select>

      <select id="queryIctNoImport" resultType="com.easycloud.jxmcc.aid.dict.entity.FtRmbsIctProject">
            SELECT
            ID as id,
            NEW_ICT_NUMBER as newIctNumber,
            ICT_NUMBER as ictNumber,
            ICT_NUMBER_NAME,
            IS_IMPORT,
            IS_IMPORT1,
            CREATE_DATE
            FROM
            FT_RMBS_ICT_PROJECT
            WHERE
            (IS_IMPORT1 is null or IS_IMPORT1 = '' or IS_IMPORT1 = 'null')
            and IS_STATE='1'
            <if test="ftRmbsIctProjectVo.ictNumber!=null and ftRmbsIctProjectVo.ictNumber!=''">
                  and ICT_NUMBER  like  CONCAT(CONCAT(#{ftRmbsIctProjectVo.ictNumber}, '%'), '%')
            </if>
            <if test="ftRmbsIctProjectVo.newIctNumber!=null and ftRmbsIctProjectVo.newIctNumber!=''">
                  and NEW_ICT_NUMBER like  CONCAT(CONCAT(#{ftRmbsIctProjectVo.newIctNumber}, '%'), '%')
            </if>
        order by CREATE_DATE desc
      </select>

      <select id="getImportIctProject" resultType="com.easycloud.jxmcc.aid.dict.entity.FtRmbsIctProject">
            SELECT
                  A.*
            FROM
                  FT_RMBS_ICT_PROJECT A
                        LEFT JOIN
                  FT_RMBS_ICT_IMPORT_ESOP_LOG B
                  ON
                        A.ICT_NUMBER = B.NUM
            WHERE
                  A.IS_STATE='1'
              AND to_char(A.CREATE_DATE,'yyyy-MM-dd') <![CDATA[ > ]]> '2019-01-01'
              AND (
                        B.IMPORT_STATUS='0'
                        OR  B.IMPORT_STATUS IS NULL )
      </select>

      <select id="queryFtRmbsIctProject" resultType="com.easycloud.jxmcc.aid.dict.entity.FtRmbsIctProject">
            select * from FT_RMBS_ICT_PROJECT where is_Import is null or is_Import='' or is_Import='null'
      </select>

      <select id="queryFtRmbsIctProjectSucess" resultType="com.easycloud.jxmcc.aid.dict.entity.FtRmbsIctProject">
            select * from FT_RMBS_ICT_PROJECT where is_Import='Y' and (is_Import1 is null or is_Import1='' or is_Import1='null')
      </select>

    <select id="queryIctprojectList" resultType="com.easycloud.jxmcc.aid.dict.entity.FtRmbsIctProject">
          select
          t.ICT_NUMBER,
          t.ICT_NUMBER_NAME,
          s.CO_SEG_CODE as APPLY_COM_ID,
          s.CO_SEG as APPLY_COM_NAME
          from  T_RMBS_ICT_PROJECT t ,T_Cmcc_COMSEGCODE s
          where t.APPLY_COM_ID = s.COM_ID and t.IS_STATE = '1'
          <if test="param.approvalTimeBegin!=null and param.approvalTimeBegin!=''">
                and t.APPROVAL_TIME <![CDATA[ >= ]]> #{param.approvalTimeBegin}
          </if>
          <if test="param.approvalTimeEnd!=null and param.approvalTimeEnd!=''">
                and t.APPROVAL_TIME <![CDATA[ <= ]]> #{param.approvalTimeEnd}
          </if>

          <if test="param.itcNumber!=null and param.itcNumber!=''">
                and t.ICT_NUMBER = #{param.itcNumber}
          </if>
        </select>

      <select id="findIctProjectList1" resultType="com.easycloud.jxmcc.aid.dict.entity.FtRmbsIctProject">
            select * from FT_RMBS_ICT_PROJECT where is_Import is null or is_Import='' or is_Import='null'
      </select>

      <select id="findIctProjectList2" resultType="com.easycloud.jxmcc.aid.dict.entity.FtRmbsIctProject">
            select * from FT_RMBS_ICT_PROJECT where is_Import='Y' and (is_Import1 is null or is_Import1='' or is_Import1='null')
      </select>

      <select id="selectSeq" resultType="int">
            select SEQ_FT_RMBS_ICT_PROJECT.nextval from dual
      </select>


      <select id="getIctProjectAmount" resultType="com.easycloud.jxmcc.aid.dict.vo.IctProjectAmountVo">
            SELECT
                  A.PROJECT_CODE,
                  A.ictMainRevenueAmount,
                  B.ictOtherBusinessRevenueAmount,
                  C.informationRevenueAmount,
                  D.ictMainCostAmount,
                  E.ictOtherBusinessCostAmount,
                  F.informationCostAmount,
                  G.advanceAmount
            FROM
                  (
                        SELECT
                              PROJECT_CODE ,
                              NVL(SUM(TO_NUMBER(CASE WHEN AMOUNT IS NULL THEN '0' WHEN AMOUNT='' THEN '0' ELSE AMOUNT END)),0) AS ictMainRevenueAmount
                        FROM
                              FT_ICT_PROJECT_FINANCIAL_DATA_TITLE
                        WHERE
                              PROJECT_CODE=#{ictNumber}
                          AND ROW_NUM IN ('8',
                                          '9',
                                          '10',
                                          '12',
                                          '13',
                                          '14'
                              )
                        GROUP BY
                              PROJECT_CODE ) A
                        LEFT JOIN
                  (
                        SELECT
                              PROJECT_CODE,
                              NVL(SUM(TO_NUMBER(CASE WHEN AMOUNT IS NULL THEN '0' WHEN AMOUNT='' THEN '0' ELSE AMOUNT END)),0) AS ictOtherBusinessRevenueAmount
                        FROM
                              FT_ICT_PROJECT_FINANCIAL_DATA_TITLE
                        WHERE
                              PROJECT_CODE=#{ictNumber}
                          AND ROW_NUM IN ('11'
                              )
                        GROUP BY
                              PROJECT_CODE) B
                  ON
                        A.PROJECT_CODE=B.PROJECT_CODE
                        LEFT JOIN
                  (
                        SELECT
                              PROJECT_CODE,
                              NVL(SUM(TO_NUMBER(CASE WHEN AMOUNT IS NULL THEN '0' WHEN AMOUNT='' THEN '0' ELSE AMOUNT END)),0) AS informationRevenueAmount
                        FROM
                              FT_ICT_PROJECT_FINANCIAL_DATA_TITLE
                        WHERE
                              PROJECT_CODE=#{ictNumber}
                          AND ROW_NUM IN ('8',
                                          '9',
                                          '10')
                        GROUP BY
                              PROJECT_CODE) C
                  ON
                        A.PROJECT_CODE=C.PROJECT_CODE
                        LEFT JOIN
                  (
                        SELECT
                              PROJECT_CODE,
                              NVL(SUM(TO_NUMBER(CASE WHEN AMOUNT IS NULL THEN '0' WHEN AMOUNT='' THEN '0' ELSE AMOUNT END)),0) AS ictMainCostAmount
                        FROM
                              FT_ICT_PROJECT_FINANCIAL_DATA_TITLE
                        WHERE
                              PROJECT_CODE=#{ictNumber}
                          AND ROW_NUM IN ('23',
                                          '24',
                                          '25',
                                          '27',
                                          '28',
                                          '29'
                              )
                        GROUP BY
                              PROJECT_CODE) D
                  ON
                        A.PROJECT_CODE=D.PROJECT_CODE
                        LEFT JOIN
                  (
                        SELECT
                              PROJECT_CODE,
                              NVL(SUM(TO_NUMBER(CASE WHEN AMOUNT IS NULL THEN '0' WHEN AMOUNT='' THEN '0' ELSE AMOUNT END)),0) AS ictOtherBusinessCostAmount
                        FROM
                              FT_ICT_PROJECT_FINANCIAL_DATA_TITLE
                        WHERE
                              PROJECT_CODE=#{ictNumber}
                          AND ROW_NUM IN ('26')
                        GROUP BY
                              PROJECT_CODE) E
                  ON
                        A.PROJECT_CODE=E.PROJECT_CODE
                        LEFT JOIN
                  (
                        SELECT
                              PROJECT_CODE,
                              NVL(SUM(TO_NUMBER(CASE WHEN AMOUNT IS NULL THEN '0' WHEN AMOUNT='' THEN '0' ELSE AMOUNT END)),0) AS informationCostAmount
                        FROM
                              FT_ICT_PROJECT_FINANCIAL_DATA_TITLE
                        WHERE
                              PROJECT_CODE=#{ictNumber}
                          AND ROW_NUM IN ('23',
                                          '24',
                                          '25')
                        GROUP BY
                              PROJECT_CODE) F
                  ON
                        A.PROJECT_CODE=F.PROJECT_CODE
                        LEFT JOIN
                  (
                        SELECT
                              PROJECT_CODE,
                              MAX(NVL(AMOUNT,0)) AS advanceAmount
                        FROM
                              FT_ICT_PROJECT_FINANCIAL_DATA_DETAIL
                        WHERE
                              PROJECT_CODE=#{ictNumber}
                          AND ROW_NUM IN ('31')
                        GROUP BY
                              PROJECT_CODE) G
                  ON
                        A.PROJECT_CODE=G.PROJECT_CODE
      </select>

      <select id="valiIctProject" resultType="com.easycloud.jxmcc.aid.dict.entity.FtRmbsIctProject">
            select *

            from FT_RMBS_ICT_PROJECT

            where
               CONTRACT_NO =#{tRmbsIctProject.contractNo}
              and ID <![CDATA[<>]]>  #{tRmbsIctProject.id}
      </select>
</mapper>
