<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, jxcmcc All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: jxcmcc
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.easycloud.jxmcc.aid.receivable.mapper.TDictReceivableOrderMapper">

  <resultMap id="tDictReceivableOrderMap" type="com.easycloud.jxmcc.aid.receivable.entity.TDictReceivableOrder">
                  <id property="id" column="ID"/>
                        <result property="times" column="TIMES"/>
                        <result property="instanceId" column="INSTANCE_ID"/>
                        <result property="status" column="STATUS"/>
                        <result property="createdBy" column="CREATED_BY"/>
                        <result property="createdTime" column="CREATED_TIME"/>
                        <result property="updatedBy" column="UPDATED_BY"/>
                        <result property="updatedTime" column="UPDATED_TIME"/>
            </resultMap>
</mapper>
