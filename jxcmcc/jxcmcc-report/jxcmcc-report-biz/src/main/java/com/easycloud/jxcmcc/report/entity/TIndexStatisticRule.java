/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxcmcc.report.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 指标统计规则
 *
 * <AUTHOR> code generator
 * @date 2022-11-16 17:45:15
 */
@Data
@TableName("T_INDEX_STATISTIC_RULE")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "指标统计规则")
public class TIndexStatisticRule extends Model<TIndexStatisticRule> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    @Excel(name = "主键ID")
    private String id;
    /**
     * 业务编码
     */
    @ApiModelProperty(value = "业务编码")
    @Excel(name = "业务编码")
    private String businessCode;
    /**
     * 统计类型
     */
    @ApiModelProperty(value = "统计类型")
    @Excel(name = "统计类型")
    private String statisticType;
    /**
     * 统计类型名称
     */
    @ApiModelProperty(value = "统计类型名称")
    @Excel(name = "统计类型名称")
    private String statisticTypeName;
    /**
     * 规则策略
     */
    @ApiModelProperty(value = "规则策略")
    @Excel(name = "规则策略")
    private String ruleStrategy;
    /**
     * 规则描述
     */
    @ApiModelProperty(value = "规则描述")
    @Excel(name = "规则描述")
    private String ruleDesc;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @Excel(name = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @Excel(name = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @Excel(name = "更新人")
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @Excel(name = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedTime;
    /**
     * 删除标识;0-正常 1-删除
     */
    @ApiModelProperty(value = "删除标识;0-正常 1-删除")
    @Excel(name = "删除标识;0-正常 1-删除")
    @TableLogic
    private Integer delFlag;
}
