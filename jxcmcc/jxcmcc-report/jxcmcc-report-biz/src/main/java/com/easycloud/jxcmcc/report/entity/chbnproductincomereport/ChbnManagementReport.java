/*
 *    Copyright (c) 2018-2025, jxcmcc All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jxcmcc
 */

package com.easycloud.jxcmcc.report.entity.chbnproductincomereport;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 管会报表
 *
 * <AUTHOR> code generator
 * @date 2024-10-11 16:25:43
 */
@Data
@TableName("CHBN_MANAGEMENT_REPORT")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "管会报表")
public class ChbnManagementReport extends Model<ChbnManagementReport> {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private String id;
    /**
     * 账期
     */
    @ApiModelProperty(value = "账期")
    @Excel(name = "账期", width = 15)
    private String period;
    /**
     * 地区(1为国内，2为香港)
     */
    @ApiModelProperty(value = "报表类型(1为国内，2为香港)")
    @Excel(name = "报表类型", width = 25)
    private String region;

    /**
     * 类型(1为个人口径，2为家庭口径，3为政企口径)
     */
    @ApiModelProperty(value = "口径类型(1为个人口径，2为家庭口径，3为政企口径)")
    @Excel(name = "类型", width = 25)
    private String type;
    /**
     * 地市编码
     */
    @ApiModelProperty(value = "地市编码")
    private String companyCode;
    /**
     * 地市名称
     */
    @ApiModelProperty(value = "地市名称")
    @Excel(name = "地市名称", width = 20)
    private String companyName;
    /**
     * 一级分类
     */
    @ApiModelProperty(value = "一级分类")
    @Excel(name = "一级分类", width = 25)
    private String firstStage;
    /**
     * 二级分类
     */
    @ApiModelProperty(value = "二级分类")
    @Excel(name = "二级分类", width = 25)
    private String secondStage;
    /**
     * 三级分类
     */
    @ApiModelProperty(value = "三级分类")
    @Excel(name = "三级分类", width = 25)
    private String thirdStage;
    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @Excel(name = "产品编码", width = 15)
    private String productCode;
    /**
     * 其他收入-本月数
     */
    @ApiModelProperty(value = "其他收入-本月数")
    private BigDecimal otherMonthTotal = BigDecimal.ZERO;
    /**
     * 其他收入-本年累计数
     */
    @ApiModelProperty(value = "其他收入-本年累计数")
    private BigDecimal otherYearTotal = BigDecimal.ZERO;
    /**
     * 合计数-本月数
     */
    @ApiModelProperty(value = "合计数-本月数")
    @Excel(name = "合计数-本月数", type = 10, width = 20)
    private BigDecimal monthTotal = BigDecimal.ZERO;
    /**
     * 合计数-本年累计数
     */
    @ApiModelProperty(value = "合计数-本年累计数")
    @Excel(name = "合计数-本年累计数", type = 10, width = 20)
    private BigDecimal yearTotal = BigDecimal.ZERO;
}
